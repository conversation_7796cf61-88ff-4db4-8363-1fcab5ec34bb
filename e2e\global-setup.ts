import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for Ottiq e2e tests...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(config.projects[0].use.baseURL || 'http://localhost:3000');
    
    // Wait for the main content to load
    await page.waitForSelector('body', { timeout: 30000 });
    
    // Check if the app is in a healthy state
    const title = await page.title();
    if (!title.includes('Ottiq')) {
      throw new Error('Application does not appear to be running correctly');
    }

    console.log('✅ Application is ready for testing');

    // Seed test data if needed
    console.log('🌱 Seeding test data...');
    
    // You can add API calls here to seed test data
    // Example:
    // await page.request.post('/api/test/seed', {
    //   data: { testProducts: true, testUsers: true }
    // });

    console.log('✅ Global setup completed successfully');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup;
