# Email Notifications System

## Overview

The Ottiq email notification system provides branded, transactional emails that keep excitement alive after user sessions. The system is built with emotional, lifestyle-focused copy and responsive Tailwind CSS templates.

## Features

- 🎨 **Branded Templates**: Beautiful, responsive email templates with Ottiq branding
- 📱 **Mobile-First Design**: Optimized for mobile viewing with Tailwind CSS
- 🚀 **Automated Triggers**: Automatic email sending based on user actions
- 🔒 **Secure & Reliable**: Built-in error handling and retry mechanisms
- 📊 **Testing & Validation**: Comprehensive testing tools and validation scripts
- 📈 **Bulk Email Support**: Support for newsletters and announcements

## Email Types

### Order-Related Emails
- **Order Confirmation**: Celebrates order placement with emotional copy
- **Order Shipped**: Builds excitement with tracking information
- **Order Delivered**: Encourages engagement and reviews
- **Order Cancelled**: Handles cancellations with empathy

### Payment Emails
- **Payment Confirmation**: Confirms successful payment processing
- **Payment Failed**: Provides helpful guidance for payment issues

### User Experience Emails
- **Welcome Email**: Introduces new users to the platform
- **AI Try-On Ready**: Notifies when AI try-on results are available
- **Password Reset**: Secure password reset functionality

## Architecture

### Core Components

```
src/
├── lib/services/email.ts          # Main email service
├── emails/
│   ├── components/
│   │   └── EmailLayout.tsx        # Base layout component
│   └── templates/
│       ├── OrderConfirmationEmail.tsx
│       ├── OrderShippedEmail.tsx
│       ├── WelcomeEmail.tsx
│       └── ... (other templates)
├── app/api/emails/
│   ├── send/route.ts              # Main email API
│   ├── test/route.ts              # Testing API (admin only)
│   └── bulk/route.ts              # Bulk email API (admin only)
└── __tests__/
    ├── services/email.test.ts     # Unit tests
    └── api/emails.test.ts         # API integration tests
```

### Email Service (`src/lib/services/email.ts`)

The core email service provides:
- SMTP configuration and connection management
- Template rendering with React Email
- Error handling and retry logic
- Bulk email functionality
- Email validation and sanitization

## Configuration

### Environment Variables

```bash
# SMTP Configuration
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>
SMTP_SECURE=false

# Application URL for email links
APP_URL=http://localhost:3000

# Admin emails for access control
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### Development Setup

For development, the system is configured to use Mailhog for email testing:

```yaml
# docker-compose.yml
mailhog:
  image: mailhog/mailhog:latest
  ports:
    - "1025:1025"  # SMTP
    - "8025:8025"  # Web UI
```

## Usage

### Automatic Email Triggers

Emails are automatically sent when:

1. **User Registration**: Welcome email via NextAuth events
2. **Order Creation**: Order confirmation email
3. **Order Status Updates**: Shipped/delivered emails
4. **AI Try-On Completion**: Ready notification email
5. **Payment Events**: Confirmation/failure emails

### Manual Email Sending

#### Via API (Admin Only)

```typescript
// Send single email
const response = await fetch('/api/emails/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: 'welcome',
    data: {
      to: '<EMAIL>',
      customerName: 'John Doe',
    },
  }),
});

// Send bulk emails
const response = await fetch('/api/emails/bulk', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    group: 'all-users',
    type: 'welcome',
    options: {
      batchSize: 10,
      delayBetweenBatches: 1000,
    },
  }),
});
```

#### Via Email Service

```typescript
import { emailService } from '@/lib/services/email';

const result = await emailService.sendEmail('welcome', {
  to: '<EMAIL>',
  customerName: 'John Doe',
});

if (result.success) {
  console.log('Email sent:', result.messageId);
} else {
  console.error('Email failed:', result.error);
}
```

## Testing

### Running Tests

```bash
# Unit tests
npm test

# Email service tests
npm run email:test

# Template validation
npm run email:validate

# Generate email previews
npm run email:preview
```

### Test Email Sending

```bash
# Send test emails to specific address
TEST_EMAIL=<EMAIL> npm run email:test

# Skip actual sending (template validation only)
SKIP_EMAIL_SENDING=true npm run email:test
```

### Admin Testing Interface

Access the admin testing interface at `/api/emails/test` (admin authentication required):

```bash
# Get available test types
curl -X GET /api/emails/test

# Send test email
curl -X POST /api/emails/test \
  -H "Content-Type: application/json" \
  -d '{
    "type": "welcome",
    "recipient": "<EMAIL>",
    "useTestData": true
  }'
```

## Email Templates

### Template Structure

All email templates follow a consistent structure:

```typescript
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';

export function WelcomeEmail({ customerName }: WelcomeEmailProps) {
  return (
    <EmailLayout previewText="Welcome to Ottiq!">
      <EmailHeading level={1}>
        Welcome, {customerName}!
      </EmailHeading>
      
      <EmailText>
        Your journey to unique style starts here.
      </EmailText>
      
      <EmailButton href="/create" variant="primary">
        Start Designing
      </EmailButton>
    </EmailLayout>
  );
}
```

### Styling Guidelines

- Use Tailwind CSS classes for styling
- Follow mobile-first responsive design
- Maintain consistent brand colors and typography
- Include lifestyle imagery and emotional copy
- Ensure accessibility with proper contrast and alt text

## Monitoring & Debugging

### Logging

The email service provides comprehensive logging:

```typescript
// Successful sends
console.log('Email sent successfully:', {
  type: 'welcome',
  to: '<EMAIL>',
  messageId: 'abc123',
});

// Failures
console.error('Email sending failed:', {
  type: 'welcome',
  to: '<EMAIL>',
  error: 'SMTP connection failed',
});
```

### Health Checks

Check email service status:

```bash
curl /api/emails/send
```

Response:
```json
{
  "success": true,
  "status": {
    "available": true,
    "connectionValid": true,
    "supportedTypes": ["welcome", "order-confirmation", ...]
  }
}
```

## Security

### Access Control

- Admin-only email types require authentication
- API endpoints validate admin permissions
- Rate limiting prevents abuse

### Data Protection

- Email addresses are validated before sending
- Sensitive data is not logged
- SMTP credentials are environment-protected

## Troubleshooting

### Common Issues

1. **Email service unavailable**
   - Check SMTP configuration
   - Verify environment variables
   - Test SMTP connection

2. **Templates not rendering**
   - Run template validation script
   - Check for missing dependencies
   - Verify template imports

3. **Emails not being sent**
   - Check email service logs
   - Verify SMTP credentials
   - Test with Mailhog in development

### Debug Commands

```bash
# Validate email service configuration
npm run validate:env

# Test email templates
npm run email:validate

# Test email service connection
npm run email:test
```

## Production Deployment

### SMTP Configuration

For production, configure a reliable SMTP service:

```bash
# Example with SendGrid
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASSWORD=your-sendgrid-api-key
SMTP_FROM=<EMAIL>
SMTP_SECURE=true
```

### Monitoring

- Set up email delivery monitoring
- Configure alerts for failed sends
- Monitor email open/click rates
- Track bounce and complaint rates

## Future Enhancements

- Email analytics and tracking
- A/B testing for email templates
- Advanced personalization
- Email scheduling and campaigns
- Integration with marketing automation tools
