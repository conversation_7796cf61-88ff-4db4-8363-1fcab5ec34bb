import { NextRequest, NextResponse } from 'next/server';
import { PricingEngine } from '@/lib/pricing/engine';
import { PricingRequest } from '@/types/pricing';
import { z } from 'zod';
import { createSecureApiHandler, CommonSchemas } from '@/lib/middleware/validation';

// Request validation schema
const PricingRequestSchema = z.object({
  productId: CommonSchemas.id,
  variantId: CommonSchemas.id.optional(),
  customizationId: CommonSchemas.id.optional(),
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(100, 'Quantity cannot exceed 100'),
  fabricId: CommonSchemas.id.optional(),
  printSize: z.enum(['small', 'medium', 'large', 'full_coverage']).optional(),
  qualityTier: z.enum(['standard', 'premium', 'luxury']).optional(),
  userId: CommonSchemas.id.optional()
});

// Secure POST handler with comprehensive middleware
const securePostHandler = createSecureApiHandler(
  async (request: NextRequest) => {
    const { body } = (request as any).validatedData;
    const pricingRequest: PricingRequest = body;

    // Calculate pricing using the engine
    const pricingEngine = PricingEngine.getInstance();
    const result = await pricingEngine.calculatePrice(pricingRequest);

    // Log pricing calculation for analytics
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    console.log('Pricing calculation:', {
      productId: pricingRequest.productId,
      quantity: pricingRequest.quantity,
      totalPrice: result.data?.totalPrice,
      timestamp: new Date().toISOString(),
      clientIP
    });

    // Return response with appropriate status code
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  },
  {
    validation: {
      body: PricingRequestSchema,
      maxBodySize: 1024 * 1024, // 1MB max
    },
    rateLimit: 'pricing',
  }
);

export const POST = securePostHandler;

// Query validation schema for GET requests
const GetQuerySchema = z.object({
  config: z.enum(['print-sizes', 'quality-tiers']).optional(),
});

// Secure GET handler for admin configuration
const secureGetHandler = createSecureApiHandler(
  async (request: NextRequest) => {
    const { query } = (request as any).validatedData;
    const configType = query?.config;

    const pricingEngine = PricingEngine.getInstance();

    switch (configType) {
      case 'print-sizes':
        return NextResponse.json({
          success: true,
          data: pricingEngine.getPrintSizeConfig()
        });

      case 'quality-tiers':
        return NextResponse.json({
          success: true,
          data: pricingEngine.getQualityTierConfig()
        });

      default:
        return NextResponse.json({
          success: true,
          data: {
            printSizes: pricingEngine.getPrintSizeConfig(),
            qualityTiers: pricingEngine.getQualityTierConfig()
          }
        });
    }
  },
  {
    validation: {
      query: GetQuerySchema,
    },
    rateLimit: 'admin',
    requireAdmin: true,
  }
);

export const GET = secureGetHandler;
