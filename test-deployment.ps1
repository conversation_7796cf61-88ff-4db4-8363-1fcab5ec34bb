# Test Deployment Configuration Script
# This script validates the deployment setup without actually running Docker

param(
    [switch]$Verbose = $false
)

$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput($ForegroundColor, $Message) {
    Write-Host $Message -ForegroundColor $ForegroundColor
}

function Test-FileExists($Path, $Description) {
    if (Test-Path $Path) {
        Write-ColorOutput $Green "✓ $Description exists: $Path"
        return $true
    } else {
        Write-ColorOutput $Red "✗ $Description missing: $Path"
        return $false
    }
}

function Test-DockerFiles {
    Write-ColorOutput $Blue "Testing Docker configuration files..."
    
    $allGood = $true
    
    # Test core Docker files
    $allGood = (Test-FileExists "Dockerfile" "Dockerfile") -and $allGood
    $allGood = (Test-FileExists "docker-compose.prod.yml" "Production Docker Compose") -and $allGood
    $allGood = (Test-FileExists ".dockerignore" "Docker ignore file") -and $allGood
    $allGood = (Test-FileExists "healthcheck.js" "Health check script") -and $allGood
    
    # Test environment files
    $allGood = (Test-FileExists ".env.docker" "Docker environment template") -and $allGood
    
    # Test Nginx configuration
    $allGood = (Test-FileExists "nginx/nginx.conf" "Nginx main configuration") -and $allGood
    $allGood = (Test-FileExists "nginx/conf.d/default.conf" "Nginx server configuration") -and $allGood
    
    # Test deployment scripts
    $allGood = (Test-FileExists "deploy.ps1" "PowerShell deployment script") -and $allGood
    $allGood = (Test-FileExists "deploy.sh" "Bash deployment script") -and $allGood
    
    # Test documentation
    $allGood = (Test-FileExists "DEPLOYMENT.md" "Deployment documentation") -and $allGood
    
    return $allGood
}

function Test-ProjectStructure {
    Write-ColorOutput $Blue "Testing project structure..."
    
    $allGood = $true
    
    # Test core project files
    $allGood = (Test-FileExists "package.json" "Package.json") -and $allGood
    $allGood = (Test-FileExists "next.config.js" "Next.js configuration") -and $allGood
    $allGood = (Test-FileExists "prisma/schema.prisma" "Prisma schema") -and $allGood
    $allGood = (Test-FileExists "src/app/api/health/route.ts" "Health check API") -and $allGood
    
    return $allGood
}

function Test-DockerComposeStructure {
    Write-ColorOutput $Blue "Testing Docker Compose configuration..."
    
    if (-not (Test-Path "docker-compose.prod.yml")) {
        Write-ColorOutput $Red "✗ Docker Compose file not found"
        return $false
    }
    
    $composeContent = Get-Content "docker-compose.prod.yml" -Raw
    
    $services = @("app", "postgres", "redis", "minio", "nginx", "mailhog")
    $allGood = $true
    
    foreach ($service in $services) {
        if ($composeContent -match "^\s*$service\s*:" -or $composeContent -match "\n\s*$service\s*:") {
            Write-ColorOutput $Green "✓ Service '$service' configured"
        } else {
            Write-ColorOutput $Red "✗ Service '$service' not found in docker-compose.prod.yml"
            $allGood = $false
        }
    }
    
    # Test for healthchecks
    if ($composeContent -match "healthcheck:") {
        Write-ColorOutput $Green "✓ Health checks configured"
    } else {
        Write-ColorOutput $Yellow "⚠ No health checks found"
    }
    
    # Test for networks
    if ($composeContent -match "networks:") {
        Write-ColorOutput $Green "✓ Networks configured"
    } else {
        Write-ColorOutput $Red "✗ No networks configured"
        $allGood = $false
    }
    
    # Test for volumes
    if ($composeContent -match "volumes:") {
        Write-ColorOutput $Green "✓ Volumes configured"
    } else {
        Write-ColorOutput $Red "✗ No volumes configured"
        $allGood = $false
    }
    
    return $allGood
}

function Test-EnvironmentConfiguration {
    Write-ColorOutput $Blue "Testing environment configuration..."

    if (-not (Test-Path ".env.docker")) {
        Write-ColorOutput $Red "✗ .env.docker template not found"
        return $false
    }

    $envContent = Get-Content ".env.docker" -Raw

    $requiredVars = @(
        "DATABASE_URL",
        "NEXTAUTH_SECRET",
        "REDIS_URL",
        "MINIO_ENDPOINT",
        "NODE_ENV"
    )

    $allGood = $true

    foreach ($var in $requiredVars) {
        if ($envContent -match "^$var=") {
            Write-ColorOutput $Green "✓ Environment variable '$var' configured"
        } else {
            Write-ColorOutput $Red "✗ Environment variable '$var' not found"
            $allGood = $false
        }
    }

    return $allGood
}

function Show-DeploymentInstructions {
    Write-ColorOutput $Blue "Deployment Instructions:"
    Write-Host ""
    Write-Host "1. Start Docker Desktop"
    Write-Host "2. Copy environment configuration:"
    Write-ColorOutput $Yellow "   Copy-Item .env.docker .env.production"
    Write-Host "3. Edit .env.production with your actual values"
    Write-Host "4. Deploy using PowerShell:"
    Write-ColorOutput $Yellow "   .\deploy.ps1"
    Write-Host "5. Or deploy manually:"
    Write-ColorOutput $Yellow "   docker-compose -f docker-compose.prod.yml up -d --build"
    Write-Host ""
    Write-Host "Service URLs after deployment:"
    Write-Host "  Application:    http://localhost"
    Write-Host "  MinIO Console:  http://localhost:9001"
    Write-Host "  Mailhog UI:     http://localhost:8025"
}

# Main execution
Write-ColorOutput $Blue "Ottiq Docker Deployment Configuration Test"
Write-Host ""

$dockerFilesOk = Test-DockerFiles
Write-Host ""

$projectStructureOk = Test-ProjectStructure
Write-Host ""

$composeStructureOk = Test-DockerComposeStructure
Write-Host ""

$envConfigOk = Test-EnvironmentConfiguration
Write-Host ""

if ($dockerFilesOk -and $projectStructureOk -and $composeStructureOk -and $envConfigOk) {
    Write-ColorOutput $Green "✓ All deployment configuration tests passed!"
    Write-Host ""
    Show-DeploymentInstructions
} else {
    Write-ColorOutput $Red "✗ Some deployment configuration tests failed!"
    Write-Host "Please fix the issues above before deploying."
}

Write-Host ""
Write-ColorOutput $Blue "Note: Docker Desktop must be running to actually deploy the application."
