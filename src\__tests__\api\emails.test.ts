/**
 * Email API Integration Tests
 * Tests for email API endpoints
 */

import { NextRequest } from 'next/server';
import { POST as sendEmailPOST, GET as sendEmailGET } from '../../app/api/emails/send/route';
import { POST as testEmailPOST, GET as testEmailGET } from '../../app/api/emails/test/route';

// Mock dependencies
jest.mock('../../lib/services/email', () => ({
  emailService: {
    isAvailable: jest.fn().mockReturnValue(true),
    verifyConnection: jest.fn().mockResolvedValue(true),
    sendEmail: jest.fn().mockResolvedValue({
      success: true,
      messageId: 'test-message-id-123',
    }),
    sendBulkEmails: jest.fn().mockResolvedValue({
      success: true,
      sent: 2,
      failed: 0,
      errors: [],
    }),
  },
}));

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('../../app/api/auth/[...nextauth]/route', () => ({
  authOptions: {},
}));

const { getServerSession } = require('next-auth');

describe('/api/emails/send', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/emails/send', () => {
    test('should send email successfully for admin user', async () => {
      // Mock admin session
      getServerSession.mockResolvedValue({
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
        },
      });

      // Mock admin emails environment variable
      process.env.ADMIN_EMAILS = '<EMAIL>';

      const requestBody = {
        type: 'order-confirmation',
        data: {
          to: '<EMAIL>',
          customerName: 'John Doe',
          orderNumber: 'ORD-123456',
          orderDate: '2024-01-15',
          totalAmount: '2500.00',
          currency: 'BDT',
          items: [
            {
              name: 'Custom T-Shirt',
              quantity: 1,
              price: '1500.00',
            },
          ],
          shippingAddress: {
            name: 'John Doe',
            address: '123 Test Street',
            city: 'Dhaka',
            postalCode: '1000',
            country: 'Bangladesh',
          },
          orderUrl: 'http://localhost:3000/orders/123',
        },
      };

      const request = new NextRequest('http://localhost:3000/api/emails/send', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await sendEmailPOST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.messageId).toBe('test-message-id-123');
    });

    test('should reject request from non-admin user for admin-only email types', async () => {
      // Mock regular user session
      getServerSession.mockResolvedValue({
        user: {
          id: 'user-123',
          email: '<EMAIL>',
        },
      });

      process.env.ADMIN_EMAILS = '<EMAIL>';

      const requestBody = {
        type: 'order-confirmation',
        data: {
          to: '<EMAIL>',
          customerName: 'John Doe',
          orderNumber: 'ORD-123456',
          orderDate: '2024-01-15',
          totalAmount: '2500.00',
          currency: 'BDT',
          items: [],
          shippingAddress: {
            name: 'John Doe',
            address: '123 Test Street',
            city: 'Dhaka',
            postalCode: '1000',
            country: 'Bangladesh',
          },
          orderUrl: 'http://localhost:3000/orders/123',
        },
      };

      const request = new NextRequest('http://localhost:3000/api/emails/send', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await sendEmailPOST(request);
      const responseData = await response.json();

      expect(response.status).toBe(403);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('ADMIN_ACCESS_REQUIRED');
    });

    test('should reject request with invalid email data', async () => {
      getServerSession.mockResolvedValue({
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
        },
      });

      process.env.ADMIN_EMAILS = '<EMAIL>';

      const requestBody = {
        type: 'order-confirmation',
        data: {
          to: 'invalid-email',
          customerName: 'John Doe',
          // Missing required fields
        },
      };

      const request = new NextRequest('http://localhost:3000/api/emails/send', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await sendEmailPOST(request);
      const responseData = await response.json();

      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('INVALID_EMAIL_DATA');
    });

    test('should handle email service unavailable', async () => {
      const { emailService } = require('../../lib/services/email');
      emailService.isAvailable.mockReturnValue(false);

      getServerSession.mockResolvedValue({
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
        },
      });

      process.env.ADMIN_EMAILS = '<EMAIL>';

      const requestBody = {
        type: 'welcome',
        data: {
          to: '<EMAIL>',
          customerName: 'John Doe',
        },
      };

      const request = new NextRequest('http://localhost:3000/api/emails/send', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await sendEmailPOST(request);
      const responseData = await response.json();

      expect(response.status).toBe(503);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('EMAIL_SERVICE_UNAVAILABLE');
    });
  });

  describe('GET /api/emails/send', () => {
    test('should return email service status', async () => {
      const request = new NextRequest('http://localhost:3000/api/emails/send', {
        method: 'GET',
      });

      const response = await sendEmailGET();
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.status).toBeDefined();
      expect(responseData.status.available).toBe(true);
      expect(responseData.status.connectionValid).toBe(true);
      expect(responseData.status.supportedTypes).toBeInstanceOf(Array);
    });
  });
});

describe('/api/emails/test', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/emails/test', () => {
    test('should send test email for admin user', async () => {
      getServerSession.mockResolvedValue({
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
        },
      });

      process.env.ADMIN_EMAILS = '<EMAIL>';

      const requestBody = {
        type: 'welcome',
        recipient: '<EMAIL>',
        useTestData: true,
      };

      const request = new NextRequest('http://localhost:3000/api/emails/test', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await testEmailPOST(request);
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.messageId).toBe('test-message-id-123');
      expect(responseData.type).toBe('welcome');
      expect(responseData.recipient).toBe('<EMAIL>');
    });

    test('should reject test email request from non-admin user', async () => {
      getServerSession.mockResolvedValue({
        user: {
          id: 'user-123',
          email: '<EMAIL>',
        },
      });

      process.env.ADMIN_EMAILS = '<EMAIL>';

      const requestBody = {
        type: 'welcome',
        recipient: '<EMAIL>',
        useTestData: true,
      };

      const request = new NextRequest('http://localhost:3000/api/emails/test', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await testEmailPOST(request);
      const responseData = await response.json();

      expect(response.status).toBe(403);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('ADMIN_ACCESS_REQUIRED');
    });
  });

  describe('GET /api/emails/test', () => {
    test('should return available test email types for admin', async () => {
      getServerSession.mockResolvedValue({
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
        },
      });

      process.env.ADMIN_EMAILS = '<EMAIL>';

      const response = await testEmailGET();
      const responseData = await response.json();

      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.availableTypes).toBeInstanceOf(Array);
      expect(responseData.emailServiceStatus).toBeDefined();
    });
  });
});
