'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { formatDistanceToNow, format } from 'date-fns';

interface SupportTicket {
  id: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  customerName: string;
  customerEmail: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  messages: Array<{
    id: string;
    content: string;
    messageType: string;
    senderRole: string;
    createdAt: string;
    sender: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  _count: {
    messages: number;
  };
}

interface AdminSupportTicketCardProps {
  ticket: SupportTicket;
  onUpdate: () => void;
  getStatusColor: (status: string) => string;
  getPriorityColor: (priority: string) => string;
}

export function AdminSupportTicketCard({ 
  ticket, 
  onUpdate, 
  getStatusColor, 
  getPriorityColor 
}: AdminSupportTicketCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [updating, setUpdating] = useState(false);

  const handleViewTicket = () => {
    window.location.href = `/admin/support/tickets/${ticket.id}`;
  };

  const handleQuickStatusUpdate = async (newStatus: string) => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticket.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        onUpdate();
      } else {
        throw new Error('Failed to update status');
      }
    } catch (error) {
      console.error('Error updating ticket status:', error);
      alert('Failed to update ticket status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return '🔵';
      case 'IN_PROGRESS':
        return '🟡';
      case 'WAITING_FOR_CUSTOMER':
        return '🟠';
      case 'WAITING_FOR_ADMIN':
        return '🟣';
      case 'RESOLVED':
        return '✅';
      case 'CLOSED':
        return '⚫';
      default:
        return '⚪';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'order':
        return '📦';
      case 'product':
        return '👕';
      case 'technical':
        return '🔧';
      case 'billing':
        return '💳';
      case 'general':
        return '💬';
      default:
        return '❓';
    }
  };

  const latestMessage = ticket.messages[0];
  const hasUnreadAdminMessages = latestMessage && latestMessage.senderRole === 'CUSTOMER';
  const isUrgent = ticket.priority === 'URGENT' || ticket.priority === 'HIGH';

  return (
    <Card className={`hover:shadow-lg transition-all duration-200 ${
      hasUnreadAdminMessages ? 'ring-2 ring-orange-500' : ''
    } ${isUrgent ? 'border-l-4 border-l-red-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">{getCategoryIcon(ticket.category)}</span>
              <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-1">
                {ticket.subject}
              </CardTitle>
              {hasUnreadAdminMessages && (
                <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                  Customer Reply
                </span>
              )}
              {isUrgent && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {ticket.priority}
                </span>
              )}
            </div>
            
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="font-mono">#{ticket.id.slice(-8)}</span>
              <span>•</span>
              <span className="font-medium">{ticket.customerName}</span>
              <span>•</span>
              <span className="capitalize">{ticket.category}</span>
              <span>•</span>
              <span>{formatDistanceToNow(new Date(ticket.createdAt), { addSuffix: true })}</span>
            </div>
          </div>

          <div className="flex flex-col items-end gap-2">
            <div className="flex items-center gap-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                {ticket.priority}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                {getStatusIcon(ticket.status)} {ticket.status.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Customer Info */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm font-medium text-gray-900">Customer:</span>
                <span className="ml-2 text-sm text-gray-700">{ticket.customerName}</span>
              </div>
              <div>
                <span className="text-sm text-gray-500">{ticket.customerEmail}</span>
              </div>
            </div>
          </div>

          {/* Description Preview */}
          <div>
            <p className={`text-gray-700 ${isExpanded ? '' : 'line-clamp-2'}`}>
              {ticket.description}
            </p>
            {ticket.description.length > 150 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-warm-600 hover:text-warm-700 text-sm font-medium mt-1"
              >
                {isExpanded ? 'Show less' : 'Show more'}
              </button>
            )}
          </div>

          {/* Latest Message Preview */}
          {latestMessage && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">
                  {latestMessage.senderRole === 'ADMIN' ? '👨‍💼 Support Team' : '👤 Customer'}
                </span>
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(latestMessage.createdAt), { addSuffix: true })}
                </span>
              </div>
              <p className="text-sm text-gray-700 line-clamp-2">
                {latestMessage.content}
              </p>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                💬 {ticket._count.messages} messages
              </span>
              <span className="flex items-center gap-1">
                🕒 Updated {formatDistanceToNow(new Date(ticket.updatedAt), { addSuffix: true })}
              </span>
            </div>

            <div className="flex items-center gap-2">
              {/* Quick Status Updates */}
              {ticket.status === 'OPEN' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickStatusUpdate('IN_PROGRESS')}
                  disabled={updating}
                >
                  Start Working
                </Button>
              )}
              
              {ticket.status === 'WAITING_FOR_ADMIN' && (
                <Button
                  size="sm"
                  onClick={handleViewTicket}
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  Reply Now
                </Button>
              )}
              
              {(ticket.status === 'IN_PROGRESS' || ticket.status === 'WAITING_FOR_CUSTOMER') && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickStatusUpdate('RESOLVED')}
                  disabled={updating}
                  className="text-green-600 border-green-600 hover:bg-green-50"
                >
                  Mark Resolved
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={handleViewTicket}
              >
                View Details
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
