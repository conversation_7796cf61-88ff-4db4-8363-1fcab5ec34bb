#!/bin/bash

# Ottiq Complete Backup Script
# Orchestrates backup of both PostgreSQL database and MinIO object storage
# Usage: ./backup-all.sh [environment]
# Environment: dev, prod (default: prod)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-prod}"

# Backup configuration
BACKUP_ROOT="$PROJECT_ROOT/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$BACKUP_ROOT/backup_all_${TIMESTAMP}.log"

# Create backup directories
mkdir -p "$BACKUP_ROOT/database"
mkdir -p "$BACKUP_ROOT/minio"
mkdir -p "$BACKUP_ROOT/logs"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        log "ERROR: Complete backup failed with exit code $exit_code"
        send_alert "Complete backup failed for $ENVIRONMENT environment"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Function to send alerts
send_alert() {
    local message="$1"
    log "ALERT: $message"
    echo "BACKUP ALERT: $message" >> "$BACKUP_ROOT/alerts.log"
    
    # Add webhook notification if configured
    if [[ -n "${BACKUP_WEBHOOK_URL:-}" ]]; then
        curl -X POST "$BACKUP_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$message\"}" \
            2>/dev/null || true
    fi
}

# Function to check system resources
check_system_resources() {
    log "Checking system resources..."
    
    # Check disk space
    local backup_disk_usage=$(df -h "$BACKUP_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $backup_disk_usage -gt 85 ]]; then
        log "WARNING: Backup disk usage is ${backup_disk_usage}%"
        send_alert "High disk usage detected: ${backup_disk_usage}%"
    fi
    
    # Check available memory
    local available_memory=$(free -m | awk 'NR==2{printf "%.1f", $7/1024}')
    log "Available memory: ${available_memory}GB"
    
    # Check Docker daemon
    if ! docker info > /dev/null 2>&1; then
        log "ERROR: Docker daemon is not running"
        return 1
    fi
    
    log "System resource check completed"
    return 0
}

# Function to backup database
backup_database() {
    log "Starting database backup..."
    
    local db_backup_script="$SCRIPT_DIR/backup-database.sh"
    if [[ -x "$db_backup_script" ]]; then
        if "$db_backup_script" "$ENVIRONMENT" 2>&1 | tee -a "$LOG_FILE"; then
            log "Database backup completed successfully"
            return 0
        else
            log "ERROR: Database backup failed"
            return 1
        fi
    else
        log "ERROR: Database backup script not found or not executable: $db_backup_script"
        return 1
    fi
}

# Function to backup MinIO
backup_minio() {
    log "Starting MinIO backup..."
    
    local minio_backup_script="$SCRIPT_DIR/backup-minio.sh"
    if [[ -x "$minio_backup_script" ]]; then
        if "$minio_backup_script" "$ENVIRONMENT" 2>&1 | tee -a "$LOG_FILE"; then
            log "MinIO backup completed successfully"
            return 0
        else
            log "ERROR: MinIO backup failed"
            return 1
        fi
    else
        log "ERROR: MinIO backup script not found or not executable: $minio_backup_script"
        return 1
    fi
}

# Function to create backup summary
create_backup_summary() {
    log "Creating backup summary..."
    
    local summary_file="$BACKUP_ROOT/backup_summary_${TIMESTAMP}.json"
    local db_backups=$(find "$BACKUP_ROOT/database" -name "ottiq_db_${ENVIRONMENT}_${TIMESTAMP:0:8}*.sql.gz" -type f | wc -l)
    local minio_backups=$(find "$BACKUP_ROOT/minio" -name "ottiq_minio_${ENVIRONMENT}_${TIMESTAMP:0:8}*.tar.gz" -type f | wc -l)
    
    # Calculate total backup size
    local total_size=$(du -sh "$BACKUP_ROOT" | cut -f1)
    
    # Get latest backup files
    local latest_db_backup=$(find "$BACKUP_ROOT/database" -name "ottiq_db_${ENVIRONMENT}_*.sql.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2- || echo "none")
    local latest_minio_backup=$(find "$BACKUP_ROOT/minio" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2- || echo "none")
    
    cat > "$summary_file" << EOF
{
  "backup_session": {
    "timestamp": "$TIMESTAMP",
    "environment": "$ENVIRONMENT",
    "date": "$(date -Iseconds)",
    "status": "completed"
  },
  "database_backup": {
    "count": $db_backups,
    "latest_file": "$(basename "$latest_db_backup")",
    "size": "$(du -h "$latest_db_backup" 2>/dev/null | cut -f1 || echo "unknown")"
  },
  "minio_backup": {
    "count": $minio_backups,
    "latest_file": "$(basename "$latest_minio_backup")",
    "size": "$(du -h "$latest_minio_backup" 2>/dev/null | cut -f1 || echo "unknown")"
  },
  "total_backup_size": "$total_size",
  "backup_location": "$BACKUP_ROOT"
}
EOF
    
    log "Backup summary created: $summary_file"
}

# Function to cleanup old logs
cleanup_old_logs() {
    log "Cleaning up old backup logs..."
    
    # Remove log files older than 30 days
    find "$BACKUP_ROOT" -name "backup_all_*.log" -type f -mtime +30 -delete 2>/dev/null || true
    find "$BACKUP_ROOT" -name "backup_summary_*.json" -type f -mtime +30 -delete 2>/dev/null || true
    
    # Keep only the last 100 alert entries
    if [[ -f "$BACKUP_ROOT/alerts.log" ]]; then
        tail -n 100 "$BACKUP_ROOT/alerts.log" > "$BACKUP_ROOT/alerts.log.tmp" 2>/dev/null || true
        mv "$BACKUP_ROOT/alerts.log.tmp" "$BACKUP_ROOT/alerts.log" 2>/dev/null || true
    fi
    
    log "Old logs cleanup completed"
}

# Function to send success notification
send_success_notification() {
    local summary_file="$BACKUP_ROOT/backup_summary_${TIMESTAMP}.json"
    
    if [[ -f "$summary_file" ]]; then
        local db_size=$(jq -r '.database_backup.size' "$summary_file" 2>/dev/null || echo "unknown")
        local minio_size=$(jq -r '.minio_backup.size' "$summary_file" 2>/dev/null || echo "unknown")
        local total_size=$(jq -r '.total_backup_size' "$summary_file" 2>/dev/null || echo "unknown")
        
        local message="Backup completed successfully for $ENVIRONMENT environment. DB: $db_size, MinIO: $minio_size, Total: $total_size"
        log "SUCCESS: $message"
        
        # Send success notification if webhook is configured
        if [[ -n "${BACKUP_SUCCESS_WEBHOOK_URL:-}" ]]; then
            curl -X POST "$BACKUP_SUCCESS_WEBHOOK_URL" \
                -H "Content-Type: application/json" \
                -d "{\"text\":\"✅ $message\"}" \
                2>/dev/null || true
        fi
    fi
}

# Function to validate backup integrity
validate_backups() {
    log "Validating backup integrity..."
    
    local validation_errors=0
    
    # Validate database backup
    local latest_db_backup=$(find "$BACKUP_ROOT/database" -name "ottiq_db_${ENVIRONMENT}_*.sql.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [[ -f "$latest_db_backup" ]]; then
        if gzip -t "$latest_db_backup" 2>/dev/null; then
            log "Database backup validation: PASSED"
        else
            log "ERROR: Database backup validation: FAILED"
            ((validation_errors++))
        fi
        
        # Check checksum if available
        if [[ -f "${latest_db_backup}.sha256" ]]; then
            if cd "$(dirname "$latest_db_backup")" && sha256sum -c "$(basename "${latest_db_backup}.sha256")" > /dev/null 2>&1; then
                log "Database backup checksum: PASSED"
            else
                log "ERROR: Database backup checksum: FAILED"
                ((validation_errors++))
            fi
        fi
    else
        log "ERROR: No database backup found for validation"
        ((validation_errors++))
    fi
    
    # Validate MinIO backup
    local latest_minio_backup=$(find "$BACKUP_ROOT/minio" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
    if [[ -f "$latest_minio_backup" ]]; then
        if tar -tzf "$latest_minio_backup" > /dev/null 2>&1; then
            log "MinIO backup validation: PASSED"
        else
            log "ERROR: MinIO backup validation: FAILED"
            ((validation_errors++))
        fi
        
        # Check checksum if available
        if [[ -f "${latest_minio_backup}.sha256" ]]; then
            if cd "$(dirname "$latest_minio_backup")" && sha256sum -c "$(basename "${latest_minio_backup}.sha256")" > /dev/null 2>&1; then
                log "MinIO backup checksum: PASSED"
            else
                log "ERROR: MinIO backup checksum: FAILED"
                ((validation_errors++))
            fi
        fi
    else
        log "ERROR: No MinIO backup found for validation"
        ((validation_errors++))
    fi
    
    if [[ $validation_errors -eq 0 ]]; then
        log "All backup validations passed"
        return 0
    else
        log "ERROR: $validation_errors backup validation(s) failed"
        return 1
    fi
}

# Main execution
main() {
    log "=== Ottiq Complete Backup Started ==="
    log "Environment: $ENVIRONMENT"
    log "Timestamp: $TIMESTAMP"
    log "Backup location: $BACKUP_ROOT"
    
    # System checks
    check_system_resources || exit 1
    
    # Perform backups
    local backup_success=true
    
    # Database backup
    if ! backup_database; then
        backup_success=false
    fi
    
    # MinIO backup
    if ! backup_minio; then
        backup_success=false
    fi
    
    # Check if any backup failed
    if [[ "$backup_success" != "true" ]]; then
        log "ERROR: One or more backups failed"
        exit 1
    fi
    
    # Validate backups
    validate_backups || exit 1
    
    # Create summary
    create_backup_summary
    
    # Cleanup old logs
    cleanup_old_logs
    
    # Send success notification
    send_success_notification
    
    log "=== Ottiq Complete Backup Completed Successfully ==="
}

# Run main function
main "$@"
