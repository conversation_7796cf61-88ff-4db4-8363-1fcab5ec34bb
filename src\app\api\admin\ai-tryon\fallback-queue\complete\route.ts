import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { emailService } from '@/lib/services/email';

const prisma = new PrismaClient();

const CompleteJobSchema = z.object({
  queueId: z.string().min(1, 'Queue ID is required'),
  resultImageUrl: z.string().min(1, 'Result image URL is required'),
  processingNotes: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
});

// POST /api/admin/ai-tryon/fallback-queue/complete - Complete a fallback job
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = CompleteJobSchema.parse(body);

    // Use transaction to update both queue item and AI try-on job
    const result = await prisma.$transaction(async (tx) => {
      // Get the queue item with AI try-on job
      const queueItem = await tx.aiTryOnFallbackQueue.findUnique({
        where: { 
          id: validatedData.queueId,
          assignedTo: session.user.id // Only allow completing own jobs
        },
        include: {
          aiTryOnJob: true
        }
      });

      if (!queueItem) {
        throw new Error('Queue item not found or not assigned to you');
      }

      if (queueItem.status !== 'PROCESSING' && queueItem.status !== 'CLAIMED') {
        throw new Error('Job is not in a state that can be completed');
      }

      // Update the AI try-on job with the result
      const updatedAiTryOnJob = await tx.aiTryOnJob.update({
        where: { id: queueItem.aiTryOnJobId },
        data: {
          status: 'FALLBACK_COMPLETED',
          resultImageUrl: validatedData.resultImageUrl,
          confidence: validatedData.confidence || 0.8, // Default confidence for manual processing
          processingTime: Math.round((Date.now() - queueItem.createdAt.getTime()) / 1000),
        }
      });

      // Update the queue item
      const updatedQueueItem = await tx.aiTryOnFallbackQueue.update({
        where: { id: validatedData.queueId },
        data: {
          status: 'COMPLETED',
          processingNotes: validatedData.processingNotes,
        },
        include: {
          aiTryOnJob: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              },
              customization: {
                include: {
                  product: {
                    select: {
                      name: true,
                      category: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      return { queueItem: updatedQueueItem, aiTryOnJob: updatedAiTryOnJob };
    });

    // Send AI try-on ready email
    await sendAiTryOnReadyEmail(result.queueItem.aiTryOnJob);

    return NextResponse.json({
      success: true,
      data: result.queueItem,
      message: 'Job completed successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error completing fallback job:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to complete job' },
      { status: 500 }
    );
  }
}

/**
 * Send AI try-on ready email notification for fallback completion
 */
async function sendAiTryOnReadyEmail(aiTryOnJobData: any): Promise<void> {
  try {
    const user = aiTryOnJobData.user;
    const customization = aiTryOnJobData.customization;

    if (!user?.email) {
      console.warn('Cannot send AI try-on ready email: user email not found');
      return;
    }

    // Prepare email data
    const emailData = {
      to: user.email,
      customerName: user.name || 'Valued Customer',
      productName: customization?.product?.name || 'Custom Product',
      tryOnImageUrl: aiTryOnJobData.resultImageUrl,
      customizationName: customization?.name || 'Custom Design',
      orderUrl: `${process.env.APP_URL}/create?design=${customization?.id}`,
    };

    // Send email
    const result = await emailService.sendEmail('ai-tryon-ready', emailData, {
      priority: 'high',
    });

    if (result.success) {
      console.log('AI try-on ready email sent successfully (fallback):', {
        jobId: aiTryOnJobData.id,
        email: user.email,
        messageId: result.messageId,
      });
    } else {
      console.error('Failed to send AI try-on ready email (fallback):', result.error);
    }
  } catch (error) {
    console.error('Error sending AI try-on ready email (fallback):', error);
  }
}
