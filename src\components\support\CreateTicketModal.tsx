'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Input, Textarea } from '@/components/ui';
import { useResponsive } from '@/hooks/useResponsive';

interface CreateTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTicketCreated: () => void;
}

export function CreateTicketModal({ isOpen, onClose, onTicketCreated }: CreateTicketModalProps) {
  const { isMobile } = useResponsive();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    subject: '',
    description: '',
    category: 'general',
    priority: 'MEDIUM',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        onTicketCreated();
        setFormData({
          subject: '',
          description: '',
          category: 'general',
          priority: 'MEDIUM',
        });
      } else {
        setError(data.error || 'Failed to create ticket');
      }
    } catch (error) {
      console.error('Error creating ticket:', error);
      setError('Failed to create ticket. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(null);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          className={`bg-white rounded-2xl shadow-2xl w-full max-w-2xl ${isMobile ? 'max-h-[90vh]' : 'max-h-[80vh]'} overflow-hidden`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-warm-500 to-amber-500 text-white p-6 relative">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-white/80 hover:text-white text-2xl"
            >
              ✕
            </button>
            <h2 className="text-2xl font-bold mb-2">🎫 Create Support Ticket</h2>
            <p className="text-white/90">
              Tell us how we can help you. Our support team will respond as soon as possible.
            </p>
          </div>

          {/* Form */}
          <div className="p-6 overflow-y-auto">
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg"
                >
                  {error}
                </motion.div>
              )}

              {/* Category and Priority */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-warm-500"
                    required
                  >
                    <option value="general">💬 General Question</option>
                    <option value="order">📦 Order Issue</option>
                    <option value="product">👕 Product Question</option>
                    <option value="technical">🔧 Technical Problem</option>
                    <option value="billing">💳 Billing Issue</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => handleInputChange('priority', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-warm-500"
                    required
                  >
                    <option value="LOW">🟢 Low - General inquiry</option>
                    <option value="MEDIUM">🟡 Medium - Standard issue</option>
                    <option value="HIGH">🟠 High - Important issue</option>
                  </select>
                </div>
              </div>

              {/* Subject */}
              <div>
                <Input
                  label="Subject"
                  placeholder="Brief description of your issue..."
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  required
                  maxLength={200}
                />
                <div className="text-right text-xs text-gray-500 mt-1">
                  {formData.subject.length}/200
                </div>
              </div>

              {/* Description */}
              <div>
                <Textarea
                  label="Description"
                  placeholder="Please provide detailed information about your issue. Include any relevant order numbers, error messages, or steps you've already tried..."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={6}
                  required
                  maxLength={5000}
                />
                <div className="text-right text-xs text-gray-500 mt-1">
                  {formData.description.length}/5000
                </div>
              </div>

              {/* Tips */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">💡 Tips for faster resolution:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Include order numbers if relevant</li>
                  <li>• Describe what you expected vs. what happened</li>
                  <li>• Mention any error messages you saw</li>
                  <li>• Tell us what device/browser you're using</li>
                </ul>
              </div>

              {/* Actions */}
              <div className="flex flex-col-reverse md:flex-row gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={loading}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading || !formData.subject.trim() || !formData.description.trim()}
                  className="flex-1"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating Ticket...
                    </>
                  ) : (
                    '🚀 Create Ticket'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
