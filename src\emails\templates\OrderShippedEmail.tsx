/**
 * Order Shipped Email Template
 * Builds excitement for delivery with tracking information
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { OrderEmailData } from '../../lib/services/email';

interface OrderShippedEmailProps extends OrderEmailData {
  trackingNumber: string;
}

export function OrderShippedEmail({
  customerName,
  orderNumber,
  totalAmount,
  currency,
  items,
  shippingAddress,
  trackingNumber,
  estimatedDelivery,
  orderUrl,
}: OrderShippedEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';
  const trackingUrl = `${baseUrl}/track/${trackingNumber}`;

  return (
    <EmailLayout previewText={`Your Ottiq order #${orderNumber} is on its way! Track your package now.`}>
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-shipped.jpg`}
          alt="Your order is on its way"
          width="600"
          height="300"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          📦 Your Style is On Its Way, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          The moment you've been waiting for is almost here. Your custom creation has left our studio and is heading straight to you!
        </EmailText>
      </Section>

      {/* Tracking Information */}
      <Section className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 mb-6 text-center">
        <EmailHeading level={2}>📍 Track Your Package</EmailHeading>
        
        <div className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-sm text-gray-600 mb-2">Tracking Number</Text>
          <Text className="text-xl font-mono font-bold text-gray-900 mb-4">
            {trackingNumber}
          </Text>
          
          <EmailButton href={trackingUrl} variant="primary">
            Track Your Order
          </EmailButton>
        </div>

        {estimatedDelivery && (
          <Text className="text-green-700 font-semibold">
            Expected Delivery: {estimatedDelivery}
          </Text>
        )}
      </Section>

      {/* Order Summary */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <EmailHeading level={2}>Order #{orderNumber}</EmailHeading>
          <Text className="text-lg font-bold text-primary-600">
            {currency} {totalAmount}
          </Text>
        </div>

        {/* Order Items Preview */}
        <div className="grid grid-cols-1 gap-4">
          {items.slice(0, 3).map((item, index) => (
            <div key={index} className="flex items-center space-x-4 p-3 bg-white rounded-lg">
              {item.customization?.previewImage && (
                <Img
                  src={item.customization.previewImage}
                  alt={item.name}
                  width="60"
                  height="60"
                  className="rounded-lg"
                />
              )}
              <div className="flex-1">
                <Text className="font-semibold text-gray-900 text-sm mb-1">
                  {item.name}
                </Text>
                {item.customization && (
                  <Text className="text-xs text-primary-600">
                    {item.customization.name}
                  </Text>
                )}
              </div>
              <Text className="text-sm text-gray-600">
                ×{item.quantity}
              </Text>
            </div>
          ))}
          
          {items.length > 3 && (
            <Text className="text-center text-sm text-gray-500">
              +{items.length - 3} more item{items.length - 3 > 1 ? 's' : ''}
            </Text>
          )}
        </div>
      </Section>

      {/* Shipping Address */}
      <Section className="bg-blue-50 rounded-lg p-6 mb-6">
        <EmailHeading level={3}>Shipping To:</EmailHeading>
        <Text className="text-gray-700 mb-0">
          {shippingAddress.name}<br />
          {shippingAddress.address}<br />
          {shippingAddress.city}, {shippingAddress.postalCode}<br />
          {shippingAddress.country}
        </Text>
      </Section>

      {/* Delivery Tips */}
      <Section className="mb-6">
        <EmailHeading level={2}>📋 Delivery Tips</EmailHeading>
        
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">📱</Text>
            <EmailText className="mb-2">
              <strong>Stay Updated:</strong> You'll receive SMS notifications about delivery updates. Make sure someone is available to receive your package.
            </EmailText>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">🏠</Text>
            <EmailText className="mb-2">
              <strong>Secure Delivery:</strong> Our courier will require ID verification before handing over your package to ensure it reaches the right person.
            </EmailText>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">📦</Text>
            <EmailText className="mb-2">
              <strong>Package Care:</strong> Your custom creation is carefully packaged to arrive in perfect condition. Please inspect upon delivery.
            </EmailText>
          </div>
        </div>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-6">
        <div className="space-y-3">
          <EmailButton href={trackingUrl} variant="primary">
            Track Your Package
          </EmailButton>
          
          <br />
          
          <EmailButton href={orderUrl} variant="outline">
            View Order Details
          </EmailButton>
        </div>
      </Section>

      {/* Excitement Builder */}
      <Section className="bg-gradient-to-r from-primary-50 to-warm-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>Get Ready to Shine! ✨</EmailHeading>
        <EmailText>
          Your unique style is almost in your hands. Start planning your first outfit and get ready to turn heads with your one-of-a-kind creation!
        </EmailText>
        
        <Text className="text-sm text-gray-600">
          Don't forget to share your unboxing moment with #OttiqUnboxing
        </Text>
      </Section>

      {/* Support */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          Questions about your delivery? Our support team is here to help.
        </EmailText>
        <EmailButton href={`${baseUrl}/support`} variant="outline">
          Contact Support
        </EmailButton>
      </Section>
    </EmailLayout>
  );
}
