#!/bin/bash

# Ottiq Backup Configuration Script
# Sets up automated backup scheduling and configuration
# Usage: ./backup-config.sh [install|uninstall|status]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ACTION="${1:-install}"

# Backup configuration
BACKUP_USER="${BACKUP_USER:-$(whoami)}"
BACKUP_TIME="${BACKUP_TIME:-02:00}"  # 2 AM daily
ENVIRONMENT="${ENVIRONMENT:-prod}"

# Paths
CRON_FILE="/tmp/ottiq_backup_cron"
LOG_DIR="$PROJECT_ROOT/backups/logs"

# Create log directory
mkdir -p "$LOG_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to install backup cron job
install_backup_cron() {
    log "Installing backup cron job..."
    
    # Create cron entry
    cat > "$CRON_FILE" << EOF
# Ottiq Automated Backup - Daily at $BACKUP_TIME
0 2 * * * cd "$PROJECT_ROOT" && "$SCRIPT_DIR/backup-all.sh" "$ENVIRONMENT" >> "$LOG_DIR/cron.log" 2>&1

# Ottiq Backup Health Check - Every 6 hours
0 */6 * * * cd "$PROJECT_ROOT" && "$SCRIPT_DIR/backup-health-check.sh" "$ENVIRONMENT" >> "$LOG_DIR/health.log" 2>&1

# Ottiq Backup Cleanup - Weekly on Sunday at 3 AM
0 3 * * 0 cd "$PROJECT_ROOT" && "$SCRIPT_DIR/backup-cleanup.sh" "$ENVIRONMENT" >> "$LOG_DIR/cleanup.log" 2>&1
EOF
    
    # Install cron job
    if crontab -l 2>/dev/null | grep -v "Ottiq Automated Backup" | cat - "$CRON_FILE" | crontab -; then
        log "Backup cron job installed successfully"
        log "Daily backup scheduled at $BACKUP_TIME for $ENVIRONMENT environment"
    else
        log "ERROR: Failed to install backup cron job"
        return 1
    fi
    
    # Clean up temp file
    rm -f "$CRON_FILE"
    
    return 0
}

# Function to uninstall backup cron job
uninstall_backup_cron() {
    log "Uninstalling backup cron job..."
    
    # Remove Ottiq backup entries from crontab
    if crontab -l 2>/dev/null | grep -v "Ottiq Automated Backup" | grep -v "Ottiq Backup Health Check" | grep -v "Ottiq Backup Cleanup" | crontab -; then
        log "Backup cron job uninstalled successfully"
    else
        log "WARNING: Failed to uninstall backup cron job or no cron job found"
    fi
    
    return 0
}

# Function to show backup cron status
show_backup_status() {
    log "Backup cron job status:"
    
    # Check if cron jobs exist
    local cron_entries=$(crontab -l 2>/dev/null | grep -c "Ottiq.*Backup" || echo "0")
    
    if [[ $cron_entries -gt 0 ]]; then
        log "Found $cron_entries Ottiq backup cron entries:"
        crontab -l 2>/dev/null | grep "Ottiq.*Backup" | while read -r line; do
            log "  $line"
        done
    else
        log "No Ottiq backup cron jobs found"
    fi
    
    # Check recent backup logs
    log ""
    log "Recent backup activity:"
    
    if [[ -f "$LOG_DIR/cron.log" ]]; then
        local last_backup=$(tail -n 20 "$LOG_DIR/cron.log" | grep "Backup Completed Successfully" | tail -1 || echo "")
        if [[ -n "$last_backup" ]]; then
            log "Last successful backup: $last_backup"
        else
            log "No recent successful backups found in logs"
        fi
    else
        log "No backup logs found"
    fi
    
    # Check backup directory
    local backup_count=$(find "$PROJECT_ROOT/backups" -name "*.gz" -type f 2>/dev/null | wc -l)
    local backup_size=$(du -sh "$PROJECT_ROOT/backups" 2>/dev/null | cut -f1 || echo "0")
    log "Backup files: $backup_count files, $backup_size total"
    
    return 0
}

# Function to create backup health check script
create_health_check_script() {
    local health_script="$SCRIPT_DIR/backup-health-check.sh"
    
    log "Creating backup health check script..."
    
    cat > "$health_script" << 'EOF'
#!/bin/bash

# Ottiq Backup Health Check Script
# Monitors backup status and sends alerts for issues

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-prod}"

# Configuration
BACKUP_ROOT="$PROJECT_ROOT/backups"
MAX_BACKUP_AGE_HOURS=26  # Alert if no backup in 26 hours
ALERT_LOG="$BACKUP_ROOT/alerts.log"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] HEALTH: $1"
}

# Function to send alert
send_alert() {
    local message="$1"
    log "ALERT: $message"
    echo "HEALTH ALERT: $message" >> "$ALERT_LOG"
}

# Check if recent backups exist
check_recent_backups() {
    local current_time=$(date +%s)
    local max_age_seconds=$((MAX_BACKUP_AGE_HOURS * 3600))
    
    # Check database backup
    local latest_db_backup=$(find "$BACKUP_ROOT/database" -name "ottiq_db_${ENVIRONMENT}_*.sql.gz" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f1 || echo "0")
    local db_age_seconds=$((current_time - ${latest_db_backup%.*}))
    
    if [[ $db_age_seconds -gt $max_age_seconds ]]; then
        send_alert "Database backup is too old: $((db_age_seconds / 3600)) hours"
        return 1
    fi
    
    # Check MinIO backup
    local latest_minio_backup=$(find "$BACKUP_ROOT/minio" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f1 || echo "0")
    local minio_age_seconds=$((current_time - ${latest_minio_backup%.*}))
    
    if [[ $minio_age_seconds -gt $max_age_seconds ]]; then
        send_alert "MinIO backup is too old: $((minio_age_seconds / 3600)) hours"
        return 1
    fi
    
    log "Recent backup check passed"
    return 0
}

# Check disk space
check_disk_space() {
    local backup_disk_usage=$(df -h "$BACKUP_ROOT" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $backup_disk_usage -gt 90 ]]; then
        send_alert "Critical: Backup disk usage is ${backup_disk_usage}%"
        return 1
    elif [[ $backup_disk_usage -gt 80 ]]; then
        send_alert "Warning: Backup disk usage is ${backup_disk_usage}%"
    fi
    
    log "Disk space check passed: ${backup_disk_usage}% used"
    return 0
}

# Check Docker services
check_docker_services() {
    if ! docker ps --format "table {{.Names}}" | grep -q "ottiq-postgres"; then
        send_alert "PostgreSQL container is not running"
        return 1
    fi
    
    if ! docker ps --format "table {{.Names}}" | grep -q "ottiq-minio"; then
        send_alert "MinIO container is not running"
        return 1
    fi
    
    log "Docker services check passed"
    return 0
}

# Main health check
main() {
    log "Starting backup health check for $ENVIRONMENT environment"
    
    local health_issues=0
    
    # Run health checks
    check_recent_backups || ((health_issues++))
    check_disk_space || ((health_issues++))
    check_docker_services || ((health_issues++))
    
    if [[ $health_issues -eq 0 ]]; then
        log "All health checks passed"
    else
        log "Health check completed with $health_issues issue(s)"
    fi
    
    return $health_issues
}

main "$@"
EOF
    
    chmod +x "$health_script"
    log "Health check script created: $health_script"
}

# Function to create backup cleanup script
create_cleanup_script() {
    local cleanup_script="$SCRIPT_DIR/backup-cleanup.sh"
    
    log "Creating backup cleanup script..."
    
    cat > "$cleanup_script" << 'EOF'
#!/bin/bash

# Ottiq Backup Cleanup Script
# Manages backup retention and cleanup

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-prod}"

# Configuration
BACKUP_ROOT="$PROJECT_ROOT/backups"
DAILY_RETENTION=30      # Keep daily backups for 30 days
WEEKLY_RETENTION=84     # Keep weekly backups for 12 weeks
MONTHLY_RETENTION=365   # Keep monthly backups for 12 months

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] CLEANUP: $1"
}

# Cleanup old backups with retention policy
cleanup_backups() {
    log "Starting backup cleanup for $ENVIRONMENT environment"
    
    local cleaned_files=0
    
    # Clean up database backups
    log "Cleaning up database backups..."
    while IFS= read -r -d '' file; do
        rm -f "$file"
        ((cleaned_files++))
    done < <(find "$BACKUP_ROOT/database" -name "ottiq_db_${ENVIRONMENT}_*.sql.gz" -type f -mtime +$DAILY_RETENTION -print0 2>/dev/null)
    
    # Clean up MinIO backups
    log "Cleaning up MinIO backups..."
    while IFS= read -r -d '' file; do
        rm -f "$file"
        ((cleaned_files++))
    done < <(find "$BACKUP_ROOT/minio" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz" -type f -mtime +$DAILY_RETENTION -print0 2>/dev/null)
    
    # Clean up associated files (checksums, manifests)
    find "$BACKUP_ROOT" -name "*.sha256" -type f -mtime +$DAILY_RETENTION -delete 2>/dev/null || true
    find "$BACKUP_ROOT" -name "*.manifest" -type f -mtime +$DAILY_RETENTION -delete 2>/dev/null || true
    
    log "Cleanup completed: removed $cleaned_files backup files"
    
    # Show current backup status
    local remaining_files=$(find "$BACKUP_ROOT" -name "*.gz" -type f | wc -l)
    local total_size=$(du -sh "$BACKUP_ROOT" | cut -f1)
    log "Remaining backups: $remaining_files files, $total_size total"
}

main() {
    cleanup_backups
}

main "$@"
EOF
    
    chmod +x "$cleanup_script"
    log "Cleanup script created: $cleanup_script"
}

# Function to make scripts executable
make_scripts_executable() {
    log "Making backup scripts executable..."
    
    chmod +x "$SCRIPT_DIR/backup-all.sh" 2>/dev/null || true
    chmod +x "$SCRIPT_DIR/backup-database.sh" 2>/dev/null || true
    chmod +x "$SCRIPT_DIR/backup-minio.sh" 2>/dev/null || true
    chmod +x "$SCRIPT_DIR/restore-database.sh" 2>/dev/null || true
    chmod +x "$SCRIPT_DIR/restore-minio.sh" 2>/dev/null || true
    
    log "Scripts made executable"
}

# Main execution
main() {
    log "=== Ottiq Backup Configuration ==="
    log "Action: $ACTION"
    log "Environment: $ENVIRONMENT"
    log "User: $BACKUP_USER"
    
    case "$ACTION" in
        "install")
            make_scripts_executable
            create_health_check_script
            create_cleanup_script
            install_backup_cron
            log "Backup configuration installed successfully"
            ;;
        "uninstall")
            uninstall_backup_cron
            log "Backup configuration uninstalled successfully"
            ;;
        "status")
            show_backup_status
            ;;
        *)
            echo "Usage: $0 [install|uninstall|status]"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
