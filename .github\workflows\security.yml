name: Security & Dependency Monitoring

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
  push:
    paths:
      - 'package.json'
      - 'package-lock.json'
      - '.github/workflows/security.yml'

env:
  NODE_VERSION: '20'

jobs:
  # Security Audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run npm audit
        run: npm audit --audit-level moderate

      - name: Run security tests
        run: npm run security:test
        env:
          NODE_ENV: test

      - name: Check for known vulnerabilities
        uses: actions/dependency-review-action@v4
        if: github.event_name == 'pull_request'

  # Dependency Updates
  dependency-update:
    name: Check for Dependency Updates
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Check for outdated packages
        run: |
          npm outdated --json > outdated.json || true
          if [ -s outdated.json ]; then
            echo "📦 Outdated packages found:"
            cat outdated.json
            echo "outdated_packages=true" >> $GITHUB_ENV
          else
            echo "✅ All packages are up to date"
            echo "outdated_packages=false" >> $GITHUB_ENV
          fi

      - name: Create issue for outdated packages
        if: env.outdated_packages == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const outdated = JSON.parse(fs.readFileSync('outdated.json', 'utf8'));
            
            let body = '## 📦 Outdated Dependencies Report\n\n';
            body += 'The following packages have updates available:\n\n';
            body += '| Package | Current | Wanted | Latest |\n';
            body += '|---------|---------|--------|---------|\n';
            
            for (const [pkg, info] of Object.entries(outdated)) {
              body += `| ${pkg} | ${info.current} | ${info.wanted} | ${info.latest} |\n`;
            }
            
            body += '\n### 🔧 Recommended Actions\n';
            body += '1. Review the changelog for each package\n';
            body += '2. Test updates in a development environment\n';
            body += '3. Update packages with `npm update`\n';
            body += '4. Run the full test suite\n';
            body += '\n*This issue was automatically created by the dependency monitoring workflow.*';
            
            // Check if issue already exists
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['dependencies', 'automated'],
              state: 'open'
            });
            
            if (issues.data.length === 0) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: '📦 Dependency Updates Available',
                body: body,
                labels: ['dependencies', 'automated', 'maintenance']
              });
            }

  # License Compliance
  license-check:
    name: License Compliance Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install license checker
        run: npm install -g license-checker

      - name: Check licenses
        run: |
          license-checker --onlyAllow 'MIT;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;0BSD;CC0-1.0;Unlicense' --excludePrivatePackages --json > licenses.json
          echo "✅ License compliance check passed"

      - name: Upload license report
        uses: actions/upload-artifact@v4
        with:
          name: license-report
          path: licenses.json
          retention-days: 30

  # Code Quality Monitoring
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for better analysis

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests with coverage
        run: npm run test:coverage

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        continue-on-error: true

      - name: CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          languages: javascript
        continue-on-error: true
