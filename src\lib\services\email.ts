/**
 * Email Service for Ottiq Platform
 * Handles all transactional email sending with branded templates
 */

import nodemailer from 'nodemailer';
import { render } from '@react-email/render';
import { z } from 'zod';

// Email configuration schema
const EmailConfigSchema = z.object({
  host: z.string(),
  port: z.number(),
  secure: z.boolean().optional(),
  auth: z.object({
    user: z.string().optional(),
    pass: z.string().optional(),
  }).optional(),
  from: z.string().email(),
});

// Email types
export type EmailType =
  | 'order-confirmation'
  | 'order-shipped'
  | 'order-delivered'
  | 'order-cancelled'
  | 'payment-confirmation'
  | 'payment-failed'
  | 'ai-tryon-ready'
  | 'welcome'
  | 'password-reset'
  | 'support-ticket-created'
  | 'support-ticket-reply'
  | 'support-ticket-customer-reply'
  | 'support-ticket-resolved'
  | 'support-ticket-closed';

// Email data interfaces
export interface BaseEmailData {
  to: string;
  customerName: string;
}

export interface OrderEmailData extends BaseEmailData {
  orderNumber: string;
  orderDate: string;
  totalAmount: string;
  currency: string;
  items: Array<{
    name: string;
    quantity: number;
    price: string;
    customization?: {
      previewImage?: string;
      name: string;
    };
  }>;
  shippingAddress: {
    name: string;
    address: string;
    city: string;
    postalCode: string;
    country: string;
  };
  trackingNumber?: string;
  estimatedDelivery?: string;
  orderUrl: string;
}

export interface PaymentEmailData extends BaseEmailData {
  orderNumber: string;
  amount: string;
  currency: string;
  paymentMethod: string;
  transactionId?: string;
  orderUrl: string;
}

export interface AiTryOnEmailData extends BaseEmailData {
  productName: string;
  tryOnImageUrl: string;
  customizationName: string;
  orderUrl: string;
}

export interface WelcomeEmailData extends BaseEmailData {
  // No additional fields needed
}

export interface PasswordResetEmailData extends BaseEmailData {
  resetUrl: string;
  expiresIn: string;
}

export interface SupportTicketCreatedEmailData extends BaseEmailData {
  ticketId: string;
  subject: string;
  description: string;
  priority: string;
  category: string;
  ticketUrl: string;
}

export interface SupportTicketReplyEmailData extends BaseEmailData {
  ticketId: string;
  subject: string;
  message: string;
  senderName: string;
  ticketUrl: string;
}

export interface SupportTicketCustomerReplyEmailData extends BaseEmailData {
  ticketId: string;
  subject: string;
  message: string;
  ticketUrl: string;
}

export interface SupportTicketResolvedEmailData extends BaseEmailData {
  ticketId: string;
  subject: string;
  resolution: string;
  ticketUrl: string;
}

export interface SupportTicketClosedEmailData extends BaseEmailData {
  ticketId: string;
  subject: string;
  ticketUrl: string;
}

export type EmailData =
  | OrderEmailData
  | PaymentEmailData
  | AiTryOnEmailData
  | WelcomeEmailData
  | PasswordResetEmailData
  | SupportTicketCreatedEmailData
  | SupportTicketReplyEmailData
  | SupportTicketCustomerReplyEmailData
  | SupportTicketResolvedEmailData
  | SupportTicketClosedEmailData;

// Email service class
class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: z.infer<typeof EmailConfigSchema> | null = null;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      const config = {
        host: process.env.SMTP_HOST || 'localhost',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: process.env.SMTP_USER ? {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASSWORD,
        } : undefined,
        from: process.env.SMTP_FROM || '<EMAIL>',
      };

      // Validate configuration
      const validatedConfig = EmailConfigSchema.parse(config);
      this.config = validatedConfig;

      // Create transporter
      this.transporter = nodemailer.createTransporter({
        host: validatedConfig.host,
        port: validatedConfig.port,
        secure: validatedConfig.secure,
        auth: validatedConfig.auth,
        // Additional options for better deliverability
        pool: true,
        maxConnections: 5,
        maxMessages: 100,
        rateDelta: 1000,
        rateLimit: 5,
      });

      console.log('Email service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize email service:', error);
      this.transporter = null;
      this.config = null;
    }
  }

  /**
   * Check if email service is available
   */
  public isAvailable(): boolean {
    return this.transporter !== null && this.config !== null;
  }

  /**
   * Verify email configuration
   */
  public async verifyConnection(): Promise<boolean> {
    if (!this.transporter) {
      return false;
    }

    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      console.error('Email connection verification failed:', error);
      return false;
    }
  }

  /**
   * Send email with template
   */
  public async sendEmail(
    type: EmailType,
    data: EmailData,
    options?: {
      priority?: 'high' | 'normal' | 'low';
      delay?: number; // Delay in milliseconds
    }
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    if (!this.transporter || !this.config) {
      return {
        success: false,
        error: 'Email service not available',
      };
    }

    try {
      // Get template component
      const TemplateComponent = await this.getTemplateComponent(type);
      if (!TemplateComponent) {
        return {
          success: false,
          error: `Template not found for type: ${type}`,
        };
      }

      // Render email HTML
      const html = render(TemplateComponent(data));
      
      // Generate subject line
      const subject = this.generateSubject(type, data);

      // Prepare email options
      const mailOptions = {
        from: this.config.from,
        to: data.to,
        subject,
        html,
        priority: options?.priority || 'normal',
        headers: {
          'X-Mailer': 'Ottiq Platform',
          'X-Email-Type': type,
        },
      };

      // Add delay if specified
      if (options?.delay) {
        await new Promise(resolve => setTimeout(resolve, options.delay));
      }

      // Send email
      const result = await this.transporter.sendMail(mailOptions);

      console.log('Email sent successfully:', {
        type,
        to: data.to,
        messageId: result.messageId,
      });

      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      console.error('Failed to send email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get template component for email type
   */
  private async getTemplateComponent(type: EmailType) {
    try {
      switch (type) {
        case 'order-confirmation':
          const { OrderConfirmationEmail } = await import('../../emails/templates/OrderConfirmationEmail');
          return OrderConfirmationEmail;
        case 'order-shipped':
          const { OrderShippedEmail } = await import('../../emails/templates/OrderShippedEmail');
          return OrderShippedEmail;
        case 'order-delivered':
          const { OrderDeliveredEmail } = await import('../../emails/templates/OrderDeliveredEmail');
          return OrderDeliveredEmail;
        case 'order-cancelled':
          const { OrderCancelledEmail } = await import('../../emails/templates/OrderCancelledEmail');
          return OrderCancelledEmail;
        case 'payment-confirmation':
          const { PaymentConfirmationEmail } = await import('../../emails/templates/PaymentConfirmationEmail');
          return PaymentConfirmationEmail;
        case 'payment-failed':
          const { PaymentFailedEmail } = await import('../../emails/templates/PaymentFailedEmail');
          return PaymentFailedEmail;
        case 'ai-tryon-ready':
          const { AiTryOnReadyEmail } = await import('../../emails/templates/AiTryOnReadyEmail');
          return AiTryOnReadyEmail;
        case 'welcome':
          const { WelcomeEmail } = await import('../../emails/templates/WelcomeEmail');
          return WelcomeEmail;
        case 'password-reset':
          const { PasswordResetEmail } = await import('../../emails/templates/PasswordResetEmail');
          return PasswordResetEmail;
        case 'support-ticket-created':
          const { SupportTicketCreatedEmail } = await import('../../emails/templates/SupportTicketCreatedEmail');
          return SupportTicketCreatedEmail;
        case 'support-ticket-reply':
          const { SupportTicketReplyEmail } = await import('../../emails/templates/SupportTicketReplyEmail');
          return SupportTicketReplyEmail;
        case 'support-ticket-customer-reply':
          const { SupportTicketCustomerReplyEmail } = await import('../../emails/templates/SupportTicketCustomerReplyEmail');
          return SupportTicketCustomerReplyEmail;
        case 'support-ticket-resolved':
          const { SupportTicketResolvedEmail } = await import('../../emails/templates/SupportTicketResolvedEmail');
          return SupportTicketResolvedEmail;
        case 'support-ticket-closed':
          const { SupportTicketClosedEmail } = await import('../../emails/templates/SupportTicketClosedEmail');
          return SupportTicketClosedEmail;
        default:
          return null;
      }
    } catch (error) {
      console.error(`Failed to load template for type ${type}:`, error);
      return null;
    }
  }

  /**
   * Generate subject line based on email type and data
   */
  private generateSubject(type: EmailType, data: EmailData): string {
    switch (type) {
      case 'order-confirmation':
        return `Your Ottiq Order is Confirmed! 🎉 Order #${(data as OrderEmailData).orderNumber}`;
      case 'order-shipped':
        return `Your Ottiq Creation is On Its Way! 📦 Order #${(data as OrderEmailData).orderNumber}`;
      case 'order-delivered':
        return `Your Ottiq Style Has Arrived! ✨ Order #${(data as OrderEmailData).orderNumber}`;
      case 'order-cancelled':
        return `Order Cancelled - Order #${(data as OrderEmailData).orderNumber}`;
      case 'payment-confirmation':
        return `Payment Confirmed! 💳 Order #${(data as PaymentEmailData).orderNumber}`;
      case 'payment-failed':
        return `Payment Issue - Order #${(data as PaymentEmailData).orderNumber}`;
      case 'ai-tryon-ready':
        return `Your AI Try-On is Ready! 🤳 See How You Look`;
      case 'welcome':
        return `Welcome to Ottiq - Wear Your Imagination! 👋`;
      case 'password-reset':
        return `Reset Your Ottiq Password 🔐`;
      case 'support-ticket-created':
        return `New Support Ticket #${(data as SupportTicketCreatedEmailData).ticketId} - ${(data as SupportTicketCreatedEmailData).subject}`;
      case 'support-ticket-reply':
        return `Support Reply: ${(data as SupportTicketReplyEmailData).subject} 💬`;
      case 'support-ticket-customer-reply':
        return `Customer Reply: ${(data as SupportTicketCustomerReplyEmailData).subject} 💬`;
      case 'support-ticket-resolved':
        return `Ticket Resolved: ${(data as SupportTicketResolvedEmailData).subject} ✅`;
      case 'support-ticket-closed':
        return `Ticket Closed: ${(data as SupportTicketClosedEmailData).subject} 📋`;
      default:
        return 'Ottiq Notification';
    }
  }

  /**
   * Send bulk emails (for newsletters, announcements)
   */
  public async sendBulkEmails(
    type: EmailType,
    recipients: Array<{ email: string; data: EmailData }>,
    options?: {
      batchSize?: number;
      delayBetweenBatches?: number;
    }
  ): Promise<{ success: boolean; sent: number; failed: number; errors: string[] }> {
    const batchSize = options?.batchSize || 10;
    const delay = options?.delayBetweenBatches || 1000;

    let sent = 0;
    let failed = 0;
    const errors: string[] = [];

    // Process in batches
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);

      // Send batch concurrently
      const promises = batch.map(async ({ email, data }) => {
        const result = await this.sendEmail(type, { ...data, to: email });
        if (result.success) {
          sent++;
        } else {
          failed++;
          errors.push(`${email}: ${result.error}`);
        }
      });

      await Promise.all(promises);

      // Delay between batches (except for the last batch)
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return {
      success: failed === 0,
      sent,
      failed,
      errors,
    };
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Export types for use in other modules
export type { EmailConfigSchema };
