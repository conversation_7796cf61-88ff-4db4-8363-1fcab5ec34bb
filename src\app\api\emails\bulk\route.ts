/**
 * Bulk Email API Route
 * Handles sending emails to multiple recipients (admin only)
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { emailService, EmailType } from '../../../../lib/services/email';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { prisma } from '../../../../lib/db';

// Bulk email request schema
const BulkEmailSchema = z.object({
  type: z.enum([
    'welcome',
    'ai-tryon-ready',
    'order-confirmation',
    'order-shipped',
    'order-delivered',
  ]),
  recipients: z.array(z.object({
    email: z.string().email(),
    data: z.object({}).passthrough(),
  })).max(100), // Limit to 100 recipients per request
  options: z.object({
    batchSize: z.number().min(1).max(50).default(10),
    delayBetweenBatches: z.number().min(100).max(10000).default(1000),
  }).optional(),
});

// Predefined recipient groups
const RecipientGroupSchema = z.object({
  group: z.enum(['all-users', 'recent-orders', 'ai-tryon-users']),
  type: z.enum(['welcome', 'ai-tryon-ready']),
  customData: z.object({}).passthrough().optional(),
  options: z.object({
    batchSize: z.number().min(1).max(50).default(10),
    delayBetweenBatches: z.number().min(100).max(10000).default(1000),
  }).optional(),
});

/**
 * POST /api/emails/bulk - Send bulk emails (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        {
          success: false,
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'ADMIN_ACCESS_REQUIRED',
          message: 'Admin access required',
        },
        { status: 403 }
      );
    }

    // Check if email service is available
    if (!emailService.isAvailable()) {
      return NextResponse.json(
        {
          success: false,
          error: 'EMAIL_SERVICE_UNAVAILABLE',
          message: 'Email service is not configured or unavailable',
        },
        { status: 503 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    
    // Check if it's a predefined group or custom recipients
    let recipients: Array<{ email: string; data: any }> = [];
    let emailType: EmailType;
    let options: any = {};

    if (body.group) {
      // Handle predefined recipient groups
      const groupValidation = RecipientGroupSchema.safeParse(body);
      if (!groupValidation.success) {
        return NextResponse.json(
          {
            success: false,
            error: 'INVALID_REQUEST',
            message: 'Invalid group request data',
            details: groupValidation.error.issues.map((issue) => ({
              field: issue.path.join('.'),
              message: issue.message,
            })),
          },
          { status: 400 }
        );
      }

      const { group, type, customData, options: groupOptions } = groupValidation.data;
      emailType = type as EmailType;
      options = groupOptions || {};

      // Get recipients based on group
      recipients = await getRecipientsByGroup(group, customData);
    } else {
      // Handle custom recipients
      const validation = BulkEmailSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          {
            success: false,
            error: 'INVALID_REQUEST',
            message: 'Invalid bulk email request data',
            details: validation.error.issues.map((issue) => ({
              field: issue.path.join('.'),
              message: issue.message,
            })),
          },
          { status: 400 }
        );
      }

      const { type, recipients: customRecipients, options: customOptions } = validation.data;
      emailType = type as EmailType;
      recipients = customRecipients;
      options = customOptions || {};
    }

    if (recipients.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'NO_RECIPIENTS',
          message: 'No recipients found for the specified criteria',
        },
        { status: 400 }
      );
    }

    // Send bulk emails
    const result = await emailService.sendBulkEmails(
      emailType,
      recipients,
      options
    );

    // Log bulk email operation
    console.log('Bulk email operation completed:', {
      type: emailType,
      totalRecipients: recipients.length,
      sent: result.sent,
      failed: result.failed,
      initiatedBy: session.user.email,
    });

    return NextResponse.json({
      success: result.success,
      message: `Bulk email operation completed`,
      stats: {
        totalRecipients: recipients.length,
        sent: result.sent,
        failed: result.failed,
        errors: result.errors.slice(0, 10), // Limit error details
      },
    });

  } catch (error) {
    console.error('Bulk email API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/emails/bulk - Get bulk email information
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        {
          success: false,
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'ADMIN_ACCESS_REQUIRED',
          message: 'Admin access required',
        },
        { status: 403 }
      );
    }

    // Get recipient group counts
    const [totalUsers, recentOrders, aiTryOnUsers] = await Promise.all([
      prisma.user.count(),
      prisma.order.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
      prisma.aiTryOnJob.count({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    ]);

    return NextResponse.json({
      success: true,
      recipientGroups: [
        {
          group: 'all-users',
          description: 'All registered users',
          count: totalUsers,
          supportedTypes: ['welcome'],
        },
        {
          group: 'recent-orders',
          description: 'Users with orders in the last 30 days',
          count: recentOrders,
          supportedTypes: ['welcome'],
        },
        {
          group: 'ai-tryon-users',
          description: 'Users who completed AI try-on in the last 30 days',
          count: aiTryOnUsers,
          supportedTypes: ['ai-tryon-ready', 'welcome'],
        },
      ],
      supportedTypes: [
        'welcome',
        'ai-tryon-ready',
        'order-confirmation',
        'order-shipped',
        'order-delivered',
      ],
      limits: {
        maxRecipientsPerRequest: 100,
        maxBatchSize: 50,
        minDelayBetweenBatches: 100,
        maxDelayBetweenBatches: 10000,
      },
      emailServiceStatus: {
        available: emailService.isAvailable(),
        connectionValid: emailService.isAvailable() ? await emailService.verifyConnection() : false,
      },
    });

  } catch (error) {
    console.error('Bulk email info API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * Get recipients by predefined group
 */
async function getRecipientsByGroup(
  group: string,
  customData?: any
): Promise<Array<{ email: string; data: any }>> {
  const baseData = {
    customerName: 'Valued Customer',
    ...customData,
  };

  switch (group) {
    case 'all-users':
      const allUsers = await prisma.user.findMany({
        select: { email: true, name: true },
        where: { email: { not: null } },
      });
      
      return allUsers.map(user => ({
        email: user.email!,
        data: {
          ...baseData,
          to: user.email!,
          customerName: user.name || 'Valued Customer',
        },
      }));

    case 'recent-orders':
      const recentOrderUsers = await prisma.user.findMany({
        select: { email: true, name: true },
        where: {
          email: { not: null },
          orders: {
            some: {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              },
            },
          },
        },
      });
      
      return recentOrderUsers.map(user => ({
        email: user.email!,
        data: {
          ...baseData,
          to: user.email!,
          customerName: user.name || 'Valued Customer',
        },
      }));

    case 'ai-tryon-users':
      const aiTryOnUsers = await prisma.user.findMany({
        select: { email: true, name: true },
        where: {
          email: { not: null },
          aiTryOnJobs: {
            some: {
              status: 'COMPLETED',
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              },
            },
          },
        },
      });
      
      return aiTryOnUsers.map(user => ({
        email: user.email!,
        data: {
          ...baseData,
          to: user.email!,
          customerName: user.name || 'Valued Customer',
        },
      }));

    default:
      return [];
  }
}
