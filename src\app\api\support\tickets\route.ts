/**
 * Support Tickets API Routes
 * Handles customer support ticket creation and management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { emailService } from '@/lib/services/email';

// Validation schemas
const CreateTicketSchema = z.object({
  subject: z.string().min(5).max(200),
  description: z.string().min(10).max(5000),
  category: z.enum(['order', 'product', 'technical', 'billing', 'general']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH']).optional().default('MEDIUM'),
});

const UpdateTicketSchema = z.object({
  status: z.enum(['OPEN', 'RESOLVED', 'CLOSED']).optional(),
  satisfactionRating: z.number().min(1).max(5).optional(),
  satisfactionFeedback: z.string().max(1000).optional(),
});

/**
 * POST /api/support/tickets - Create a new support ticket
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = CreateTicketSchema.parse(body);

    // Create the support ticket
    const ticket = await prisma.supportTicket.create({
      data: {
        subject: validatedData.subject,
        description: validatedData.description,
        category: validatedData.category || 'general',
        priority: validatedData.priority,
        userId: session.user.id,
        customerEmail: session.user.email || '',
        customerName: session.user.name || 'Customer',
        status: 'OPEN',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    // Create initial system message
    await prisma.supportMessage.create({
      data: {
        content: `Support ticket created: ${validatedData.subject}`,
        messageType: 'SYSTEM',
        senderId: session.user.id,
        senderRole: 'CUSTOMER',
        ticketId: ticket.id,
        readByCustomer: true,
        readByAdmin: false,
      },
    });

    // Send notification email to admins
    try {
      const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
      for (const adminEmail of adminEmails) {
        await emailService.sendEmail('support-ticket-created', {
          to: adminEmail.trim(),
          customerName: session.user.name || 'Customer',
          ticketId: ticket.id,
          subject: validatedData.subject,
          description: validatedData.description,
          priority: validatedData.priority,
          category: validatedData.category || 'general',
          ticketUrl: `${process.env.APP_URL}/admin/support/tickets/${ticket.id}`,
        });
      }
    } catch (emailError) {
      console.error('Failed to send admin notification email:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: ticket,
    });
  } catch (error) {
    console.error('Error creating support ticket:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create support ticket',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/support/tickets - Get user's support tickets
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const category = searchParams.get('category');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      userId: session.user.id,
    };

    if (status) {
      where.status = status;
    }

    if (category) {
      where.category = category;
    }

    // Get tickets with pagination
    const [tickets, total] = await Promise.all([
      prisma.supportTicket.findMany({
        where,
        include: {
          messages: {
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 1, // Only get the latest message for list view
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.supportTicket.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching support tickets:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch support tickets',
      },
      { status: 500 }
    );
  }
}
