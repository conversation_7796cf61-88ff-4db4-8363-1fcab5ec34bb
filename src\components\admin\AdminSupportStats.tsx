'use client';

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui';

interface SupportStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  closedTickets: number;
  averageResponseTime: string;
  recentActivity: number;
  byStatus: Record<string, number>;
  byPriority: Record<string, number>;
  byCategory: Record<string, number>;
  dailyStats?: Record<string, number>;
  satisfaction?: {
    averageRating: number;
    totalRatings: number;
  };
}

interface AdminSupportStatsProps {
  stats: SupportStats;
}

export function AdminSupportStats({ stats }: AdminSupportStatsProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-blue-500';
      case 'IN_PROGRESS':
        return 'bg-yellow-500';
      case 'WAITING_FOR_CUSTOMER':
        return 'bg-orange-500';
      case 'WAITING_FOR_ADMIN':
        return 'bg-purple-500';
      case 'RESOLVED':
        return 'bg-green-500';
      case 'CLOSED':
        return 'bg-gray-500';
      case 'CANCELLED':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-green-500';
      case 'MEDIUM':
        return 'bg-yellow-500';
      case 'HIGH':
        return 'bg-orange-500';
      case 'URGENT':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'order':
        return '📦';
      case 'product':
        return '👕';
      case 'technical':
        return '🔧';
      case 'billing':
        return '💳';
      case 'general':
        return '💬';
      default:
        return '❓';
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="text-yellow-400">⭐</span>);
    }
    
    if (hasHalfStar) {
      stars.push(<span key="half" className="text-yellow-400">⭐</span>);
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<span key={`empty-${i}`} className="text-gray-300">⭐</span>);
    }
    
    return stars;
  };

  return (
    <div className="space-y-8">
      {/* Status Breakdown */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📊 Ticket Status Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(stats.byStatus).map(([status, count]) => (
                <div key={status} className="text-center">
                  <div className={`w-16 h-16 ${getStatusColor(status)} rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-2`}>
                    {count}
                  </div>
                  <div className="text-sm font-medium text-gray-900 capitalize">
                    {status.replace('_', ' ')}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Priority and Category Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Priority Breakdown */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🚨 Priority Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats.byPriority).map(([priority, count]) => {
                  const percentage = (count / stats.totalTickets) * 100;
                  return (
                    <div key={priority}>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-gray-900">
                          {priority}
                        </span>
                        <span className="text-sm text-gray-600">
                          {count} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getPriorityColor(priority)}`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Category Breakdown */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📂 Category Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats.byCategory).map(([category, count]) => {
                  const percentage = (count / stats.totalTickets) * 100;
                  return (
                    <div key={category}>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-gray-900 flex items-center gap-2">
                          {getCategoryIcon(category)}
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </span>
                        <span className="text-sm text-gray-600">
                          {count} ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full bg-warm-500"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Response Time */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                ⏱️ Response Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-4xl font-bold text-warm-600 mb-2">
                  {stats.averageResponseTime}
                </div>
                <div className="text-sm text-gray-600 mb-4">
                  Average First Response Time
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="font-semibold text-green-800">Target</div>
                    <div className="text-green-600">&lt; 2 hours</div>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="font-semibold text-blue-800">Recent Activity</div>
                    <div className="text-blue-600">{stats.recentActivity} tickets (30d)</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Customer Satisfaction */}
        {stats.satisfaction && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  😊 Customer Satisfaction
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-4xl font-bold text-yellow-600 mb-2">
                    {stats.satisfaction.averageRating.toFixed(1)}
                  </div>
                  <div className="flex justify-center mb-2">
                    {renderStars(stats.satisfaction.averageRating)}
                  </div>
                  <div className="text-sm text-gray-600 mb-4">
                    Based on {stats.satisfaction.totalRatings} ratings
                  </div>
                  
                  <div className="bg-yellow-50 rounded-lg p-3">
                    <div className="text-sm">
                      <span className="font-semibold text-yellow-800">
                        {((stats.satisfaction.averageRating / 5) * 100).toFixed(1)}%
                      </span>
                      <span className="text-yellow-600 ml-1">satisfaction rate</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>

      {/* Key Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💡 Key Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-blue-800 font-semibold mb-1">Resolution Rate</div>
                <div className="text-2xl font-bold text-blue-600">
                  {((stats.resolvedTickets / stats.totalTickets) * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-blue-600">
                  {stats.resolvedTickets} of {stats.totalTickets} tickets resolved
                </div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-orange-800 font-semibold mb-1">Pending Action</div>
                <div className="text-2xl font-bold text-orange-600">
                  {stats.byStatus.WAITING_FOR_ADMIN || 0}
                </div>
                <div className="text-sm text-orange-600">
                  tickets waiting for admin response
                </div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-green-800 font-semibold mb-1">Active Workload</div>
                <div className="text-2xl font-bold text-green-600">
                  {(stats.byStatus.IN_PROGRESS || 0) + (stats.byStatus.WAITING_FOR_CUSTOMER || 0)}
                </div>
                <div className="text-sm text-green-600">
                  tickets in progress
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
