# Docker Production Environment Configuration
# Copy this to .env.production for production deployment

# Application Settings
NODE_ENV=production
APP_URL=http://localhost
PORT=3000

# Database Configuration
DATABASE_URL=****************************************************/ottiq_prod

# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost
NEXTAUTH_SECRET=your-production-secret-key-change-this-to-a-secure-random-string

# OAuth Providers (configure these for production)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# MinIO/S3 Configuration
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=ottiq_minio
MINIO_SECRET_KEY=ottiq_minio_password
MINIO_BUCKET_NAME=ottiq-uploads
MINIO_USE_SSL=false

# Redis Configuration
REDIS_URL=redis://:ottiq_redis_password@redis:6379

# Email Configuration
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>
SMTP_SECURE=false

# AI Services Configuration
HUGGING_FACE_API_KEY=your-hugging-face-api-key
OPENAI_API_KEY=your-openai-api-key

# AI Try-On Configuration
HUGGINGFACE_OOTD_SPACE_URL=https://huggingface.co/spaces/levihsu/OOTDiffusion
AI_IMAGE_TARGET_SIZE=512
AI_TRYON_RATE_LIMIT=10
MAX_CONCURRENT_AI_JOBS=5
AI_TRYON_TIMEOUT=120000

# Image Security Configuration
ENABLE_IMAGE_SECURITY_SCAN=false
IMAGE_SECURITY_SERVICE_URL=
IMAGE_SECURITY_API_KEY=

# File Upload Settings
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=900000

# Payment Configuration (bKash)
BKASH_APP_KEY=your-bkash-app-key
BKASH_APP_SECRET=your-bkash-app-secret
BKASH_USERNAME=your-bkash-username
BKASH_PASSWORD=your-bkash-password
BKASH_BASE_URL=https://tokenized.sandbox.bka.sh/v1.2.0-beta

# Monitoring and Logging
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Security Settings
ENCRYPTION_KEY=your-32-character-encryption-key-here
JWT_SECRET=your-jwt-secret-key-here

# Feature Flags
ENABLE_AI_TRYON=true
ENABLE_PAYMENT_GATEWAY=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_ANALYTICS=true

# Performance Settings
NEXT_TELEMETRY_DISABLED=1
DISABLE_SOURCE_MAPS=true
