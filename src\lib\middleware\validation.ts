/**
 * Input Validation and Sanitization Middleware for Ottiq
 * 
 * Provides comprehensive input validation, sanitization, and security
 * middleware for all API endpoints with XSS protection and data integrity.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z, ZodSchema, ZodError } from 'zod';
import { SecurityUtils } from '@/lib/security/env';

// Common validation schemas
export const CommonSchemas = {
  // Basic string validation
  safeString: z.string().min(1).max(1000).transform(SecurityUtils.sanitizeString),
  
  // Email validation
  email: z.string().email().transform(SecurityUtils.sanitizeString),
  
  // URL validation
  url: z.string().url().refine(SecurityUtils.isValidUrl, 'Invalid URL format'),
  
  // ID validation (UUID or MongoDB ObjectId)
  id: z.string().min(1).max(50).regex(/^[a-zA-Z0-9_-]+$/, 'Invalid ID format'),
  
  // Pagination
  pagination: z.object({
    page: z.number().int().min(1).max(1000).default(1),
    limit: z.number().int().min(1).max(100).default(20),
  }),
  
  // File upload validation
  fileUpload: z.object({
    filename: z.string().min(1).max(255),
    size: z.number().int().min(1).max(50 * 1024 * 1024), // 50MB max
    type: z.string().min(1).max(100),
  }),
  
  // Search query
  searchQuery: z.string().min(1).max(200).transform(SecurityUtils.sanitizeString),
  
  // Sort options
  sortOptions: z.object({
    field: z.string().min(1).max(50).regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, 'Invalid field name'),
    order: z.enum(['asc', 'desc']).default('asc'),
  }),
};

// Request validation options
interface ValidationOptions {
  body?: ZodSchema;
  query?: ZodSchema;
  params?: ZodSchema;
  headers?: ZodSchema;
  sanitize?: boolean;
  maxBodySize?: number;
}

/**
 * Create validation middleware for API routes
 */
export function createValidationMiddleware(options: ValidationOptions) {
  return async function validationMiddleware(
    request: NextRequest,
    context?: { params?: Record<string, string> }
  ): Promise<NextResponse | null> {
    try {
      const validatedData: Record<string, any> = {};
      
      // Validate request body
      if (options.body) {
        const body = await parseRequestBody(request, options.maxBodySize);
        const validatedBody = options.body.parse(body);
        validatedData.body = validatedBody;
      }
      
      // Validate query parameters
      if (options.query) {
        const query = parseQueryParams(request);
        const validatedQuery = options.query.parse(query);
        validatedData.query = validatedQuery;
      }
      
      // Validate route parameters
      if (options.params && context?.params) {
        const validatedParams = options.params.parse(context.params);
        validatedData.params = validatedParams;
      }
      
      // Validate headers
      if (options.headers) {
        const headers = parseHeaders(request);
        const validatedHeaders = options.headers.parse(headers);
        validatedData.headers = validatedHeaders;
      }
      
      // Store validated data in request for use in handlers
      (request as any).validatedData = validatedData;
      
      return null; // Continue to next middleware/handler
    } catch (error) {
      if (error instanceof ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation failed',
            code: 'VALIDATION_ERROR',
            details: error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message,
              code: err.code,
            })),
          },
          { status: 400 }
        );
      }
      
      console.error('Validation middleware error:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request format',
          code: 'INVALID_REQUEST',
        },
        { status: 400 }
      );
    }
  };
}

/**
 * Parse request body with size limits
 */
async function parseRequestBody(
  request: NextRequest,
  maxSize: number = 10 * 1024 * 1024 // 10MB default
): Promise<any> {
  const contentType = request.headers.get('content-type') || '';
  
  // Check content length
  const contentLength = request.headers.get('content-length');
  if (contentLength && parseInt(contentLength, 10) > maxSize) {
    throw new Error('Request body too large');
  }
  
  if (contentType.includes('application/json')) {
    try {
      return await request.json();
    } catch (error) {
      throw new Error('Invalid JSON format');
    }
  }
  
  if (contentType.includes('multipart/form-data')) {
    try {
      const formData = await request.formData();
      const data: Record<string, any> = {};
      
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          // Validate file
          if (value.size > maxSize) {
            throw new Error(`File ${key} too large`);
          }
          data[key] = {
            filename: value.name,
            size: value.size,
            type: value.type,
            file: value,
          };
        } else {
          data[key] = value;
        }
      }
      
      return data;
    } catch (error) {
      throw new Error('Invalid form data');
    }
  }
  
  if (contentType.includes('application/x-www-form-urlencoded')) {
    try {
      const formData = await request.formData();
      const data: Record<string, string> = {};
      
      for (const [key, value] of formData.entries()) {
        data[key] = value.toString();
      }
      
      return data;
    } catch (error) {
      throw new Error('Invalid form data');
    }
  }
  
  // For other content types, return empty object
  return {};
}

/**
 * Parse query parameters
 */
function parseQueryParams(request: NextRequest): Record<string, any> {
  const { searchParams } = new URL(request.url);
  const query: Record<string, any> = {};
  
  for (const [key, value] of searchParams.entries()) {
    // Handle array parameters (e.g., ?tags=a&tags=b)
    if (query[key]) {
      if (Array.isArray(query[key])) {
        query[key].push(value);
      } else {
        query[key] = [query[key], value];
      }
    } else {
      // Try to parse as number or boolean
      if (value === 'true') {
        query[key] = true;
      } else if (value === 'false') {
        query[key] = false;
      } else if (/^\d+$/.test(value)) {
        query[key] = parseInt(value, 10);
      } else if (/^\d+\.\d+$/.test(value)) {
        query[key] = parseFloat(value);
      } else {
        query[key] = value;
      }
    }
  }
  
  return query;
}

/**
 * Parse relevant headers
 */
function parseHeaders(request: NextRequest): Record<string, string> {
  const relevantHeaders = [
    'authorization',
    'content-type',
    'user-agent',
    'accept',
    'accept-language',
    'x-api-key',
    'x-request-id',
  ];
  
  const headers: Record<string, string> = {};
  
  for (const header of relevantHeaders) {
    const value = request.headers.get(header);
    if (value) {
      headers[header] = value;
    }
  }
  
  return headers;
}

/**
 * Sanitization middleware
 */
export function sanitizationMiddleware(request: NextRequest): NextResponse | null {
  // Add request ID for tracing
  const requestId = SecurityUtils.generateSecureToken(16);
  (request as any).requestId = requestId;
  
  // Log request for security monitoring
  const clientIP = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown';
  
  console.log(`[${requestId}] ${request.method} ${request.url} from ${clientIP}`);
  
  return null; // Continue processing
}

/**
 * File upload validation middleware
 */
export function createFileUploadMiddleware(options: {
  maxSize?: number;
  allowedTypes?: string[];
  maxFiles?: number;
}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
    maxFiles = 5,
  } = options;
  
  return async function fileUploadMiddleware(
    request: NextRequest
  ): Promise<NextResponse | null> {
    const contentType = request.headers.get('content-type') || '';
    
    if (!contentType.includes('multipart/form-data')) {
      return null; // Not a file upload, continue
    }
    
    try {
      const formData = await request.formData();
      const files: File[] = [];
      
      // Collect all files
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          files.push(value);
        }
      }
      
      // Validate file count
      if (files.length > maxFiles) {
        return NextResponse.json(
          {
            success: false,
            error: 'Too many files',
            code: 'FILE_COUNT_EXCEEDED',
            details: { maxFiles, received: files.length },
          },
          { status: 400 }
        );
      }
      
      // Validate each file
      for (const file of files) {
        // Check file size
        if (file.size > maxSize) {
          return NextResponse.json(
            {
              success: false,
              error: 'File too large',
              code: 'FILE_SIZE_EXCEEDED',
              details: { 
                filename: file.name,
                size: file.size,
                maxSize,
              },
            },
            { status: 400 }
          );
        }
        
        // Check file type
        if (!allowedTypes.includes(file.type)) {
          return NextResponse.json(
            {
              success: false,
              error: 'File type not allowed',
              code: 'FILE_TYPE_NOT_ALLOWED',
              details: {
                filename: file.name,
                type: file.type,
                allowedTypes,
              },
            },
            { status: 400 }
          );
        }
        
        // Additional security checks
        if (!SecurityUtils.isValidFileType(file.name, ['.jpg', '.jpeg', '.png', '.webp'])) {
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid file extension',
              code: 'INVALID_FILE_EXTENSION',
              details: { filename: file.name },
            },
            { status: 400 }
          );
        }
      }
      
      return null; // All validations passed, continue
    } catch (error) {
      console.error('File upload validation error:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'File upload validation failed',
          code: 'FILE_UPLOAD_ERROR',
        },
        { status: 400 }
      );
    }
  };
}

/**
 * Get validated data from request
 */
export function getValidatedData(request: NextRequest): Record<string, any> {
  return (request as any).validatedData || {};
}

/**
 * Get request ID
 */
export function getRequestId(request: NextRequest): string {
  return (request as any).requestId || 'unknown';
}

/**
 * Comprehensive API middleware wrapper
 */
export function createSecureApiHandler<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>,
  options: {
    validation?: ValidationOptions;
    rateLimit?: 'general' | 'pricing' | 'upload' | 'auth' | 'aiTryOn' | 'admin';
    requireAuth?: boolean;
    requireAdmin?: boolean;
    fileUpload?: {
      maxSize?: number;
      allowedTypes?: string[];
      maxFiles?: number;
    };
  } = {}
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    const requestId = SecurityUtils.generateSecureToken(16);
    (request as any).requestId = requestId;

    try {
      // 1. Sanitization
      const sanitizationResponse = sanitizationMiddleware(request);
      if (sanitizationResponse) return sanitizationResponse;

      // 2. Rate limiting
      if (options.rateLimit) {
        const { rateLimiters } = await import('./rateLimiter');
        const rateLimitResponse = await rateLimiters[options.rateLimit](request);
        if (rateLimitResponse) return rateLimitResponse;
      }

      // 3. CORS handling
      const { corsMiddleware } = await import('./cors');
      const corsResponse = corsMiddleware(request);
      if (corsResponse) return corsResponse;

      // 4. File upload validation
      if (options.fileUpload) {
        const fileUploadMiddleware = createFileUploadMiddleware(options.fileUpload);
        const fileUploadResponse = await fileUploadMiddleware(request);
        if (fileUploadResponse) return fileUploadResponse;
      }

      // 5. Input validation
      if (options.validation) {
        const validationMiddleware = createValidationMiddleware(options.validation);
        const validationResponse = await validationMiddleware(request, args[0] as any);
        if (validationResponse) return validationResponse;
      }

      // 6. Authentication check
      if (options.requireAuth || options.requireAdmin) {
        // This would integrate with NextAuth - simplified for now
        const authHeader = request.headers.get('authorization');
        if (!authHeader) {
          return NextResponse.json(
            {
              success: false,
              error: 'Authentication required',
              code: 'AUTH_REQUIRED',
            },
            { status: 401 }
          );
        }
      }

      // Execute the handler
      const response = await handler(request, ...args);

      // Add security headers to response
      const { addCorsHeaders } = await import('./cors');
      return addCorsHeaders(response, request);

    } catch (error) {
      console.error(`[${requestId}] API handler error:`, error);

      const errorResponse = NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
          code: 'INTERNAL_ERROR',
          requestId,
        },
        { status: 500 }
      );

      // Add CORS headers even to error responses
      const { addCorsHeaders } = await import('./cors');
      return addCorsHeaders(errorResponse, request);
    }
  };
}
