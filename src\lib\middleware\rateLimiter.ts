/**
 * Redis-based Rate Limiting Middleware for Ottiq
 * 
 * Provides flexible rate limiting for different API endpoints with
 * Redis backend for distributed rate limiting across multiple instances.
 */

import { NextRequest, NextResponse } from 'next/server';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { getRedisClient } from '@/lib/redis';

// Rate limit configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // General API endpoints
  general: {
    points: 100, // Number of requests
    duration: 900, // Per 15 minutes (900 seconds)
    blockDuration: 900, // Block for 15 minutes after limit exceeded
  },
  
  // Pricing API - more restrictive
  pricing: {
    points: 50,
    duration: 900, // Per 15 minutes
    blockDuration: 1800, // Block for 30 minutes
  },
  
  // File upload endpoints
  upload: {
    points: 10,
    duration: 3600, // Per hour
    blockDuration: 3600, // Block for 1 hour
  },
  
  // Authentication endpoints
  auth: {
    points: 20,
    duration: 900, // Per 15 minutes
    blockDuration: 1800, // Block for 30 minutes
  },
  
  // AI Try-On endpoints (additional to user-based limits)
  aiTryOn: {
    points: 5,
    duration: 3600, // Per hour
    blockDuration: 7200, // Block for 2 hours
  },
  
  // Admin endpoints - more lenient for legitimate admin use
  admin: {
    points: 200,
    duration: 900, // Per 15 minutes
    blockDuration: 900, // Block for 15 minutes
  },
} as const;

export type RateLimitType = keyof typeof RATE_LIMIT_CONFIGS;

/**
 * Rate Limiter Manager
 */
class RateLimiterManager {
  private limiters: Map<RateLimitType, RateLimiterRedis> = new Map();
  private fallbackLimiters: Map<string, { count: number; resetTime: number }> = new Map();
  private isRedisAvailable = false;

  /**
   * Initialize rate limiters
   */
  async initialize(): Promise<void> {
    try {
      const redisClient = await getRedisClient();
      
      if (redisClient) {
        this.isRedisAvailable = true;
        
        // Create Redis-based rate limiters
        for (const [type, config] of Object.entries(RATE_LIMIT_CONFIGS)) {
          this.limiters.set(type as RateLimitType, new RateLimiterRedis({
            storeClient: redisClient,
            keyPrefix: `rl:${type}:`,
            points: config.points,
            duration: config.duration,
            blockDuration: config.blockDuration,
            execEvenly: true, // Spread requests evenly across duration
          }));
        }
        
        console.log('Rate limiters initialized with Redis backend');
      } else {
        console.warn('Redis not available, using in-memory fallback rate limiting');
        this.isRedisAvailable = false;
      }
    } catch (error) {
      console.error('Failed to initialize rate limiters:', error);
      this.isRedisAvailable = false;
    }
  }

  /**
   * Check rate limit for a key
   */
  async checkLimit(
    type: RateLimitType,
    key: string
  ): Promise<{
    allowed: boolean;
    totalHits: number;
    remainingPoints: number;
    msBeforeNext: number;
    resetTime: Date;
  }> {
    if (this.isRedisAvailable && this.limiters.has(type)) {
      return this.checkRedisLimit(type, key);
    } else {
      return this.checkFallbackLimit(type, key);
    }
  }

  /**
   * Redis-based rate limiting
   */
  private async checkRedisLimit(type: RateLimitType, key: string) {
    try {
      const limiter = this.limiters.get(type)!;
      const result = await limiter.consume(key);
      
      return {
        allowed: true,
        totalHits: result.totalHits,
        remainingPoints: result.remainingPoints || 0,
        msBeforeNext: result.msBeforeNext || 0,
        resetTime: new Date(Date.now() + (result.msBeforeNext || 0)),
      };
    } catch (rejRes: any) {
      // Rate limit exceeded
      return {
        allowed: false,
        totalHits: rejRes.totalHits || 0,
        remainingPoints: 0,
        msBeforeNext: rejRes.msBeforeNext || 0,
        resetTime: new Date(Date.now() + (rejRes.msBeforeNext || 0)),
      };
    }
  }

  /**
   * In-memory fallback rate limiting
   */
  private checkFallbackLimit(type: RateLimitType, key: string) {
    const config = RATE_LIMIT_CONFIGS[type];
    const now = Date.now();
    const windowStart = Math.floor(now / (config.duration * 1000)) * (config.duration * 1000);
    const fallbackKey = `${type}:${key}:${windowStart}`;
    
    const existing = this.fallbackLimiters.get(fallbackKey);
    const currentCount = existing ? existing.count : 0;
    
    if (currentCount >= config.points) {
      return {
        allowed: false,
        totalHits: currentCount,
        remainingPoints: 0,
        msBeforeNext: windowStart + (config.duration * 1000) - now,
        resetTime: new Date(windowStart + (config.duration * 1000)),
      };
    }
    
    // Increment counter
    this.fallbackLimiters.set(fallbackKey, {
      count: currentCount + 1,
      resetTime: windowStart + (config.duration * 1000),
    });
    
    // Cleanup old entries
    this.cleanupFallbackLimiters();
    
    return {
      allowed: true,
      totalHits: currentCount + 1,
      remainingPoints: config.points - (currentCount + 1),
      msBeforeNext: windowStart + (config.duration * 1000) - now,
      resetTime: new Date(windowStart + (config.duration * 1000)),
    };
  }

  /**
   * Cleanup expired fallback entries
   */
  private cleanupFallbackLimiters(): void {
    const now = Date.now();
    for (const [key, data] of this.fallbackLimiters.entries()) {
      if (data.resetTime < now) {
        this.fallbackLimiters.delete(key);
      }
    }
  }
}

// Singleton instance
const rateLimiterManager = new RateLimiterManager();

/**
 * Initialize rate limiter (call once at startup)
 */
export async function initializeRateLimiter(): Promise<void> {
  await rateLimiterManager.initialize();
}

/**
 * Get client identifier from request
 */
function getClientIdentifier(request: NextRequest): string {
  // Try to get real IP from headers (for reverse proxy setups)
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = forwardedFor?.split(',')[0] || realIP || request.ip || 'unknown';
  
  return clientIP;
}

/**
 * Rate limiting middleware factory
 */
export function createRateLimitMiddleware(type: RateLimitType) {
  return async function rateLimitMiddleware(
    request: NextRequest,
    identifier?: string
  ): Promise<NextResponse | null> {
    try {
      const key = identifier || getClientIdentifier(request);
      const result = await rateLimiterManager.checkLimit(type, key);
      
      if (!result.allowed) {
        // Rate limit exceeded
        const response = NextResponse.json(
          {
            success: false,
            error: 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            data: {
              type,
              resetTime: result.resetTime,
              retryAfter: Math.ceil(result.msBeforeNext / 1000),
            },
          },
          { status: 429 }
        );
        
        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', RATE_LIMIT_CONFIGS[type].points.toString());
        response.headers.set('X-RateLimit-Remaining', '0');
        response.headers.set('X-RateLimit-Reset', result.resetTime.toISOString());
        response.headers.set('Retry-After', Math.ceil(result.msBeforeNext / 1000).toString());
        
        return response;
      }
      
      // Add rate limit headers to successful requests
      const response = NextResponse.next();
      response.headers.set('X-RateLimit-Limit', RATE_LIMIT_CONFIGS[type].points.toString());
      response.headers.set('X-RateLimit-Remaining', result.remainingPoints.toString());
      response.headers.set('X-RateLimit-Reset', result.resetTime.toISOString());
      
      return null; // Continue to next middleware/handler
    } catch (error) {
      console.error('Rate limiting error:', error);
      // On error, allow the request to continue (fail open)
      return null;
    }
  };
}

/**
 * Convenience functions for common rate limit types
 */
export const rateLimiters = {
  general: createRateLimitMiddleware('general'),
  pricing: createRateLimitMiddleware('pricing'),
  upload: createRateLimitMiddleware('upload'),
  auth: createRateLimitMiddleware('auth'),
  aiTryOn: createRateLimitMiddleware('aiTryOn'),
  admin: createRateLimitMiddleware('admin'),
};

/**
 * Check rate limit without consuming (for preview/checking purposes)
 */
export async function checkRateLimit(
  type: RateLimitType,
  identifier: string
): Promise<{
  allowed: boolean;
  remainingPoints: number;
  resetTime: Date;
}> {
  try {
    const result = await rateLimiterManager.checkLimit(type, identifier);
    return {
      allowed: result.allowed,
      remainingPoints: result.remainingPoints,
      resetTime: result.resetTime,
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    return {
      allowed: true,
      remainingPoints: RATE_LIMIT_CONFIGS[type].points,
      resetTime: new Date(Date.now() + RATE_LIMIT_CONFIGS[type].duration * 1000),
    };
  }
}
