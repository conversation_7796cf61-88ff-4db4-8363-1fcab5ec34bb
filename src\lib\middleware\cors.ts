/**
 * CORS Middleware for Ottiq API Routes
 * 
 * Provides configurable CORS handling for different API endpoints
 * with security-first defaults and environment-specific configurations.
 */

import { NextRequest, NextResponse } from 'next/server';

// CORS configuration
const CORS_CONFIG = {
  // Allowed origins (environment-specific)
  allowedOrigins: process.env.NODE_ENV === 'production' 
    ? [
        'https://ottiq.com',
        'https://www.ottiq.com',
        'https://admin.ottiq.com',
      ]
    : [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'http://localhost:3001', // For admin panel
      ],
  
  // Allowed methods
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  
  // Allowed headers
  allowedHeaders: [
    'Accept',
    'Accept-Version',
    'Authorization',
    'Content-Length',
    'Content-MD5',
    'Content-Type',
    'Date',
    'X-Api-Version',
    'X-CSRF-Token',
    'X-Requested-With',
  ],
  
  // Exposed headers
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'X-Request-ID',
  ],
  
  // Credentials
  credentials: true,
  
  // Preflight cache duration (24 hours)
  maxAge: 86400,
} as const;

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin: string | null): boolean {
  if (!origin) return false;
  
  // Allow same-origin requests
  if (origin === process.env.NEXTAUTH_URL || origin === process.env.APP_URL) {
    return true;
  }
  
  return CORS_CONFIG.allowedOrigins.includes(origin);
}

/**
 * CORS middleware for API routes
 */
export function corsMiddleware(request: NextRequest): NextResponse | null {
  const origin = request.headers.get('origin');
  const method = request.method;
  
  // Handle preflight requests
  if (method === 'OPTIONS') {
    const response = new NextResponse(null, { status: 200 });
    
    // Set CORS headers for preflight
    if (origin && isOriginAllowed(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
    
    response.headers.set('Access-Control-Allow-Credentials', CORS_CONFIG.credentials.toString());
    response.headers.set('Access-Control-Allow-Methods', CORS_CONFIG.allowedMethods.join(', '));
    response.headers.set('Access-Control-Allow-Headers', CORS_CONFIG.allowedHeaders.join(', '));
    response.headers.set('Access-Control-Max-Age', CORS_CONFIG.maxAge.toString());
    
    return response;
  }
  
  // For non-preflight requests, return null to continue processing
  return null;
}

/**
 * Add CORS headers to response
 */
export function addCorsHeaders(response: NextResponse, request: NextRequest): NextResponse {
  const origin = request.headers.get('origin');
  
  // Set CORS headers
  if (origin && isOriginAllowed(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }
  
  response.headers.set('Access-Control-Allow-Credentials', CORS_CONFIG.credentials.toString());
  response.headers.set('Access-Control-Expose-Headers', CORS_CONFIG.exposedHeaders.join(', '));
  
  // Add Vary header for proper caching
  response.headers.set('Vary', 'Origin');
  
  return response;
}

/**
 * CORS wrapper for API route handlers
 */
export function withCors<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    // Handle preflight requests
    const corsResponse = corsMiddleware(request);
    if (corsResponse) {
      return corsResponse;
    }
    
    try {
      // Execute the handler
      const response = await handler(request, ...args);
      
      // Add CORS headers to the response
      return addCorsHeaders(response, request);
    } catch (error) {
      // Handle errors and still add CORS headers
      console.error('API handler error:', error);
      
      const errorResponse = NextResponse.json(
        {
          success: false,
          error: 'Internal server error',
          message: 'An unexpected error occurred',
        },
        { status: 500 }
      );
      
      return addCorsHeaders(errorResponse, request);
    }
  };
}

/**
 * Validate request origin for sensitive operations
 */
export function validateOrigin(request: NextRequest): boolean {
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');
  
  // For same-origin requests, origin might be null
  if (!origin && !referer) {
    // Allow requests without origin/referer (e.g., server-to-server)
    return true;
  }
  
  // Check origin
  if (origin && !isOriginAllowed(origin)) {
    return false;
  }
  
  // Check referer as fallback
  if (referer) {
    try {
      const refererUrl = new URL(referer);
      const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
      return isOriginAllowed(refererOrigin);
    } catch {
      return false;
    }
  }
  
  return true;
}

/**
 * Security middleware for sensitive API endpoints
 */
export function securityMiddleware(request: NextRequest): NextResponse | null {
  // Validate origin
  if (!validateOrigin(request)) {
    return NextResponse.json(
      {
        success: false,
        error: 'Forbidden',
        message: 'Request origin not allowed',
      },
      { status: 403 }
    );
  }
  
  // Check for required security headers in production
  if (process.env.NODE_ENV === 'production') {
    const userAgent = request.headers.get('user-agent');
    
    // Block requests without user agent (potential bot/scraper)
    if (!userAgent) {
      return NextResponse.json(
        {
          success: false,
          error: 'Bad Request',
          message: 'User agent required',
        },
        { status: 400 }
      );
    }
    
    // Block suspicious user agents
    const suspiciousPatterns = [
      /curl/i,
      /wget/i,
      /python/i,
      /bot/i,
      /crawler/i,
      /spider/i,
    ];
    
    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
    if (isSuspicious) {
      console.warn('Suspicious user agent blocked:', userAgent);
      return NextResponse.json(
        {
          success: false,
          error: 'Forbidden',
          message: 'Access denied',
        },
        { status: 403 }
      );
    }
  }
  
  return null;
}

/**
 * Combined security and CORS middleware
 */
export function createSecureApiHandler<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return withCors(async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    // Apply security checks
    const securityResponse = securityMiddleware(request);
    if (securityResponse) {
      return securityResponse;
    }
    
    // Execute the handler
    return handler(request, ...args);
  });
}

/**
 * Get CORS configuration for debugging
 */
export function getCorsConfig() {
  return {
    ...CORS_CONFIG,
    environment: process.env.NODE_ENV,
    currentOrigin: process.env.NEXTAUTH_URL || process.env.APP_URL,
  };
}
