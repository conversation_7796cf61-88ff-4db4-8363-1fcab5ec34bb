/**
 * Welcome Email Template
 * Introduces new users to the Ottiq platform with inspiration and guidance
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { WelcomeEmailData } from '../../lib/services/email';

interface WelcomeEmailProps extends WelcomeEmailData {}

export function WelcomeEmail({ customerName }: WelcomeEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText="Welcome to Ottiq! Your journey to unique style starts here.">
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-welcome.jpg`}
          alt="Welcome to Ottiq - Where imagination meets fashion"
          width="600"
          height="300"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          Welcome to Ottiq, {customerName}! 👋
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          You've just joined a community of style creators who believe fashion should be as unique as you are. Let's turn your imagination into wearable art!
        </EmailText>
      </Section>

      {/* What Makes Ottiq Special */}
      <Section className="mb-8">
        <EmailHeading level={2}>Why You'll Love Ottiq</EmailHeading>
        
        <div className="space-y-6">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="text-primary-600 text-xl">🎨</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-2">
                Design Your Dreams
              </Text>
              <Text className="text-gray-600 text-sm">
                Use our intuitive editor to create custom designs, or choose from our curated templates inspired by street style, minimalism, and bold expressions.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-secondary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="text-secondary-600 text-xl">🤳</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-2">
                AI Try-On Magic
              </Text>
              <Text className="text-gray-600 text-sm">
                See how your designs look on you before ordering! Our AI try-on technology lets you visualize your style in real-time.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="text-accent-600 text-xl">✨</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-2">
                Premium Quality
              </Text>
              <Text className="text-gray-600 text-sm">
                Every piece is crafted with premium fabrics and attention to detail. Your unique style deserves nothing less than perfection.
              </Text>
            </div>
          </div>
        </div>
      </Section>

      {/* Get Started Steps */}
      <Section className="bg-gradient-to-r from-primary-50 to-warm-50 rounded-lg p-6 mb-8">
        <EmailHeading level={2}>Ready to Create? Here's How:</EmailHeading>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="font-bold text-sm">1</Text>
            </div>
            <Text className="text-gray-700">
              <strong>Choose Your Canvas:</strong> Pick from t-shirts, hoodies, dresses, and more
            </Text>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="font-bold text-sm">2</Text>
            </div>
            <Text className="text-gray-700">
              <strong>Design Your Style:</strong> Add text, images, or use our templates
            </Text>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="font-bold text-sm">3</Text>
            </div>
            <Text className="text-gray-700">
              <strong>Try It On:</strong> Use AI to see how it looks on you
            </Text>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center flex-shrink-0">
              <Text className="font-bold text-sm">4</Text>
            </div>
            <Text className="text-gray-700">
              <strong>Order & Enjoy:</strong> We'll craft and deliver your unique piece
            </Text>
          </div>
        </div>
      </Section>

      {/* Featured Templates */}
      <Section className="mb-8">
        <EmailHeading level={2}>Get Inspired</EmailHeading>
        <EmailText>
          Not sure where to start? Check out these popular design styles:
        </EmailText>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center">
            <Img
              src={`${baseUrl}/images/template-streetwear.jpg`}
              alt="Bold Streetwear Style"
              width="250"
              height="200"
              className="w-full rounded-lg mb-2"
            />
            <Text className="text-sm font-semibold text-gray-900">Bold Streetwear</Text>
            <Text className="text-xs text-gray-600">Urban, confident, edgy</Text>
          </div>

          <div className="text-center">
            <Img
              src={`${baseUrl}/images/template-minimalist.jpg`}
              alt="Minimalist Chic Style"
              width="250"
              height="200"
              className="w-full rounded-lg mb-2"
            />
            <Text className="text-sm font-semibold text-gray-900">Minimalist Chic</Text>
            <Text className="text-xs text-gray-600">Clean, elegant, timeless</Text>
          </div>
        </div>

        <div className="text-center">
          <EmailButton href={`${baseUrl}/templates`} variant="outline">
            Browse All Templates
          </EmailButton>
        </div>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-8">
        <EmailHeading level={2}>Ready to Create Your First Design?</EmailHeading>
        <EmailText>
          Your unique style is waiting to be discovered. Let's create something amazing together!
        </EmailText>
        
        <EmailButton href={`${baseUrl}/create`} variant="primary">
          Start Designing Now
        </EmailButton>
      </Section>

      {/* Community */}
      <Section className="bg-gray-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>Join the Style Community</EmailHeading>
        <EmailText>
          Follow us for daily inspiration, styling tips, and to see how other creators are expressing their unique style.
        </EmailText>
        
        <Text className="text-sm text-gray-600 mb-4">
          Tag your creations with #OttiqStyle to be featured!
        </Text>

        <div className="flex justify-center space-x-4">
          <EmailButton href="https://instagram.com/ottiq" variant="outline">
            Follow on Instagram
          </EmailButton>
        </div>
      </Section>

      {/* Support */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          Have questions? Our team is here to help you every step of the way.
        </EmailText>
        <EmailButton href={`${baseUrl}/support`} variant="outline">
          Get Help
        </EmailButton>
      </Section>
    </EmailLayout>
  );
}
