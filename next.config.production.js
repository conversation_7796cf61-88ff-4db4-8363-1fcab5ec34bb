/**
 * Production-specific Next.js Configuration for Ottiq
 * 
 * This configuration is optimized for production deployment with
 * enhanced security, performance, and monitoring capabilities.
 */

/** @type {import('next').NextConfig} */
const productionConfig = {
  // App Router configuration
  experimental: {
    appDir: true,
    // Enable server components optimization
    serverComponentsExternalPackages: ['sharp', 'prisma'],
    // Optimize bundle splitting
    optimizePackageImports: ['@heroicons/react', 'lucide-react'],
  },

  // Image optimization
  images: {
    domains: [
      'ottiq.com',
      'www.ottiq.com',
      'cdn.ottiq.com',
      'storage.ottiq.com',
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 3600, // 1 hour
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Security settings
  reactStrictMode: true,
  poweredByHeader: false,
  productionBrowserSourceMaps: false,
  
  // Performance optimizations
  swcMinify: true,
  compress: true,
  
  // Generate unique build IDs for cache busting
  generateBuildId: async () => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `build-${timestamp}`;
  },

  // Output configuration
  output: 'standalone',
  
  // Webpack configuration
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Production-specific webpack optimizations
    if (!dev) {
      // Remove console.log in production (keep console.error and console.warn)
      config.optimization.minimizer.push(
        new webpack.DefinePlugin({
          'console.log': 'function(){}',
        })
      );

      // Bundle analyzer (optional - enable for debugging)
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: 'bundle-analyzer-report.html',
          })
        );
      }
    }

    // Security: Prevent source map exposure
    config.devtool = false;

    return config;
  },

  // Environment variables exposed to client (be very careful here)
  env: {
    NEXT_PUBLIC_APP_URL: process.env.APP_URL,
    NEXT_PUBLIC_NODE_ENV: 'production',
  },

  // Redirects for SEO and security
  async redirects() {
    return [
      // Redirect HTTP to HTTPS
      {
        source: '/:path*',
        has: [
          {
            type: 'header',
            key: 'x-forwarded-proto',
            value: 'http',
          },
        ],
        destination: 'https://ottiq.com/:path*',
        permanent: true,
      },
      // Redirect www to non-www
      {
        source: '/:path*',
        has: [
          {
            type: 'host',
            value: 'www.ottiq.com',
          },
        ],
        destination: 'https://ottiq.com/:path*',
        permanent: true,
      },
    ];
  },

  // Security headers
  async headers() {
    const securityHeaders = [
      {
        key: 'X-Frame-Options',
        value: 'DENY',
      },
      {
        key: 'X-Content-Type-Options',
        value: 'nosniff',
      },
      {
        key: 'X-XSS-Protection',
        value: '1; mode=block',
      },
      {
        key: 'Referrer-Policy',
        value: 'strict-origin-when-cross-origin',
      },
      {
        key: 'Permissions-Policy',
        value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
      },
      {
        key: 'Strict-Transport-Security',
        value: 'max-age=********; includeSubDomains; preload',
      },
      {
        key: 'Content-Security-Policy',
        value: [
          "default-src 'self'",
          "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://accounts.google.com https://apis.google.com https://connect.facebook.net",
          "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
          "font-src 'self' https://fonts.gstatic.com",
          "img-src 'self' data: blob: https: http:",
          "media-src 'self' blob:",
          "connect-src 'self' https://api.ottiq.com https://huggingface.co https://accounts.google.com https://graph.facebook.com",
          "frame-src 'self' https://accounts.google.com https://www.facebook.com",
          "object-src 'none'",
          "base-uri 'self'",
          "form-action 'self'",
          "frame-ancestors 'none'",
          "upgrade-insecure-requests",
        ].join('; '),
      },
      {
        key: 'X-DNS-Prefetch-Control',
        value: 'on',
      },
    ];

    return [
      // Apply security headers to all routes
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
      // API routes - no cache
      {
        source: '/api/(.*)',
        headers: [
          ...securityHeaders,
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      // Static assets - long cache
      {
        source: '/(_next/static|favicon.ico|robots.txt|sitemap.xml)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=********, immutable',
          },
        ],
      },
      // Images - medium cache
      {
        source: '/(images|uploads)/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, s-maxage=86400',
          },
        ],
      },
    ];
  },

  // Rewrites for API versioning and routing
  async rewrites() {
    return [
      // API versioning
      {
        source: '/api/v1/:path*',
        destination: '/api/:path*',
      },
    ];
  },

  // Custom error pages
  async rewrites() {
    return {
      beforeFiles: [],
      afterFiles: [
        // Custom 404 handling
        {
          source: '/404',
          destination: '/not-found',
        },
      ],
      fallback: [],
    };
  },

  // Logging configuration
  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  // TypeScript configuration
  typescript: {
    // Fail build on type errors in production
    ignoreBuildErrors: false,
  },

  // ESLint configuration
  eslint: {
    // Fail build on lint errors in production
    ignoreDuringBuilds: false,
  },

  // Experimental features for production
  experimental: {
    ...this.experimental,
    // Enable server actions
    serverActions: true,
    // Optimize server components
    serverComponentsExternalPackages: ['sharp', 'prisma', 'bcrypt'],
    // Enable partial prerendering
    ppr: false, // Enable when stable
  },
};

module.exports = productionConfig;
