/**
 * Support Tickets API Tests
 * Tests for customer support ticket creation and management
 */

import { NextRequest } from 'next/server';
import { POST, GET } from '@/app/api/support/tickets/route';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';

// Mock dependencies
jest.mock('next-auth');
jest.mock('@/lib/prisma', () => ({
  prisma: {
    supportTicket: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
    supportMessage: {
      create: jest.fn(),
    },
  },
}));

jest.mock('@/lib/services/email', () => ({
  emailService: {
    sendEmail: jest.fn().mockResolvedValue({ success: true, messageId: 'test-id' }),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('/api/support/tickets', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST - Create Support Ticket', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
    };

    const validTicketData = {
      subject: 'Test Support Issue',
      description: 'This is a test support ticket description that is long enough to pass validation.',
      category: 'technical',
      priority: 'MEDIUM',
    };

    it('should create a support ticket successfully', async () => {
      // Mock authenticated user
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      // Mock database responses
      const mockTicket = {
        id: 'ticket-123',
        ...validTicketData,
        userId: mockUser.id,
        customerEmail: mockUser.email,
        customerName: mockUser.name,
        status: 'OPEN',
        user: mockUser,
        messages: [],
      };

      mockPrisma.supportTicket.create.mockResolvedValue(mockTicket as any);
      mockPrisma.supportMessage.create.mockResolvedValue({} as any);

      // Create request
      const request = new NextRequest('http://localhost:3000/api/support/tickets', {
        method: 'POST',
        body: JSON.stringify(validTicketData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Execute
      const response = await POST(request);
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockTicket);
      expect(mockPrisma.supportTicket.create).toHaveBeenCalledWith({
        data: {
          subject: validTicketData.subject,
          description: validTicketData.description,
          category: validTicketData.category,
          priority: validTicketData.priority,
          userId: mockUser.id,
          customerEmail: mockUser.email,
          customerName: mockUser.name,
          status: 'OPEN',
        },
        include: expect.any(Object),
      });
      expect(mockPrisma.supportMessage.create).toHaveBeenCalled();
    });

    it('should require authentication', async () => {
      // Mock unauthenticated user
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/support/tickets', {
        method: 'POST',
        body: JSON.stringify(validTicketData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });

    it('should validate required fields', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      const invalidData = {
        subject: 'Hi', // Too short
        description: 'Short', // Too short
      };

      const request = new NextRequest('http://localhost:3000/api/support/tickets', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Validation failed');
      expect(data.details).toBeDefined();
    });

    it('should handle database errors', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      mockPrisma.supportTicket.create.mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/support/tickets', {
        method: 'POST',
        body: JSON.stringify(validTicketData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create support ticket');
    });
  });

  describe('GET - List Support Tickets', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
    };

    it('should fetch user tickets successfully', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      const mockTickets = [
        {
          id: 'ticket-1',
          subject: 'Test Ticket 1',
          status: 'OPEN',
          messages: [],
          _count: { messages: 0 },
        },
        {
          id: 'ticket-2',
          subject: 'Test Ticket 2',
          status: 'RESOLVED',
          messages: [],
          _count: { messages: 2 },
        },
      ];

      mockPrisma.supportTicket.findMany.mockResolvedValue(mockTickets as any);
      mockPrisma.supportTicket.count.mockResolvedValue(2);

      const request = new NextRequest('http://localhost:3000/api/support/tickets?page=1&limit=10');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.tickets).toEqual(mockTickets);
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 2,
        pages: 1,
      });
    });

    it('should filter tickets by status', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      mockPrisma.supportTicket.findMany.mockResolvedValue([]);
      mockPrisma.supportTicket.count.mockResolvedValue(0);

      const request = new NextRequest('http://localhost:3000/api/support/tickets?status=OPEN');

      await GET(request);

      expect(mockPrisma.supportTicket.findMany).toHaveBeenCalledWith({
        where: {
          userId: mockUser.id,
          status: 'OPEN',
        },
        include: expect.any(Object),
        orderBy: expect.any(Object),
        skip: 0,
        take: 10,
      });
    });

    it('should require authentication', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/support/tickets');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });
  });
});
