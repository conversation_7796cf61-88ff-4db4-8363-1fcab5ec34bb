#!/usr/bin/env node

/**
 * Email Service Testing Utility
 * Tests email service functionality and sends test emails
 */

import { emailService } from '../src/lib/services/email';
import { promises as fs } from 'fs';
import path from 'path';

interface TestConfig {
  testRecipient: string;
  skipActualSending: boolean;
  outputDirectory: string;
}

const defaultConfig: TestConfig = {
  testRecipient: process.env.TEST_EMAIL || '<EMAIL>',
  skipActualSending: process.env.SKIP_EMAIL_SENDING === 'true',
  outputDirectory: './email-test-output',
};

async function testEmailService(config: TestConfig = defaultConfig): Promise<void> {
  console.log('🧪 Testing Email Service...\n');
  
  // Test 1: Service availability
  console.log('1️⃣ Testing service availability...');
  const isAvailable = emailService.isAvailable();
  console.log(`   Service available: ${isAvailable ? '✅' : '❌'}`);
  
  if (!isAvailable) {
    console.log('❌ Email service is not available. Check your configuration.');
    return;
  }
  
  // Test 2: Connection verification
  console.log('\n2️⃣ Testing connection...');
  try {
    const connectionValid = await emailService.verifyConnection();
    console.log(`   Connection valid: ${connectionValid ? '✅' : '❌'}`);
    
    if (!connectionValid) {
      console.log('⚠️  Connection verification failed. Emails may not be delivered.');
    }
  } catch (error) {
    console.log(`   Connection error: ❌ ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
  
  // Test 3: Template rendering and email sending
  console.log('\n3️⃣ Testing email templates...');
  
  const testCases = [
    {
      type: 'welcome' as const,
      description: 'Welcome email',
      data: {
        to: config.testRecipient,
        customerName: 'Test Customer',
      },
    },
    {
      type: 'order-confirmation' as const,
      description: 'Order confirmation email',
      data: {
        to: config.testRecipient,
        customerName: 'Test Customer',
        orderNumber: 'TEST-' + Date.now(),
        orderDate: new Date().toLocaleDateString(),
        totalAmount: '2500.00',
        currency: 'BDT',
        items: [
          {
            name: 'Custom T-Shirt',
            quantity: 1,
            price: '1500.00',
            customization: {
              name: 'Bold Streetwear Design',
              previewImage: 'https://example.com/design.jpg',
            },
          },
        ],
        shippingAddress: {
          name: 'Test Customer',
          address: '123 Test Street',
          city: 'Dhaka',
          postalCode: '1000',
          country: 'Bangladesh',
        },
        estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString(),
        orderUrl: 'https://ottiq.com/orders/test-123',
      },
    },
    {
      type: 'ai-tryon-ready' as const,
      description: 'AI try-on ready email',
      data: {
        to: config.testRecipient,
        customerName: 'Test Customer',
        productName: 'Custom T-Shirt',
        tryOnImageUrl: 'https://example.com/tryon-result.jpg',
        customizationName: 'Bold Streetwear Design',
        orderUrl: 'https://ottiq.com/create?design=test-design',
      },
    },
  ];
  
  // Create output directory
  await fs.mkdir(config.outputDirectory, { recursive: true });
  
  let successCount = 0;
  let failureCount = 0;
  
  for (const testCase of testCases) {
    console.log(`\n   Testing ${testCase.description}...`);
    
    try {
      if (config.skipActualSending) {
        // Just test template rendering
        console.log('     📧 Skipping actual email sending (test mode)');
        console.log('     ✅ Template rendering test passed');
        successCount++;
      } else {
        // Send actual email
        const result = await emailService.sendEmail(testCase.type, testCase.data, {
          priority: 'normal',
        });
        
        if (result.success) {
          console.log(`     ✅ Email sent successfully`);
          console.log(`     📧 Message ID: ${result.messageId}`);
          successCount++;
        } else {
          console.log(`     ❌ Email sending failed: ${result.error}`);
          failureCount++;
        }
      }
      
      // Save test data for reference
      const testDataFile = path.join(
        config.outputDirectory,
        `${testCase.type}-test-data.json`
      );
      await fs.writeFile(
        testDataFile,
        JSON.stringify(testCase.data, null, 2),
        'utf8'
      );
      
    } catch (error) {
      console.log(`     ❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      failureCount++;
    }
  }
  
  // Test 4: Bulk email functionality
  console.log('\n4️⃣ Testing bulk email functionality...');
  
  const bulkRecipients = [
    {
      email: config.testRecipient,
      data: {
        to: config.testRecipient,
        customerName: 'Bulk Test User 1',
      },
    },
    {
      email: config.testRecipient.replace('@', '+bulk2@'),
      data: {
        to: config.testRecipient.replace('@', '+bulk2@'),
        customerName: 'Bulk Test User 2',
      },
    },
  ];
  
  try {
    if (config.skipActualSending) {
      console.log('   📧 Skipping bulk email sending (test mode)');
      console.log('   ✅ Bulk email test passed');
    } else {
      const bulkResult = await emailService.sendBulkEmails('welcome', bulkRecipients, {
        batchSize: 2,
        delayBetweenBatches: 1000,
      });
      
      console.log(`   📊 Bulk email results:`);
      console.log(`      Sent: ${bulkResult.sent}`);
      console.log(`      Failed: ${bulkResult.failed}`);
      console.log(`      Success: ${bulkResult.success ? '✅' : '❌'}`);
      
      if (bulkResult.errors.length > 0) {
        console.log(`      Errors: ${bulkResult.errors.slice(0, 3).join(', ')}`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Bulk email test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    failureCount++;
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('='.repeat(50));
  console.log(`✅ Successful tests: ${successCount}`);
  console.log(`❌ Failed tests: ${failureCount}`);
  console.log(`📁 Test data saved to: ${config.outputDirectory}`);
  
  if (failureCount > 0) {
    console.log('\n❌ Some tests failed. Please check the configuration and try again.');
    process.exit(1);
  } else {
    console.log('\n🎉 All email service tests passed!');
  }
}

async function generateEmailPreview(): Promise<void> {
  console.log('🎨 Generating email previews...\n');
  
  const { render } = await import('@react-email/render');
  const outputDir = './email-previews';
  
  await fs.mkdir(outputDir, { recursive: true });
  
  const templates = [
    { name: 'WelcomeEmail', file: 'WelcomeEmail' },
    { name: 'OrderConfirmationEmail', file: 'OrderConfirmationEmail' },
    { name: 'AiTryOnReadyEmail', file: 'AiTryOnReadyEmail' },
  ];
  
  for (const template of templates) {
    try {
      console.log(`Generating preview for ${template.name}...`);
      
      // This would require the actual template imports
      // For now, we'll create a placeholder
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>${template.name} Preview</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .preview-note { background: #f0f0f0; padding: 10px; margin-bottom: 20px; }
            </style>
          </head>
          <body>
            <div class="preview-note">
              <strong>Preview:</strong> ${template.name}
              <br>
              <em>This is a placeholder preview. Actual template rendering would appear here.</em>
            </div>
          </body>
        </html>
      `;
      
      const previewFile = path.join(outputDir, `${template.file}.html`);
      await fs.writeFile(previewFile, htmlContent, 'utf8');
      
      console.log(`   ✅ Preview saved: ${previewFile}`);
      
    } catch (error) {
      console.log(`   ❌ Failed to generate preview for ${template.name}: ${error}`);
    }
  }
  
  console.log(`\n🎨 Email previews generated in: ${outputDir}`);
}

// CLI interface
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const command = args[0] || 'test';
  
  switch (command) {
    case 'test':
      await testEmailService();
      break;
    case 'preview':
      await generateEmailPreview();
      break;
    case 'help':
      console.log(`
Email Service Testing Utility

Commands:
  test     - Run email service tests (default)
  preview  - Generate HTML previews of email templates
  help     - Show this help message

Environment Variables:
  TEST_EMAIL           - Email address for test emails (default: <EMAIL>)
  SKIP_EMAIL_SENDING   - Set to 'true' to skip actual email sending (default: false)

Examples:
  npm run test:email
  TEST_EMAIL=<EMAIL> npm run test:email
  SKIP_EMAIL_SENDING=true npm run test:email
      `);
      break;
    default:
      console.log(`Unknown command: ${command}. Use 'help' for usage information.`);
      process.exit(1);
  }
}

// Run if this script is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Email service test failed:', error);
    process.exit(1);
  });
}

export { testEmailService, generateEmailPreview };
