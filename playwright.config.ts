import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './e2e',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 2 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: process.env.CI
    ? [['github'], ['html', { open: 'never' }], ['junit', { outputFile: 'test-results/junit.xml' }]]
    : [['html'], ['list']],
  /* Global timeout for each test */
  timeout: 30 * 1000,
  /* Global timeout for expect() assertions */
  expect: {
    timeout: 10 * 1000,
  },
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.BASE_URL || 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    /* Record video on failure */
    video: process.env.CI ? 'retain-on-failure' : 'off',

    /* Take screenshot on failure */
    screenshot: 'only-on-failure',

    /* Emulate user interactions */
    actionTimeout: 10 * 1000,
    navigationTimeout: 15 * 1000,
  },

  /* Configure projects for major browsers - Mobile First Approach */
  projects: [
    // Mobile-first testing (primary focus)
    {
      name: 'chromium-mobile',
      use: {
        ...devices['Pixel 5'],
        // Emotional flow optimizations
        hasTouch: true,
        isMobile: true,
      },
      testMatch: ['**/emotional-flows/**', '**/auth.spec.ts'],
    },
    {
      name: 'webkit-mobile',
      use: {
        ...devices['iPhone 12'],
        hasTouch: true,
        isMobile: true,
      },
      testMatch: ['**/emotional-flows/**', '**/auth.spec.ts'],
    },

    // Desktop testing (secondary)
    {
      name: 'chromium-desktop',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
      },
    },
    {
      name: 'firefox-desktop',
      use: {
        ...devices['Desktop Firefox'],
        viewport: { width: 1280, height: 720 },
      },
    },
    {
      name: 'webkit-desktop',
      use: {
        ...devices['Desktop Safari'],
        viewport: { width: 1280, height: 720 },
      },
    },

    // Tablet testing for design flows
    {
      name: 'tablet',
      use: {
        ...devices['iPad Pro'],
        hasTouch: true,
      },
      testMatch: ['**/emotional-flows/**'],
    },

    // High-DPI testing for visual elements
    {
      name: 'high-dpi',
      use: {
        ...devices['Desktop Chrome HiDPI'],
        viewport: { width: 1920, height: 1080 },
      },
      testMatch: ['**/visual/**', '**/emotional-flows/**'],
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000, // 2 minutes for server startup
    stdout: 'pipe',
    stderr: 'pipe',
  },

  /* Global setup and teardown - uncomment when needed */
  // globalSetup: require.resolve('./e2e/global-setup.ts'),
  // globalTeardown: require.resolve('./e2e/global-teardown.ts'),

  /* Test output directory */
  outputDir: 'test-results/',

  /* Metadata for test reports */
  metadata: {
    platform: process.platform,
    node: process.version,
    ci: !!process.env.CI,
  },
});
