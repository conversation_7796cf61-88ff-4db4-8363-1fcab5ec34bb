import { Page, expect } from '@playwright/test';

export class AuthHelpers {
  constructor(private page: Page) {}

  /**
   * Navigate to sign-in page and verify it loads correctly
   */
  async goToSignIn() {
    await this.page.goto('/auth/signin');
    await expect(this.page.getByRole('heading', { name: 'Join the Style Club' })).toBeVisible();
    return this;
  }

  /**
   * Mock successful authentication for testing protected routes
   * This simulates a logged-in user without going through OAuth
   */
  async mockAuthentication(userType: 'regular' | 'premium' | 'admin' = 'regular') {
    // Set authentication cookies/session storage
    await this.page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: `mock-session-${userType}-${Date.now()}`,
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      }
    ]);

    // Set user data in localStorage for client-side access
    await this.page.evaluate((type) => {
      const mockUser = {
        id: `mock-user-${type}`,
        email: `test-${type}@ottiq.com`,
        name: `Test ${type.charAt(0).toUpperCase() + type.slice(1)} User`,
        image: '/images/mock-avatar.jpg',
        role: type === 'admin' ? 'ADMIN' : 'USER',
        subscription: type === 'premium' ? 'PREMIUM' : 'FREE',
        createdAt: new Date().toISOString(),
      };
      
      localStorage.setItem('ottiq-user', JSON.stringify(mockUser));
      localStorage.setItem('ottiq-auth-state', 'authenticated');
    }, userType);

    return this;
  }

  /**
   * Clear authentication state
   */
  async signOut() {
    await this.page.context().clearCookies();
    await this.page.evaluate(() => {
      localStorage.removeItem('ottiq-user');
      localStorage.removeItem('ottiq-auth-state');
      sessionStorage.clear();
    });
    return this;
  }

  /**
   * Verify user is authenticated and on the correct page
   */
  async verifyAuthenticated() {
    // Check for authenticated user indicators
    const userMenu = this.page.getByTestId('user-menu');
    const profileButton = this.page.getByRole('button', { name: /profile|account/i });
    
    // At least one of these should be visible for authenticated users
    await expect(userMenu.or(profileButton)).toBeVisible({ timeout: 10000 });
    return this;
  }

  /**
   * Verify user is not authenticated (should see sign-in options)
   */
  async verifyNotAuthenticated() {
    // Should see sign-in button or be redirected to sign-in page
    const signInButton = this.page.getByRole('button', { name: /sign in|join/i });
    const signInLink = this.page.getByRole('link', { name: /sign in|join/i });
    const signInHeading = this.page.getByRole('heading', { name: 'Join the Style Club' });
    
    await expect(signInButton.or(signInLink).or(signInHeading)).toBeVisible({ timeout: 10000 });
    return this;
  }

  /**
   * Handle OAuth flow simulation (for visual testing)
   */
  async simulateOAuthFlow(provider: 'google' | 'facebook' = 'google') {
    await this.goToSignIn();
    
    const providerButton = this.page.getByRole('button', { 
      name: new RegExp(`Continue with ${provider}`, 'i') 
    });
    
    await expect(providerButton).toBeVisible();
    
    // In a real test, this would redirect to OAuth provider
    // For e2e tests, we'll mock the successful return
    await this.mockAuthentication('regular');
    
    return this;
  }

  /**
   * Test accessibility of auth pages
   */
  async checkAuthAccessibility() {
    await this.goToSignIn();
    
    // Check for proper heading hierarchy
    const h1 = this.page.getByRole('heading', { level: 1 });
    await expect(h1).toBeVisible();
    
    // Check for proper button labels
    const buttons = this.page.getByRole('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      const accessibleName = await button.getAttribute('aria-label') || await button.textContent();
      expect(accessibleName).toBeTruthy();
    }
    
    // Check for proper link text
    const links = this.page.getByRole('link');
    const linkCount = await links.count();
    
    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);
      const linkText = await link.textContent();
      expect(linkText?.trim()).toBeTruthy();
    }
    
    return this;
  }

  /**
   * Test mobile responsiveness of auth pages
   */
  async checkMobileResponsiveness() {
    // Set mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 });
    await this.goToSignIn();
    
    // Check that buttons are touch-friendly (at least 44px height)
    const buttons = this.page.getByRole('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      const boundingBox = await button.boundingBox();
      if (boundingBox) {
        expect(boundingBox.height).toBeGreaterThanOrEqual(44);
      }
    }
    
    return this;
  }
}
