# Ottiq Backup & Recovery System

## Overview

The Ottiq platform includes a comprehensive backup and recovery system designed to protect customer designs, brand assets, and all critical data. The system provides automated nightly backups, monitoring, alerting, and easy restore procedures.

## Architecture

### Components

1. **PostgreSQL Database Backup**
   - Customer data, orders, designs, user accounts
   - Compressed SQL dumps with checksums
   - Automated retention management

2. **MinIO Object Storage Backup**
   - Customer design files, images, AI try-on results
   - Brand assets, product images, templates
   - Complete volume snapshots

3. **Monitoring & Alerting**
   - Health checks every 6 hours
   - Webhook notifications for failures
   - Web dashboard for status monitoring

4. **Automated Scheduling**
   - Nightly backups at 2 AM
   - Weekly cleanup on Sundays
   - Configurable retention policies

## Quick Start

### 1. Install Backup System

```bash
# Make scripts executable and install cron jobs
cd /path/to/ottiq
chmod +x scripts/backup-*.sh scripts/restore-*.sh
./scripts/backup-config.sh install
```

### 2. Run Manual Backup

```bash
# Complete backup (database + MinIO)
./scripts/backup-all.sh prod

# Individual backups
./scripts/backup-database.sh prod
./scripts/backup-minio.sh prod
```

### 3. Check Backup Status

```bash
# View cron job status
./scripts/backup-config.sh status

# Generate status report
npx ts-node scripts/backup-monitor.ts prod html
```

## Backup Scripts

### Main Scripts

| Script | Purpose | Usage |
|--------|---------|-------|
| `backup-all.sh` | Complete backup orchestration | `./backup-all.sh [env]` |
| `backup-database.sh` | PostgreSQL database backup | `./backup-database.sh [env]` |
| `backup-minio.sh` | MinIO object storage backup | `./backup-minio.sh [env]` |
| `backup-config.sh` | Setup and configuration | `./backup-config.sh [install\|uninstall\|status]` |
| `backup-monitor.ts` | Status monitoring and alerts | `npx ts-node backup-monitor.ts [env] [format]` |

### Restore Scripts

| Script | Purpose | Usage |
|--------|---------|-------|
| `restore-database.sh` | Database restore from backup | `./restore-database.sh <backup_file> [env]` |
| `restore-minio.sh` | MinIO restore from backup | `./restore-minio.sh <backup_file> [env]` |

## Backup Configuration

### Environment Variables

```bash
# Optional: Webhook for alerts
export BACKUP_WEBHOOK_URL="https://hooks.slack.com/services/..."

# Optional: Success notifications
export BACKUP_SUCCESS_WEBHOOK_URL="https://hooks.slack.com/services/..."

# Optional: Email configuration
export SMTP_HOST="smtp.gmail.com"
export SMTP_FROM="<EMAIL>"
export ADMIN_EMAILS="<EMAIL>,<EMAIL>"
```

### Retention Policy

- **Daily Backups**: 30 days
- **Weekly Backups**: 12 weeks (84 days)
- **Monthly Backups**: 12 months (365 days)

### Storage Requirements

Estimate backup storage needs:

```bash
# Check current data sizes
docker exec ottiq-postgres psql -U ottiq_user -d ottiq_prod -c "SELECT pg_size_pretty(pg_database_size('ottiq_prod'));"
docker exec ottiq-minio du -sh /data
```

Recommended: 3x current data size for backup storage.

## Monitoring & Alerting

### Health Checks

The system performs automated health checks:

- **Backup Age**: Alerts if backups are older than 26 hours
- **Disk Usage**: Warns at 80%, critical at 90%
- **Service Status**: Monitors Docker containers
- **Backup Integrity**: Validates checksums and archive integrity

### Status Dashboard

Generate HTML status report:

```bash
npx ts-node scripts/backup-monitor.ts prod html
# View at: backups/status.html
```

### Alert Configuration

Configure webhook notifications:

```bash
# Slack webhook example
export BACKUP_WEBHOOK_URL="*****************************************************************************"

# Discord webhook example
export BACKUP_WEBHOOK_URL="https://discord.com/api/webhooks/123456789/abcdefghijklmnop"
```

## Restore Procedures

### Database Restore

1. **List Available Backups**
   ```bash
   ls -la backups/database/ottiq_db_prod_*.sql.gz
   ```

2. **Restore Database**
   ```bash
   # This will prompt for confirmation
   ./scripts/restore-database.sh ottiq_db_prod_20241211_020000.sql.gz prod
   ```

3. **Verify Restore**
   ```bash
   # Check database connection and tables
   docker exec ottiq-postgres psql -U ottiq_user -d ottiq_prod -c "\dt"
   ```

### MinIO Restore

1. **List Available Backups**
   ```bash
   ls -la backups/minio/ottiq_minio_prod_*.tar.gz
   ```

2. **Restore MinIO Data**
   ```bash
   # This will stop/start MinIO container
   ./scripts/restore-minio.sh ottiq_minio_prod_20241211_020000.tar.gz prod
   ```

3. **Verify Restore**
   ```bash
   # Check MinIO console at http://localhost:9001
   # Or check via CLI
   docker exec ottiq-minio find /data -type f | wc -l
   ```

### Complete System Restore

For disaster recovery:

1. **Restore Database First**
   ```bash
   ./scripts/restore-database.sh <latest_db_backup> prod
   ```

2. **Restore MinIO Data**
   ```bash
   ./scripts/restore-minio.sh <latest_minio_backup> prod
   ```

3. **Restart Application**
   ```bash
   docker-compose -f docker-compose.prod.yml restart app
   ```

4. **Verify Application**
   ```bash
   curl -f http://localhost/api/health
   ```

## Troubleshooting

### Common Issues

#### Backup Fails with "Container Not Running"

```bash
# Check container status
docker ps | grep ottiq

# Start containers if needed
docker-compose -f docker-compose.prod.yml up -d
```

#### Disk Space Issues

```bash
# Check disk usage
df -h

# Clean up old backups manually
find backups/ -name "*.gz" -mtime +30 -delete

# Or run cleanup script
./scripts/backup-cleanup.sh prod
```

#### Permission Issues

```bash
# Fix script permissions
chmod +x scripts/backup-*.sh scripts/restore-*.sh

# Fix backup directory permissions
sudo chown -R $(whoami):$(whoami) backups/
```

#### Restore Fails

```bash
# Check backup file integrity
gzip -t backups/database/backup_file.sql.gz
tar -tzf backups/minio/backup_file.tar.gz

# Verify checksums
cd backups/database
sha256sum -c backup_file.sql.gz.sha256
```

### Log Files

Check these log files for troubleshooting:

- `backups/backup_all_YYYYMMDD_HHMMSS.log` - Complete backup logs
- `backups/database/backup_YYYYMMDD_HHMMSS.log` - Database backup logs
- `backups/minio/backup_YYYYMMDD_HHMMSS.log` - MinIO backup logs
- `backups/alerts.log` - Alert history
- `backups/logs/cron.log` - Cron job execution logs

### Testing Backups

Regularly test backup integrity:

```bash
# Test database backup
./scripts/restore-database.sh <backup_file> dev

# Test MinIO backup
./scripts/restore-minio.sh <backup_file> dev

# Run application tests
npm test
```

## Security Considerations

### Backup Encryption

For production environments, consider encrypting backups:

```bash
# Encrypt backup files
gpg --symmetric --cipher-algo AES256 backup_file.sql.gz

# Decrypt for restore
gpg --decrypt backup_file.sql.gz.gpg > backup_file.sql.gz
```

### Access Control

- Limit backup directory access to backup user only
- Use secure webhook URLs with authentication
- Store backup encryption keys separately
- Regular security audits of backup procedures

### Off-site Backups

Consider implementing off-site backup storage:

- AWS S3 / Google Cloud Storage
- SFTP to remote server
- Network-attached storage (NAS)

## Maintenance

### Weekly Tasks

- Review backup logs for errors
- Check disk space usage
- Verify recent backup integrity
- Update retention policies if needed

### Monthly Tasks

- Test complete restore procedure
- Review and update documentation
- Audit backup security settings
- Performance optimization

### Quarterly Tasks

- Disaster recovery drill
- Review backup strategy
- Update monitoring thresholds
- Security assessment

## Windows Support

PowerShell versions of backup scripts are available:

```powershell
# Windows backup commands
.\scripts\backup-all.ps1 prod
.\scripts\backup-database.ps1 prod
.\scripts\backup-minio.ps1 prod

# Windows restore commands
.\scripts\restore-database.ps1 "backup_file.sql.gz" prod
.\scripts\restore-minio.ps1 "backup_file.tar.gz" prod
```

## API Integration

### Backup Status API

Create an API endpoint to check backup status:

```typescript
// src/app/api/admin/backup-status/route.ts
import { BackupMonitor } from '@/scripts/backup-monitor';

export async function GET() {
  const monitor = new BackupMonitor('prod');
  const status = await monitor.getStatus();

  return Response.json(status);
}
```

### Trigger Manual Backup

```typescript
// src/app/api/admin/backup-trigger/route.ts
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST() {
  try {
    const { stdout } = await execAsync('./scripts/backup-all.sh prod');
    return Response.json({ success: true, output: stdout });
  } catch (error) {
    return Response.json({ success: false, error: error.message }, { status: 500 });
  }
}
```

## Support

For backup-related issues:

1. Check the troubleshooting section above
2. Review log files in `backups/` directory
3. Run health check: `npx ts-node scripts/backup-monitor.ts prod`
4. Contact system administrator with log details

---

**Remember**: Backups are only as good as your ability to restore from them. Test your restore procedures regularly!
