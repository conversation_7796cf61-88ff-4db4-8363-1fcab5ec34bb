# Security & Code Protection Guide

## Overview

This document outlines the comprehensive security measures implemented in the Ottiq platform to protect business logic, user data, and ensure platform integrity while maintaining user trust.

## 🔒 Core Security Principles

### 1. Server-Side Security First
- **All sensitive business logic runs server-side only**
- **No sensitive calculations or pricing logic exposed to client**
- **API keys and secrets never sent to frontend**
- **Authentication and authorization handled server-side**

### 2. Defense in Depth
- **Multiple layers of security controls**
- **Input validation at every boundary**
- **Rate limiting and abuse prevention**
- **Comprehensive logging and monitoring**

## 🛡️ Security Implementation

### Authentication & Authorization

#### NextAuth.js Configuration
- **Secure session management with JWT tokens**
- **Role-based access control (admin/customer)**
- **OAuth integration with Google/Facebook**
- **Session encryption and secure cookies**

```typescript
// Middleware protection for routes
const protectedRoutes = ['/create', '/dashboard', '/privacy'];
const adminRoutes = ['/admin', '/api/admin'];
```

#### Access Control Matrix
| Route Type | Public | Authenticated | Admin Only |
|------------|--------|---------------|------------|
| `/` | ✅ | ✅ | ✅ |
| `/create` | ❌ | ✅ | ✅ |
| `/admin` | ❌ | ❌ | ✅ |
| `/api/admin` | ❌ | ❌ | ✅ |

### Rate Limiting & Abuse Prevention

#### API Rate Limits
- **General API**: 100 requests per 15 minutes per IP
- **AI Try-On**: 2 requests per day per user (server-enforced)
- **Pricing API**: 50 requests per 15 minutes per IP
- **File Upload**: 10 uploads per hour per user

#### Implementation
```typescript
// Redis-based rate limiting
const rateLimiter = new RateLimiter({
  redis: redisClient,
  keyPrefix: 'rl:',
  points: 100, // Number of requests
  duration: 900, // Per 15 minutes
});
```

### Input Validation & Sanitization

#### File Upload Security
- **File type validation**: Only JPEG, PNG, WebP allowed
- **File size limits**: 10MB general, 5MB portraits
- **Image dimension validation**: Min 256x256, Max 2048x2048
- **Malware scanning**: Configurable in production
- **Content-Type verification**: Server-side MIME type checking

#### Data Validation
- **Zod schemas for all API inputs**
- **SQL injection prevention via Prisma ORM**
- **XSS prevention through input sanitization**
- **CSRF protection via NextAuth**

### Security Headers

#### Production Headers
```typescript
// Implemented in next.config.js
{
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': 'default-src \'self\'; ...',
}
```

#### CORS Configuration
- **Restricted origins in production**
- **Credentials handling for authenticated requests**
- **Method and header restrictions**

### Environment Variable Security

#### Sensitive Variables (Server-Only)
```bash
# Never exposed to client
NEXTAUTH_SECRET=
DATABASE_URL=
BKASH_APP_SECRET=
HUGGING_FACE_API_KEY=
MINIO_SECRET_KEY=
REDIS_URL=
```

#### Public Variables (Client-Safe)
```bash
# Safe for client exposure
NEXT_PUBLIC_APP_URL=
NEXT_PUBLIC_BKASH_SANDBOX=
```

### Data Protection

#### Database Security
- **Connection encryption (SSL/TLS)**
- **Parameterized queries via Prisma**
- **Row-level security for sensitive data**
- **Regular backup encryption**

#### File Storage Security
- **MinIO with access key authentication**
- **Bucket policies for restricted access**
- **Signed URLs for temporary access**
- **Automatic file cleanup for AI try-on images**

### AI Try-On Privacy & Security

#### Privacy Controls
- **Explicit user consent required**
- **Daily usage limits (2 per day)**
- **Automatic image deletion after 7 days**
- **No image storage without consent**

#### Security Measures
- **Server-side image processing only**
- **Secure API communication with Hugging Face**
- **Input validation for uploaded portraits**
- **Rate limiting and abuse prevention**

## 🔧 Production Configuration

### Next.js Production Settings

#### Source Map Protection
```javascript
// next.config.production.js
const nextConfig = {
  productionBrowserSourceMaps: false, // Hide source maps
  generateBuildId: () => `build-${Date.now()}`, // Unique build IDs
  poweredByHeader: false, // Hide Next.js header
  output: 'standalone', // Optimized for Docker deployment
};
```

#### Performance & Security
- **SWC minification enabled**
- **Compression enabled**
- **Image optimization with Sharp**
- **Static file caching headers**
- **Bundle analysis for optimization**
- **Console.log removal in production**

### Security Middleware Implementation

#### Comprehensive API Security
```typescript
// Usage example
const secureHandler = createSecureApiHandler(
  async (request) => {
    // Your API logic here
    return NextResponse.json({ success: true });
  },
  {
    validation: { body: MySchema },
    rateLimit: 'general',
    requireAuth: true,
    fileUpload: { maxSize: 10 * 1024 * 1024 },
  }
);
```

#### Available Security Features
- **Input validation with Zod schemas**
- **Rate limiting with Redis backend**
- **CORS protection**
- **File upload security**
- **XSS prevention**
- **Request sanitization**
- **Authentication checks**

### Environment-Specific Configurations

#### Development
```bash
NODE_ENV=development
NEXTAUTH_URL=http://localhost:3000
BKASH_SANDBOX=true
ENABLE_IMAGE_SECURITY_SCAN=false
```

#### Production
```bash
NODE_ENV=production
NEXTAUTH_URL=https://ottiq.com
BKASH_SANDBOX=false
ENABLE_IMAGE_SECURITY_SCAN=true
```

## 🚨 Security Monitoring

### Logging & Monitoring
- **Comprehensive error logging with Sentry**
- **Security event logging**
- **Failed authentication attempts tracking**
- **Rate limit violation alerts**

### Health Checks
- **Database connectivity monitoring**
- **Redis availability checks**
- **External service health monitoring**
- **File storage accessibility verification**

## 🔍 Security Testing

### Automated Security Testing
```bash
# Security linting
npm run lint:security

# Dependency vulnerability scanning
npm audit

# End-to-end security tests
npm run test:e2e:security
```

### Manual Security Reviews
- **Code review checklist for security**
- **Penetration testing guidelines**
- **Security audit procedures**

## 📋 Security Checklist

### Pre-Deployment Security Checklist
- [ ] All environment variables properly configured
- [ ] Source maps disabled in production
- [ ] Security headers implemented
- [ ] Rate limiting configured
- [ ] Input validation in place
- [ ] Authentication/authorization tested
- [ ] File upload security verified
- [ ] Database security configured
- [ ] Monitoring and logging enabled
- [ ] Backup and recovery tested

### Ongoing Security Maintenance
- [ ] Regular dependency updates
- [ ] Security patch monitoring
- [ ] Log review and analysis
- [ ] Performance and security metrics review
- [ ] Incident response procedures updated

## 🚀 Deployment Security

### Docker Security
- **Non-root user containers**
- **Minimal base images**
- **Security scanning of images**
- **Network isolation**

### Infrastructure Security
- **TLS/SSL encryption**
- **Firewall configuration**
- **Regular security updates**
- **Access control and monitoring**

## 📞 Incident Response

### Security Incident Procedures
1. **Immediate containment**
2. **Impact assessment**
3. **Evidence preservation**
4. **Stakeholder notification**
5. **Recovery and remediation**
6. **Post-incident review**

### Contact Information
- **Security Team**: <EMAIL>
- **Emergency Contact**: +880-XXX-XXXX
- **Incident Reporting**: <EMAIL>

---

**Last Updated**: 2025-01-11  
**Version**: 1.0  
**Review Cycle**: Quarterly
