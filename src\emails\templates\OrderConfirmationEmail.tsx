/**
 * Order Confirmation Email Template
 * Celebrates the customer's order with emotional, lifestyle-focused copy
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { OrderEmailData } from '../../lib/services/email';

interface OrderConfirmationEmailProps extends OrderEmailData {}

export function OrderConfirmationEmail({
  customerName,
  orderNumber,
  orderDate,
  totalAmount,
  currency,
  items,
  shippingAddress,
  estimatedDelivery,
  orderUrl,
}: OrderConfirmationEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`Your Ottiq order #${orderNumber} is confirmed! Your unique style is being crafted.`}>
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-order-confirmed.jpg`}
          alt="Order Confirmed - Your style journey begins"
          width="600"
          height="300"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          🎉 Your Style is Confirmed, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Your unique creation is now in our hands. We're excited to bring your imagination to life!
        </EmailText>
      </Section>

      {/* Order Details */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <EmailHeading level={2}>Order #{orderNumber}</EmailHeading>
          <Text className="text-sm text-gray-500">{orderDate}</Text>
        </div>

        {/* Order Items */}
        <div className="space-y-4">
          {items.map((item, index) => (
            <div key={index} className="flex items-start space-x-4 p-4 bg-white rounded-lg">
              {item.customization?.previewImage && (
                <Img
                  src={item.customization.previewImage}
                  alt={item.name}
                  width="80"
                  height="80"
                  className="rounded-lg"
                />
              )}
              <div className="flex-1">
                <Text className="font-semibold text-gray-900 mb-1">
                  {item.name}
                </Text>
                {item.customization && (
                  <Text className="text-sm text-primary-600 mb-1">
                    Custom Design: {item.customization.name}
                  </Text>
                )}
                <Text className="text-sm text-gray-600">
                  Quantity: {item.quantity} × {currency} {item.price}
                </Text>
              </div>
            </div>
          ))}
        </div>

        <Hr className="my-4" />

        {/* Order Total */}
        <div className="flex justify-between items-center">
          <Text className="text-lg font-semibold text-gray-900">
            Total Amount
          </Text>
          <Text className="text-xl font-bold text-primary-600">
            {currency} {totalAmount}
          </Text>
        </div>
      </Section>

      {/* What's Next */}
      <Section className="mb-6">
        <EmailHeading level={2}>What Happens Next?</EmailHeading>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Text className="text-primary-600 font-bold text-sm">1</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Design Review & Production
              </Text>
              <Text className="text-gray-600 text-sm">
                Our artisans will carefully review your design and begin crafting your unique piece with premium materials.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Text className="text-primary-600 font-bold text-sm">2</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Quality Check & Packaging
              </Text>
              <Text className="text-gray-600 text-sm">
                Every piece goes through our quality assurance process before being lovingly packaged for you.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Text className="text-primary-600 font-bold text-sm">3</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Shipped to You
              </Text>
              <Text className="text-gray-600 text-sm">
                {estimatedDelivery 
                  ? `Expected delivery: ${estimatedDelivery}. You'll receive tracking details once shipped.`
                  : 'You\'ll receive tracking details once your order ships.'
                }
              </Text>
            </div>
          </div>
        </div>
      </Section>

      {/* Shipping Address */}
      <Section className="bg-blue-50 rounded-lg p-6 mb-6">
        <EmailHeading level={3}>Shipping To:</EmailHeading>
        <Text className="text-gray-700 mb-0">
          {shippingAddress.name}<br />
          {shippingAddress.address}<br />
          {shippingAddress.city}, {shippingAddress.postalCode}<br />
          {shippingAddress.country}
        </Text>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-6">
        <EmailText>
          Track your order, view your designs, or create something new:
        </EmailText>
        
        <div className="space-y-3">
          <EmailButton href={orderUrl} variant="primary">
            View Order Details
          </EmailButton>
          
          <br />
          
          <EmailButton href={`${baseUrl}/create`} variant="outline">
            Create Another Design
          </EmailButton>
        </div>
      </Section>

      {/* Inspiration Section */}
      <Section className="bg-gradient-to-r from-primary-50 to-warm-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>Share Your Style Journey</EmailHeading>
        <EmailText>
          We can't wait to see how you style your creation! Tag us @ottiq when you wear it and inspire others to express their unique style.
        </EmailText>
        
        <Text className="text-sm text-gray-600">
          #OttiqStyle #WearYourImagination #CustomFashion
        </Text>
      </Section>
    </EmailLayout>
  );
}
