import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { ProductHelpers } from '../utils/product-helpers';

test.describe('Emotional Customization Flow', () => {
  let authHelpers: AuthHelpers;
  let productHelpers: ProductHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    productHelpers = new ProductHelpers(page);
    await authHelpers.mockAuthentication('regular');
    
    // Navigate to customization interface
    await productHelpers.selectCategory('hoodies');
    await productHelpers.selectProduct();
    await productHelpers.startCustomization();
  });

  test('should inspire creativity through intuitive design tools', async ({ page }) => {
    await test.step('Design interface should feel welcoming and inspiring', async () => {
      // Check for inspirational welcome message
      await expect(page.getByText(/let's create something amazing|design your unique style|express yourself/i)).toBeVisible();
      
      // Design canvas should be prominent and inviting
      const designCanvas = page.getByTestId('design-canvas');
      await expect(designCanvas).toBeVisible();
      
      // Tool panel should be organized and accessible
      const toolPanel = page.getByTestId('design-tools');
      await expect(toolPanel).toBeVisible();
      
      // Should show helpful tips or tutorials
      const helpTips = page.getByTestId('design-tips');
      if (await helpTips.isVisible()) {
        const tipText = await helpTips.textContent();
        expect(tipText).toMatch(/tip|try|start with|click to/i);
      }
    });

    await test.step('Text tool should encourage personal expression', async () => {
      // Add text with emotional validation
      await productHelpers.addTextToDesign('My Story');
      
      // Should show positive feedback
      await expect(page.getByText(/great choice|looking good|perfect/i)).toBeVisible();
      
      // Text customization should be intuitive
      const textOptions = page.getByTestId('text-options');
      if (await textOptions.isVisible()) {
        // Test font selection
        const fontSelector = page.getByTestId('font-selector');
        if (await fontSelector.isVisible()) {
          await fontSelector.click();
          
          // Should have personality-based font names
          const fontOptions = page.getByTestId(/font-option/);
          const fontCount = await fontOptions.count();
          
          if (fontCount > 0) {
            const firstFont = fontOptions.first();
            const fontName = await firstFont.textContent();
            expect(fontName).toMatch(/bold|elegant|playful|modern|classic/i);
            
            await firstFont.click();
          }
        }
        
        // Test color selection with emotional context
        const colorPicker = page.getByTestId('text-color-picker');
        if (await colorPicker.isVisible()) {
          await colorPicker.click();
          
          // Should have mood-based color descriptions
          const colorOptions = page.getByTestId(/color-option/);
          const colorCount = await colorOptions.count();
          
          if (colorCount > 0) {
            const vibrantColor = colorOptions.first();
            await vibrantColor.click();
            
            // Should show color applied to text
            await page.waitForTimeout(500);
          }
        }
      }
    });

    await test.step('Color selection should evoke emotional responses', async () => {
      await productHelpers.selectColor('forest-green');
      
      // Should show color mood description
      const colorDescription = page.getByTestId('color-description');
      if (await colorDescription.isVisible()) {
        const descText = await colorDescription.textContent();
        expect(descText).toMatch(/nature|calm|fresh|earthy|peaceful/i);
      }
      
      // Should update product preview immediately
      const productPreview = page.getByTestId('product-preview');
      await expect(productPreview).toBeVisible();
      
      // Test multiple colors to see emotional descriptions
      const colorOptions = ['navy', 'burgundy', 'cream'];
      for (const color of colorOptions) {
        const colorButton = page.getByTestId(`color-${color}`);
        if (await colorButton.isVisible()) {
          await colorButton.click();
          await page.waitForTimeout(500);
          
          // Each color should have its own emotional context
          const moodText = page.getByTestId('color-mood');
          if (await moodText.isVisible()) {
            const mood = await moodText.textContent();
            expect(mood?.length).toBeGreaterThan(0);
          }
        }
      }
    });

    await test.step('Design elements should provide creative inspiration', async () => {
      // Test graphics/icons library
      const graphicsLibrary = page.getByTestId('graphics-library');
      if (await graphicsLibrary.isVisible()) {
        await graphicsLibrary.click();
        
        // Should have categorized graphics
        const categories = page.getByTestId(/graphics-category/);
        const categoryCount = await categories.count();
        
        if (categoryCount > 0) {
          const natureCategory = page.getByTestId('graphics-category-nature');
          if (await natureCategory.isVisible()) {
            await natureCategory.click();
            
            // Should show nature-themed graphics
            const graphics = page.getByTestId(/graphic-item/);
            const graphicCount = await graphics.count();
            expect(graphicCount).toBeGreaterThan(0);
            
            // Add a graphic
            const firstGraphic = graphics.first();
            await firstGraphic.click();
            
            // Should appear on canvas
            await page.waitForTimeout(1000);
          }
        }
      }
      
      // Test patterns/textures
      const patternsTab = page.getByTestId('patterns-tab');
      if (await patternsTab.isVisible()) {
        await patternsTab.click();
        
        const patterns = page.getByTestId(/pattern-item/);
        const patternCount = await patterns.count();
        
        if (patternCount > 0) {
          const subtlePattern = patterns.first();
          await subtlePattern.click();
          await page.waitForTimeout(500);
        }
      }
    });
  });

  test('should provide real-time emotional feedback during design', async ({ page }) => {
    await test.step('Design progress should build excitement', async () => {
      // Initial state should encourage starting
      await expect(page.getByText(/start creating|add your first element|let's begin/i)).toBeVisible();
      
      // Add first element
      await productHelpers.addTextToDesign('Creative');
      
      // Should show progress encouragement
      await expect(page.getByText(/great start|looking good|keep going/i)).toBeVisible();
      
      // Add color
      await productHelpers.selectColor('royal-blue');
      
      // Should show design evolution feedback
      await expect(page.getByText(/nice choice|coming together|perfect/i)).toBeVisible();
    });

    await test.step('Design validation should be supportive', async () => {
      // Test design with potential issues
      await productHelpers.addTextToDesign('Very Long Text That Might Not Fit Well');
      
      // Should provide helpful suggestions, not harsh criticism
      const designFeedback = page.getByTestId('design-feedback');
      if (await designFeedback.isVisible()) {
        const feedbackText = await designFeedback.textContent();
        expect(feedbackText).toMatch(/consider|try|might look better|suggestion/i);
        expect(feedbackText).not.toMatch(/error|wrong|bad|invalid/i);
      }
      
      // Should offer solutions
      const suggestions = page.getByTestId('design-suggestions');
      if (await suggestions.isVisible()) {
        await expect(page.getByText(/try shorter text|adjust size|different position/i)).toBeVisible();
      }
    });

    await test.step('Undo/redo should feel safe and encouraging', async () => {
      // Make several changes
      await productHelpers.addTextToDesign('Test 1');
      await productHelpers.selectColor('red');
      await productHelpers.addTextToDesign('Test 2');
      
      // Test undo with encouraging messaging
      const undoButton = page.getByRole('button', { name: /undo|go back/i });
      if (await undoButton.isVisible()) {
        await undoButton.click();
        
        // Should show supportive undo message
        const undoFeedback = page.getByTestId('undo-feedback');
        if (await undoFeedback.isVisible()) {
          const feedbackText = await undoFeedback.textContent();
          expect(feedbackText).toMatch(/undone|previous step|no worries/i);
        }
      }
      
      // Test redo
      const redoButton = page.getByRole('button', { name: /redo|forward/i });
      if (await redoButton.isVisible()) {
        await redoButton.click();
        await page.waitForTimeout(500);
      }
    });
  });

  test('should handle mobile customization with touch-optimized tools', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await test.step('Mobile design tools should be touch-friendly', async () => {
      // Tool buttons should be large enough for touch
      const tools = page.getByTestId(/tool-/);
      const toolCount = await tools.count();
      
      for (let i = 0; i < Math.min(toolCount, 5); i++) {
        const tool = tools.nth(i);
        const toolBox = await tool.boundingBox();
        if (toolBox) {
          expect(toolBox.height).toBeGreaterThanOrEqual(44);
          expect(toolBox.width).toBeGreaterThanOrEqual(44);
        }
      }
    });

    await test.step('Mobile canvas should support touch gestures', async () => {
      const canvas = page.getByTestId('design-canvas');
      const canvasBox = await canvas.boundingBox();
      
      if (canvasBox) {
        // Test tap to add text
        await page.touchscreen.tap(canvasBox.x + canvasBox.width / 2, canvasBox.y + canvasBox.height / 2);
        
        // Should show text input or tool selection
        const textInput = page.getByTestId('text-input');
        const toolSelector = page.getByTestId('tool-selector');
        
        await expect(textInput.or(toolSelector)).toBeVisible();
        
        // Test pinch-to-zoom if supported
        // Note: Playwright's touch simulation is limited, so this is a basic test
        await page.touchscreen.tap(canvasBox.x + 100, canvasBox.y + 100);
        await page.waitForTimeout(500);
      }
    });

    await test.step('Mobile color picker should be accessible', async () => {
      const colorPicker = page.getByTestId('color-picker');
      await expect(colorPicker).toBeVisible();
      
      // Color swatches should be touch-friendly
      const colorSwatches = page.getByTestId(/color-/);
      const swatchCount = await colorSwatches.count();
      
      for (let i = 0; i < Math.min(swatchCount, 5); i++) {
        const swatch = colorSwatches.nth(i);
        const swatchBox = await swatch.boundingBox();
        if (swatchBox) {
          expect(swatchBox.height).toBeGreaterThanOrEqual(40);
          expect(swatchBox.width).toBeGreaterThanOrEqual(40);
        }
      }
    });
  });

  test('should save designs with emotional attachment', async ({ page }) => {
    await test.step('Design saving should feel meaningful', async () => {
      // Create a design
      await productHelpers.addTextToDesign('My Masterpiece');
      await productHelpers.selectColor('deep-purple');
      
      // Save with emotional validation
      await productHelpers.saveDesign('My Personal Creation');
      
      // Should show meaningful save confirmation
      await expect(page.getByText(/your creation is saved|design preserved|safely stored/i)).toBeVisible();
      
      // Should offer to continue or share
      const continueOptions = page.getByTestId('save-options');
      if (await continueOptions.isVisible()) {
        await expect(page.getByText(/continue designing|share your creation|see how it looks/i)).toBeVisible();
      }
    });

    await test.step('Design library should show personal collection', async () => {
      // Navigate to saved designs
      const myDesigns = page.getByRole('button', { name: /my designs|saved|collection/i });
      if (await myDesigns.isVisible()) {
        await myDesigns.click();
        
        // Should show personal design collection
        const designLibrary = page.getByTestId('design-library');
        await expect(designLibrary).toBeVisible();
        
        // Should show emotional context for saved designs
        const savedDesigns = page.getByTestId(/saved-design/);
        const designCount = await savedDesigns.count();
        
        if (designCount > 0) {
          const firstDesign = savedDesigns.first();
          
          // Should show design preview
          const designPreview = firstDesign.getByRole('img');
          await expect(designPreview).toBeVisible();
          
          // Should show creation date with emotional context
          const creationInfo = firstDesign.getByTestId('creation-info');
          if (await creationInfo.isVisible()) {
            const infoText = await creationInfo.textContent();
            expect(infoText).toMatch(/created|designed|made/i);
          }
        }
      }
    });

    await test.step('Design sharing should build pride', async () => {
      // Create and save a design first
      await productHelpers.addTextToDesign('Share This');
      await productHelpers.saveDesign('Shareable Design');
      
      // Look for share options
      const shareButton = page.getByRole('button', { name: /share|show off|tell friends/i });
      if (await shareButton.isVisible()) {
        await shareButton.click();
        
        // Should show sharing options with encouraging copy
        const shareModal = page.getByTestId('share-modal');
        await expect(shareModal).toBeVisible();
        
        await expect(page.getByText(/show off your creation|share your style|let others see/i)).toBeVisible();
        
        // Should have multiple sharing platforms
        const shareOptions = page.getByTestId(/share-/);
        const optionCount = await shareOptions.count();
        expect(optionCount).toBeGreaterThan(0);
      }
    });
  });

  test('should handle design complexity with guidance', async ({ page }) => {
    await test.step('Complex designs should receive helpful guidance', async () => {
      // Add multiple elements
      await productHelpers.addTextToDesign('Main Title');
      await productHelpers.addTextToDesign('Subtitle');
      await productHelpers.addTextToDesign('Small Text');
      
      // Should provide composition guidance
      const designGuidance = page.getByTestId('design-guidance');
      if (await designGuidance.isVisible()) {
        const guidanceText = await designGuidance.textContent();
        expect(guidanceText).toMatch(/balance|composition|spacing|hierarchy/i);
      }
      
      // Should suggest improvements
      const suggestions = page.getByTestId('design-suggestions');
      if (await suggestions.isVisible()) {
        await expect(page.getByText(/consider|try|might look better/i)).toBeVisible();
      }
    });

    await test.step('Design templates should inspire creativity', async () => {
      const templatesButton = page.getByRole('button', { name: /templates|inspiration|examples/i });
      if (await templatesButton.isVisible()) {
        await templatesButton.click();
        
        // Should show categorized templates
        const templateCategories = page.getByTestId(/template-category/);
        const categoryCount = await templateCategories.count();
        
        if (categoryCount > 0) {
          const firstCategory = templateCategories.first();
          await firstCategory.click();
          
          // Should show templates with emotional context
          const templates = page.getByTestId(/template-item/);
          const templateCount = await templates.count();
          
          if (templateCount > 0) {
            const template = templates.first();
            
            // Should have inspiring names/descriptions
            const templateName = template.getByTestId('template-name');
            if (await templateName.isVisible()) {
              const nameText = await templateName.textContent();
              expect(nameText).toMatch(/bold|elegant|fun|modern|classic|creative/i);
            }
            
            // Apply template
            const useTemplateButton = template.getByRole('button', { name: /use|apply|start with/i });
            if (await useTemplateButton.isVisible()) {
              await useTemplateButton.click();
              
              // Should apply template to canvas
              await page.waitForTimeout(1000);
              
              // Should show template applied confirmation
              await expect(page.getByText(/template applied|starting point|customize from here/i)).toBeVisible();
            }
          }
        }
      }
    });
  });
});
