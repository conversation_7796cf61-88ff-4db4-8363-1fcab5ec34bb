/**
 * Base Email Layout Component for Ottiq
 * Provides consistent branding and structure for all emails
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Img,
  Text,
  Link,
  Hr,
  Tai<PERSON><PERSON>,
} from '@react-email/components';
import { ReactNode } from 'react';

interface EmailLayoutProps {
  children: ReactNode;
  previewText?: string;
}

export function EmailLayout({ children, previewText }: EmailLayoutProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <Html>
      <Head />
      <Tailwind
        config={{
          theme: {
            extend: {
              colors: {
                // Ottiq brand colors inspired by Bangladeshi fashion culture
                primary: {
                  50: '#fef7ee',
                  100: '#fdedd3',
                  200: '#fbd7a5',
                  300: '#f8bb6d',
                  400: '#f59332',
                  500: '#f2750a',
                  600: '#e35d05',
                  700: '#bc4508',
                  800: '#95370e',
                  900: '#792f0f',
                },
                secondary: {
                  50: '#f0f9ff',
                  100: '#e0f2fe',
                  200: '#bae6fd',
                  300: '#7dd3fc',
                  400: '#38bdf8',
                  500: '#0ea5e9',
                  600: '#0284c7',
                  700: '#0369a1',
                  800: '#075985',
                  900: '#0c4a6e',
                },
                accent: {
                  50: '#fdf4ff',
                  100: '#fae8ff',
                  200: '#f5d0fe',
                  300: '#f0abfc',
                  400: '#e879f9',
                  500: '#d946ef',
                  600: '#c026d3',
                  700: '#a21caf',
                  800: '#86198f',
                  900: '#701a75',
                },
                warm: {
                  50: '#fefaf0',
                  100: '#fef3c7',
                  200: '#fde68a',
                  300: '#fcd34d',
                  400: '#fbbf24',
                  500: '#f59e0b',
                  600: '#d97706',
                  700: '#b45309',
                  800: '#92400e',
                  900: '#78350f',
                },
              },
              fontFamily: {
                sans: ['Inter', 'system-ui', 'sans-serif'],
                display: ['Poppins', 'Inter', 'system-ui', 'sans-serif'],
              },
            },
          },
        }}
      >
        <Body className="bg-gray-50 font-sans">
          {previewText && (
            <div
              style={{
                display: 'none',
                overflow: 'hidden',
                lineHeight: '1px',
                opacity: 0,
                maxHeight: 0,
                maxWidth: 0,
              }}
            >
              {previewText}
            </div>
          )}
          
          <Container className="mx-auto py-8 px-4 max-w-2xl">
            {/* Header */}
            <Section className="bg-white rounded-t-lg shadow-sm">
              <div className="px-8 py-6 text-center border-b border-gray-100">
                <Img
                  src={`${baseUrl}/images/logo-email.png`}
                  alt="Ottiq"
                  width="120"
                  height="40"
                  className="mx-auto"
                />
                <Text className="text-sm text-gray-600 mt-2 mb-0">
                  Wear Your Imagination
                </Text>
              </div>
            </Section>

            {/* Main Content */}
            <Section className="bg-white px-8 py-6">
              {children}
            </Section>

            {/* Footer */}
            <Section className="bg-gray-100 rounded-b-lg px-8 py-6">
              <div className="text-center">
                <Text className="text-sm text-gray-600 mb-4">
                  Follow us for style inspiration and updates
                </Text>
                
                <div className="flex justify-center space-x-4 mb-4">
                  <Link
                    href="https://facebook.com/ottiq"
                    className="text-primary-600 hover:text-primary-700"
                  >
                    Facebook
                  </Link>
                  <Link
                    href="https://instagram.com/ottiq"
                    className="text-primary-600 hover:text-primary-700"
                  >
                    Instagram
                  </Link>
                  <Link
                    href="https://twitter.com/ottiq"
                    className="text-primary-600 hover:text-primary-700"
                  >
                    Twitter
                  </Link>
                </div>

                <Hr className="border-gray-300 my-4" />

                <Text className="text-xs text-gray-500 mb-2">
                  Ottiq - Custom Fashion Platform
                  <br />
                  Dhaka, Bangladesh
                </Text>

                <Text className="text-xs text-gray-500 mb-2">
                  <Link
                    href={`${baseUrl}/unsubscribe`}
                    className="text-gray-500 hover:text-gray-700 underline"
                  >
                    Unsubscribe
                  </Link>
                  {' | '}
                  <Link
                    href={`${baseUrl}/privacy`}
                    className="text-gray-500 hover:text-gray-700 underline"
                  >
                    Privacy Policy
                  </Link>
                  {' | '}
                  <Link
                    href={`${baseUrl}/support`}
                    className="text-gray-500 hover:text-gray-700 underline"
                  >
                    Support
                  </Link>
                </Text>

                <Text className="text-xs text-gray-400 mb-0">
                  © 2024 Ottiq. All rights reserved.
                </Text>
              </div>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
}

// Reusable components for emails
export function EmailButton({
  href,
  children,
  variant = 'primary',
}: {
  href: string;
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
}) {
  const baseClasses = 'inline-block px-6 py-3 rounded-lg font-semibold text-center no-underline transition-colors';
  
  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700',
    secondary: 'bg-secondary-600 text-white hover:bg-secondary-700',
    outline: 'border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white',
  };

  return (
    <Link
      href={href}
      className={`${baseClasses} ${variantClasses[variant]}`}
    >
      {children}
    </Link>
  );
}

export function EmailHeading({
  children,
  level = 1,
}: {
  children: ReactNode;
  level?: 1 | 2 | 3;
}) {
  const classes = {
    1: 'text-2xl font-bold text-gray-900 mb-4',
    2: 'text-xl font-semibold text-gray-800 mb-3',
    3: 'text-lg font-medium text-gray-700 mb-2',
  };

  return (
    <Text className={classes[level]}>
      {children}
    </Text>
  );
}

export function EmailText({
  children,
  className = '',
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <Text className={`text-gray-600 leading-relaxed mb-4 ${className}`}>
      {children}
    </Text>
  );
}
