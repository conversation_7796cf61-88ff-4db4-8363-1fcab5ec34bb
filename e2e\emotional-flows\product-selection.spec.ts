import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { ProductHelpers } from '../utils/product-helpers';

test.describe('Emotional Product Selection Flow', () => {
  let authHelpers: AuthHelpers;
  let productHelpers: ProductHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    productHelpers = new ProductHelpers(page);
    await authHelpers.mockAuthentication('regular');
  });

  test('should inspire users with emotional product discovery', async ({ page }) => {
    await test.step('Homepage should trigger emotional desire', async () => {
      await page.goto('/');
      
      // Check for inspirational hero messaging
      await expect(page.getByText(/wear your imagination|express yourself|create something unique/i)).toBeVisible();
      
      // Check for emotional call-to-action
      const ctaButton = page.getByRole('button', { name: /start creating|design now|express yourself/i });
      await expect(ctaButton).toBeVisible();
      
      await ctaButton.click();
    });

    await test.step('Product categories should evoke lifestyle aspirations', async () => {
      // Should be on products page or category selection
      await expect(page.getByText(/choose your canvas|what speaks to you|express your style/i)).toBeVisible();
      
      // Each category should have emotional messaging
      const categories = ['hoodies', 't-shirts', 'accessories', 'jackets'];
      
      for (const category of categories) {
        const categoryCard = page.getByTestId(`category-${category}`);
        if (await categoryCard.isVisible()) {
          // Check for lifestyle imagery
          const categoryImage = categoryCard.getByRole('img');
          await expect(categoryImage).toBeVisible();
          
          // Check for emotional copy
          const emotionalText = categoryCard.getByText(/cozy|comfort|adventure|style|express|unique/i);
          await expect(emotionalText).toBeVisible();
          
          // Check for aspirational pricing presentation
          const priceText = categoryCard.getByText(/starting at|from|as low as/i);
          if (await priceText.isVisible()) {
            expect(await priceText.textContent()).toMatch(/\$\d+/);
          }
        }
      }
    });

    await test.step('Product selection should build excitement', async () => {
      await productHelpers.selectCategory('hoodies');
      
      // Check for category-specific emotional messaging
      await expect(page.getByText(/cozy creativity|comfort meets style|wrap yourself in imagination/i)).toBeVisible();
      
      // Product grid should show lifestyle context
      const productCards = page.getByTestId(/product-card/);
      const productCount = await productCards.count();
      expect(productCount).toBeGreaterThan(0);
      
      // Each product should have emotional elements
      for (let i = 0; i < Math.min(productCount, 3); i++) {
        const productCard = productCards.nth(i);
        
        // Lifestyle image
        const productImage = productCard.getByRole('img');
        await expect(productImage).toBeVisible();
        
        // Emotional product name/description
        const productTitle = productCard.getByRole('heading');
        await expect(productTitle).toBeVisible();
        
        // Social proof or popularity indicators
        const socialProof = productCard.getByText(/popular|trending|loved by|bestseller/i);
        // Social proof is optional but good to have
      }
    });

    await test.step('Product details should create ownership desire', async () => {
      await productHelpers.selectProduct();
      
      // Should show detailed lifestyle imagery
      const productGallery = page.getByTestId('product-gallery');
      await expect(productGallery).toBeVisible();
      
      // Emotional product description
      const description = page.getByTestId('product-description');
      await expect(description).toBeVisible();
      const descriptionText = await description.textContent();
      expect(descriptionText).toMatch(/imagine|perfect for|express|unique|style|comfort/i);
      
      // Customization preview should build excitement
      const customizationPreview = page.getByTestId('customization-preview');
      if (await customizationPreview.isVisible()) {
        await expect(page.getByText(/make it yours|customize|personalize/i)).toBeVisible();
      }
      
      // Size guide should be helpful and encouraging
      const sizeGuide = page.getByRole('button', { name: /size guide|find your fit/i });
      if (await sizeGuide.isVisible()) {
        await sizeGuide.click();
        await expect(page.getByText(/perfect fit|comfortable|true to size/i)).toBeVisible();
        
        // Close size guide
        const closeButton = page.getByRole('button', { name: /close/i });
        await closeButton.click();
      }
      
      // Reviews should build confidence
      const reviews = page.getByTestId('product-reviews');
      if (await reviews.isVisible()) {
        const reviewText = await reviews.textContent();
        expect(reviewText).toMatch(/love|amazing|perfect|comfortable|quality/i);
      }
    });
  });

  test('should handle mobile product discovery with touch-optimized experience', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await test.step('Mobile category selection should be touch-friendly', async () => {
      await productHelpers.goToProductSelection();
      
      // Category cards should be large enough for touch
      const categoryCards = page.getByTestId(/category-/);
      const cardCount = await categoryCards.count();
      
      for (let i = 0; i < Math.min(cardCount, 3); i++) {
        const card = categoryCards.nth(i);
        const boundingBox = await card.boundingBox();
        if (boundingBox) {
          expect(boundingBox.height).toBeGreaterThanOrEqual(120);
          expect(boundingBox.width).toBeGreaterThanOrEqual(150);
        }
      }
    });

    await test.step('Mobile product grid should be optimized for scrolling', async () => {
      await productHelpers.selectCategory('t-shirts');
      
      // Products should be in mobile-friendly grid
      const productGrid = page.getByTestId('product-grid');
      await expect(productGrid).toBeVisible();
      
      // Test infinite scroll or pagination
      const initialProductCount = await page.getByTestId(/product-card/).count();
      
      // Scroll to bottom
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(2000);
      
      const finalProductCount = await page.getByTestId(/product-card/).count();
      // Should either load more products or show pagination
      expect(finalProductCount).toBeGreaterThanOrEqual(initialProductCount);
    });

    await test.step('Mobile product details should be swipe-friendly', async () => {
      await productHelpers.selectProduct();
      
      // Product images should support swipe gestures
      const productGallery = page.getByTestId('product-gallery');
      await expect(productGallery).toBeVisible();
      
      // Test swipe gesture simulation
      const galleryBox = await productGallery.boundingBox();
      if (galleryBox) {
        // Simulate swipe left
        await page.touchscreen.tap(galleryBox.x + galleryBox.width * 0.8, galleryBox.y + galleryBox.height * 0.5);
        await page.waitForTimeout(500);
        
        // Simulate swipe right
        await page.touchscreen.tap(galleryBox.x + galleryBox.width * 0.2, galleryBox.y + galleryBox.height * 0.5);
      }
      
      // Customize button should be prominent and touch-friendly
      const customizeButton = page.getByRole('button', { name: /customize|design|create/i });
      await expect(customizeButton).toBeVisible();
      
      const buttonBox = await customizeButton.boundingBox();
      if (buttonBox) {
        expect(buttonBox.height).toBeGreaterThanOrEqual(48);
      }
    });
  });

  test('should provide emotional filtering and search experience', async ({ page }) => {
    await test.step('Search should understand emotional intent', async () => {
      await productHelpers.goToProductSelection();
      
      const searchInput = page.getByTestId('product-search');
      if (await searchInput.isVisible()) {
        // Test emotional search terms
        await searchInput.fill('cozy vibes');
        await searchInput.press('Enter');
        
        await page.waitForLoadState('networkidle');
        
        // Should show relevant products with emotional context
        const searchResults = page.getByTestId('search-results');
        await expect(searchResults).toBeVisible();
        
        const resultText = await searchResults.textContent();
        expect(resultText).toMatch(/cozy|comfort|warm|soft/i);
      }
    });

    await test.step('Filters should support lifestyle preferences', async () => {
      await productHelpers.selectCategory('hoodies');
      
      const filterPanel = page.getByTestId('filter-panel');
      if (await filterPanel.isVisible()) {
        // Test style filters
        const styleFilter = page.getByTestId('filter-style');
        if (await styleFilter.isVisible()) {
          const casualOption = styleFilter.getByText(/casual|relaxed|everyday/i);
          if (await casualOption.isVisible()) {
            await casualOption.click();
            await page.waitForTimeout(1000);
            
            // Results should update
            const filteredResults = page.getByTestId(/product-card/);
            const resultCount = await filteredResults.count();
            expect(resultCount).toBeGreaterThan(0);
          }
        }
        
        // Test color mood filters
        const colorFilter = page.getByTestId('filter-color');
        if (await colorFilter.isVisible()) {
          const earthyColors = colorFilter.getByText(/earthy|natural|warm/i);
          if (await earthyColors.isVisible()) {
            await earthyColors.click();
            await page.waitForTimeout(1000);
          }
        }
      }
    });

    await test.step('Sorting should prioritize emotional relevance', async () => {
      const sortDropdown = page.getByTestId('sort-dropdown');
      if (await sortDropdown.isVisible()) {
        await sortDropdown.click();
        
        // Should have emotional sorting options
        const sortOptions = [
          /most popular/i,
          /trending/i,
          /newest/i,
          /best rated/i
        ];
        
        for (const optionPattern of sortOptions) {
          const option = page.getByText(optionPattern);
          if (await option.isVisible()) {
            await option.click();
            await page.waitForTimeout(1000);
            
            // Verify results are sorted
            const products = page.getByTestId(/product-card/);
            const productCount = await products.count();
            expect(productCount).toBeGreaterThan(0);
            
            // Re-open dropdown for next option
            if (sortOptions.indexOf(optionPattern) < sortOptions.length - 1) {
              await sortDropdown.click();
            }
          }
        }
      }
    });
  });

  test('should build anticipation for customization', async ({ page }) => {
    await test.step('Product selection should tease customization possibilities', async () => {
      await productHelpers.selectCategory('t-shirts');
      await productHelpers.selectProduct();
      
      // Should show customization preview
      const customizationTeaser = page.getByTestId('customization-teaser');
      if (await customizationTeaser.isVisible()) {
        await expect(page.getByText(/add your text|upload your art|make it unique/i)).toBeVisible();
        
        // Should show example customizations
        const examples = page.getByTestId('customization-examples');
        if (await examples.isVisible()) {
          const exampleImages = examples.getByRole('img');
          const exampleCount = await exampleImages.count();
          expect(exampleCount).toBeGreaterThan(0);
        }
      }
      
      // Customize button should be prominent and exciting
      const customizeButton = page.getByRole('button', { name: /customize|design|create|make it yours/i });
      await expect(customizeButton).toBeVisible();
      
      // Button should have engaging copy
      const buttonText = await customizeButton.textContent();
      expect(buttonText).toMatch(/customize|design|create|make it yours|start creating/i);
    });

    await test.step('Customization entry should build excitement', async () => {
      const customizeButton = page.getByRole('button', { name: /customize|design|create/i });
      await customizeButton.click();
      
      // Should show loading state with exciting messaging
      const loadingState = page.getByTestId('customization-loading');
      if (await loadingState.isVisible()) {
        const loadingText = await loadingState.textContent();
        expect(loadingText).toMatch(/preparing|loading|getting ready|almost there/i);
      }
      
      // Should transition to customization interface
      await page.waitForLoadState('networkidle');
      
      // Verify we're in customization mode
      const designCanvas = page.getByTestId('design-canvas');
      await expect(designCanvas).toBeVisible();
      
      // Should show welcoming customization message
      await expect(page.getByText(/let's create|design your style|make it unique/i)).toBeVisible();
    });
  });
});
