/**
 * AI Try-On Ready Email Template
 * Notifies users when their AI try-on is complete
 */

import { Section, Text, Img } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { AiTryOnEmailData } from '../../lib/services/email';

interface AiTryOnReadyEmailProps extends AiTryOnEmailData {}

export function AiTryOnReadyEmail({
  customerName,
  productName,
  tryOnImageUrl,
  customizationName,
  orderUrl,
}: AiTryOnReadyEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText="Your AI try-on is ready! See how your custom design looks on you.">
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <EmailHeading level={1}>
          🤳 Your Try-On is Ready, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          The moment of truth has arrived! See how your custom "{customizationName}" design looks on you.
        </EmailText>
      </Section>

      {/* Try-On Result */}
      <Section className="text-center mb-8">
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg p-6">
          <EmailHeading level={2}>Your {productName}</EmailHeading>
          
          <div className="bg-white rounded-lg p-4 mb-6 inline-block">
            <Img
              src={tryOnImageUrl}
              alt={`AI try-on result for ${customizationName}`}
              width="300"
              height="400"
              className="rounded-lg shadow-lg"
            />
          </div>

          <EmailText className="text-center">
            <strong>"{customizationName}"</strong> - How does it look on you?
          </EmailText>
        </div>
      </Section>

      {/* Feedback Section */}
      <Section className="mb-8">
        <EmailHeading level={2}>Love What You See?</EmailHeading>
        
        <div className="grid grid-cols-1 gap-4">
          <div className="bg-green-50 rounded-lg p-4 text-center">
            <Text className="text-green-800 font-semibold mb-2">
              ✨ Perfect Fit!
            </Text>
            <Text className="text-green-700 text-sm mb-4">
              Ready to make this unique style yours? Your custom piece will be crafted with premium materials and attention to detail.
            </Text>
            <EmailButton href={orderUrl} variant="primary">
              Order This Design
            </EmailButton>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <Text className="text-blue-800 font-semibold mb-2">
              🎨 Want to Adjust?
            </Text>
            <Text className="text-blue-700 text-sm mb-4">
              Not quite right? No problem! Go back to the editor and make tweaks until it's perfect.
            </Text>
            <EmailButton href={`${baseUrl}/editor?design=${customizationName}`} variant="outline">
              Edit Design
            </EmailButton>
          </div>
        </div>
      </Section>

      {/* Style Tips */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>Styling Tips for Your Custom Piece</EmailHeading>
        
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">👕</Text>
            <EmailText className="mb-2">
              <strong>Versatile Styling:</strong> This design works great for casual outings, creative meetups, or making a statement at work.
            </EmailText>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">🎯</Text>
            <EmailText className="mb-2">
              <strong>Perfect Occasions:</strong> Ideal for expressing your personality at social events, creative spaces, or everyday confidence boosting.
            </EmailText>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">📸</Text>
            <EmailText className="mb-2">
              <strong>Share Your Style:</strong> This unique piece is perfect for your social media. Get ready for compliments and questions about where you got it!
            </EmailText>
          </div>
        </div>
      </Section>

      {/* Social Proof */}
      <Section className="text-center mb-8">
        <EmailHeading level={3}>Join Thousands of Style Creators</EmailHeading>
        
        <div className="bg-gradient-to-r from-accent-50 to-warm-50 rounded-lg p-6">
          <Text className="text-gray-700 italic mb-4">
            "I love how I can see exactly how my design will look before ordering. The AI try-on feature is a game-changer!" 
          </Text>
          <Text className="text-sm text-gray-600">
            - Sarah K., Ottiq Customer
          </Text>
        </div>

        <EmailText className="mt-4">
          Ready to join our community of confident style creators?
        </EmailText>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-8">
        <div className="space-y-4">
          <EmailButton href={orderUrl} variant="primary">
            Order This Design Now
          </EmailButton>
          
          <br />
          
          <EmailButton href={`${baseUrl}/create`} variant="outline">
            Create Another Design
          </EmailButton>
        </div>
      </Section>

      {/* Urgency/Scarcity (Optional) */}
      <Section className="bg-warm-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>⏰ Limited Time Offer</EmailHeading>
        <EmailText>
          Love your try-on result? Order within the next 24 hours and get free express shipping on your custom creation!
        </EmailText>
        
        <Text className="text-sm text-gray-600">
          Use code: TRYON24 at checkout
        </Text>
      </Section>
    </EmailLayout>
  );
}
