/**
 * Backup Trigger API Endpoint
 * Allows manual triggering of backup operations from admin dashboard
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

// Types
interface BackupTriggerRequest {
  type: 'all' | 'database' | 'minio';
  environment?: 'dev' | 'prod';
}

interface BackupTriggerResponse {
  success: boolean;
  message: string;
  jobId: string;
  timestamp: string;
  type: string;
  environment: string;
  output?: string;
  error?: string;
}

/**
 * Check if user has admin privileges
 */
async function checkAdminAccess(request: NextRequest): Promise<boolean> {
  // In a real implementation, you would check the user's session/token
  const adminKey = request.headers.get('x-admin-key');
  const expectedKey = process.env.ADMIN_API_KEY;
  
  if (!expectedKey) {
    // If no admin key is set, allow access in development
    return process.env.NODE_ENV === 'development';
  }
  
  return adminKey === expectedKey;
}

/**
 * Generate unique job ID for tracking
 */
function generateJobId(): string {
  return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Execute backup script
 */
async function executeBackup(
  type: 'all' | 'database' | 'minio',
  environment: string
): Promise<{ success: boolean; output: string; error?: string }> {
  const projectRoot = path.resolve(process.cwd());
  const scriptsDir = path.join(projectRoot, 'scripts');
  
  let scriptName: string;
  switch (type) {
    case 'all':
      scriptName = 'backup-all.sh';
      break;
    case 'database':
      scriptName = 'backup-database.sh';
      break;
    case 'minio':
      scriptName = 'backup-minio.sh';
      break;
    default:
      throw new Error(`Invalid backup type: ${type}`);
  }
  
  const scriptPath = path.join(scriptsDir, scriptName);
  
  try {
    // Check if we're on Windows and use PowerShell script instead
    const isWindows = process.platform === 'win32';
    const finalScriptPath = isWindows 
      ? scriptPath.replace('.sh', '.ps1')
      : scriptPath;
    
    const command = isWindows
      ? `powershell -ExecutionPolicy Bypass -File "${finalScriptPath}" ${environment}`
      : `bash "${finalScriptPath}" ${environment}`;
    
    console.log(`Executing backup command: ${command}`);
    
    // Set timeout to 30 minutes for backup operations
    const { stdout, stderr } = await execAsync(command, {
      timeout: 30 * 60 * 1000, // 30 minutes
      cwd: projectRoot,
      env: {
        ...process.env,
        PATH: process.env.PATH,
      },
    });
    
    const output = stdout + (stderr ? `\nSTDERR:\n${stderr}` : '');
    
    return {
      success: true,
      output,
    };
    
  } catch (error) {
    console.error(`Backup execution error:`, error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const output = error && typeof error === 'object' && 'stdout' in error 
      ? String(error.stdout) 
      : '';
    
    return {
      success: false,
      output,
      error: errorMessage,
    };
  }
}

/**
 * POST /api/admin/backup-trigger
 * Triggers a backup operation
 */
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    if (!(await checkAdminAccess(request))) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }
    
    // Parse request body
    let requestData: BackupTriggerRequest;
    try {
      requestData = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }
    
    // Validate request data
    const { type, environment = 'prod' } = requestData;
    
    if (!type || !['all', 'database', 'minio'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid backup type. Must be: all, database, or minio' },
        { status: 400 }
      );
    }
    
    if (!['dev', 'prod'].includes(environment)) {
      return NextResponse.json(
        { error: 'Invalid environment. Must be: dev or prod' },
        { status: 400 }
      );
    }
    
    // Generate job ID
    const jobId = generateJobId();
    const timestamp = new Date().toISOString();
    
    console.log(`Starting backup job ${jobId}: type=${type}, environment=${environment}`);
    
    // Execute backup
    const result = await executeBackup(type, environment);
    
    // Prepare response
    const response: BackupTriggerResponse = {
      success: result.success,
      message: result.success 
        ? `Backup ${type} completed successfully for ${environment} environment`
        : `Backup ${type} failed for ${environment} environment`,
      jobId,
      timestamp,
      type,
      environment,
      output: result.output,
    };
    
    if (!result.success) {
      response.error = result.error;
    }
    
    // Log result
    console.log(`Backup job ${jobId} completed:`, {
      success: result.success,
      type,
      environment,
      outputLength: result.output?.length || 0,
    });
    
    // Return response with appropriate status code
    const httpStatus = result.success ? 200 : 500;
    return NextResponse.json(response, { status: httpStatus });
    
  } catch (error) {
    console.error('Backup trigger API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/backup-trigger
 * Returns information about available backup operations
 */
export async function GET(request: NextRequest) {
  try {
    // Check admin access
    if (!(await checkAdminAccess(request))) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }
    
    return NextResponse.json({
      availableOperations: {
        all: {
          description: 'Complete backup of database and MinIO storage',
          estimatedDuration: '5-15 minutes',
          scripts: ['backup-all.sh', 'backup-all.ps1'],
        },
        database: {
          description: 'PostgreSQL database backup only',
          estimatedDuration: '1-5 minutes',
          scripts: ['backup-database.sh', 'backup-database.ps1'],
        },
        minio: {
          description: 'MinIO object storage backup only',
          estimatedDuration: '2-10 minutes',
          scripts: ['backup-minio.sh', 'backup-minio.ps1'],
        },
      },
      environments: ['dev', 'prod'],
      platform: process.platform,
      usage: {
        endpoint: '/api/admin/backup-trigger',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-key': 'your-admin-key (if configured)',
        },
        body: {
          type: 'all | database | minio',
          environment: 'dev | prod (optional, defaults to prod)',
        },
      },
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('Backup trigger info API error:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
