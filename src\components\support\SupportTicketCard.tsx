'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui';
import { formatDistanceToNow } from 'date-fns';

interface SupportTicket {
  id: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  messages: Array<{
    id: string;
    content: string;
    messageType: string;
    senderRole: string;
    createdAt: string;
    sender: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  _count: {
    messages: number;
  };
}

interface SupportTicketCardProps {
  ticket: SupportTicket;
  onUpdate: () => void;
  getStatusColor: (status: string) => string;
  getPriorityColor: (priority: string) => string;
}

export function SupportTicketCard({ 
  ticket, 
  onUpdate, 
  getStatusColor, 
  getPriorityColor 
}: SupportTicketCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleViewTicket = () => {
    window.location.href = `/support/tickets/${ticket.id}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return '🔵';
      case 'IN_PROGRESS':
        return '🟡';
      case 'WAITING_FOR_CUSTOMER':
        return '🟠';
      case 'WAITING_FOR_ADMIN':
        return '🟣';
      case 'RESOLVED':
        return '✅';
      case 'CLOSED':
        return '⚫';
      default:
        return '⚪';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'order':
        return '📦';
      case 'product':
        return '👕';
      case 'technical':
        return '🔧';
      case 'billing':
        return '💳';
      case 'general':
        return '💬';
      default:
        return '❓';
    }
  };

  const latestMessage = ticket.messages[0];
  const hasUnreadMessages = latestMessage && latestMessage.senderRole === 'ADMIN';

  return (
    <Card className={`hover:shadow-lg transition-all duration-200 ${hasUnreadMessages ? 'ring-2 ring-warm-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">{getCategoryIcon(ticket.category)}</span>
              <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-1">
                {ticket.subject}
              </CardTitle>
              {hasUnreadMessages && (
                <span className="bg-warm-500 text-white text-xs px-2 py-1 rounded-full">
                  New Reply
                </span>
              )}
            </div>
            
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <span className="font-mono">#{ticket.id.slice(-8)}</span>
              <span>•</span>
              <span className="capitalize">{ticket.category}</span>
              <span>•</span>
              <span>{formatDistanceToNow(new Date(ticket.createdAt), { addSuffix: true })}</span>
            </div>
          </div>

          <div className="flex flex-col items-end gap-2">
            <div className="flex items-center gap-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                {ticket.priority}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                {getStatusIcon(ticket.status)} {ticket.status.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Description Preview */}
          <div>
            <p className={`text-gray-700 ${isExpanded ? '' : 'line-clamp-2'}`}>
              {ticket.description}
            </p>
            {ticket.description.length > 150 && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-warm-600 hover:text-warm-700 text-sm font-medium mt-1"
              >
                {isExpanded ? 'Show less' : 'Show more'}
              </button>
            )}
          </div>

          {/* Latest Message Preview */}
          {latestMessage && (
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900">
                  {latestMessage.senderRole === 'ADMIN' ? '👨‍💼 Support Team' : '👤 You'}
                </span>
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(latestMessage.createdAt), { addSuffix: true })}
                </span>
              </div>
              <p className="text-sm text-gray-700 line-clamp-2">
                {latestMessage.content}
              </p>
            </div>
          )}

          {/* Stats and Actions */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                💬 {ticket._count.messages} messages
              </span>
              <span className="flex items-center gap-1">
                🕒 Updated {formatDistanceToNow(new Date(ticket.updatedAt), { addSuffix: true })}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewTicket}
              >
                View Details
              </Button>
              
              {ticket.status === 'WAITING_FOR_CUSTOMER' && (
                <Button
                  size="sm"
                  onClick={handleViewTicket}
                  className="bg-warm-500 hover:bg-warm-600"
                >
                  Reply Now
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
