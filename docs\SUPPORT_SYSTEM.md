# Customer Support System Documentation

## Overview

The Ottiq Customer Support System is a comprehensive solution designed to provide exceptional customer service through multiple channels including support tickets, AI-powered chatbot assistance, and admin management tools.

## Features

### 🎫 Support Tickets
- **Customer Ticket Creation**: Easy-to-use modal for creating support tickets
- **Ticket Management**: Full CRUD operations for tickets
- **Status Tracking**: Real-time status updates (Open, In Progress, Waiting, Resolved, Closed)
- **Priority Levels**: Low, Medium, High, and Urgent priority classification
- **Category Organization**: Order, Product, Technical, Billing, and General categories

### 💬 Real-time Messaging
- **Threaded Conversations**: Full conversation history for each ticket
- **Message Types**: Text, images, and file attachments
- **Read Status**: Track message read status for both customers and admins
- **Internal Notes**: Admin-only internal messages for team coordination

### 🤖 AI Chatbot Assistant
- **Instant Responses**: 24/7 AI-powered support for common questions
- **Smart Routing**: Intelligent question categorization and response
- **Escalation**: Seamless transition from chatbot to human support
- **Self-Service**: Guided troubleshooting and help resources

### 📊 Admin Dashboard
- **Ticket Overview**: Comprehensive dashboard with statistics and metrics
- **Queue Management**: Prioritized ticket queues and assignment
- **Performance Analytics**: Response times, resolution rates, and satisfaction scores
- **Bulk Operations**: Mass ticket updates and management

### 📧 Email Notifications
- **Automated Alerts**: Email notifications for ticket updates
- **Customer Communications**: Professional email templates for all interactions
- **Admin Notifications**: Real-time alerts for new tickets and customer replies
- **Customizable Templates**: Branded email templates with dynamic content

## Architecture

### Database Schema

```sql
-- Support Tickets
model SupportTicket {
  id          String   @id @default(cuid())
  subject     String
  description String   @db.Text
  status      SupportTicketStatus @default(OPEN)
  priority    SupportTicketPriority @default(MEDIUM)
  category    String?
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  messages    SupportMessage[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

-- Support Messages
model SupportMessage {
  id          String   @id @default(cuid())
  content     String   @db.Text
  messageType SupportMessageType @default(TEXT)
  senderId    String
  sender      User     @relation(fields: [senderId], references: [id])
  senderRole  SupportSenderRole
  ticketId    String
  ticket      SupportTicket @relation(fields: [ticketId], references: [id])
  isInternal  Boolean  @default(false)
  createdAt   DateTime @default(now())
}
```

### API Endpoints

#### Customer Endpoints
- `POST /api/support/tickets` - Create new ticket
- `GET /api/support/tickets` - List user's tickets
- `GET /api/support/tickets/[id]` - Get specific ticket
- `PUT /api/support/tickets/[id]` - Update ticket (feedback)
- `POST /api/support/tickets/[id]/messages` - Add message
- `GET /api/support/tickets/[id]/messages` - Get messages
- `GET /api/support/stats` - User support statistics

#### Admin Endpoints
- `GET /api/admin/support/tickets` - List all tickets
- `PUT /api/admin/support/tickets` - Bulk update tickets
- `GET /api/admin/support/tickets/[id]` - Get ticket (admin view)
- `PUT /api/admin/support/tickets/[id]` - Update ticket (admin)
- `DELETE /api/admin/support/tickets/[id]` - Delete ticket
- `GET /api/admin/support/stats` - Admin statistics

## Components

### Customer Components
- `SupportTicketList` - Display user's tickets
- `SupportTicketCard` - Individual ticket preview
- `CreateTicketModal` - Ticket creation form
- `SupportChatBot` - AI assistant interface

### Admin Components
- `AdminSupportTicketList` - Admin ticket management
- `AdminSupportTicketCard` - Admin ticket preview
- `AdminSupportStats` - Analytics dashboard

## Configuration

### Environment Variables

```env
# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# Email Service
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password

# Application URLs
APP_URL=https://ottiq.com
```

### Email Templates

The system includes professional email templates for:
- New ticket creation (admin notification)
- Support team replies (customer notification)
- Customer replies (admin notification)
- Ticket resolution (customer notification)
- Ticket closure (customer notification)

## Usage

### For Customers

1. **Creating a Ticket**:
   - Visit `/support`
   - Click "Create Ticket" or use the floating chat button
   - Fill out the form with subject, description, category, and priority
   - Submit to create the ticket

2. **Managing Tickets**:
   - View all tickets on the support page
   - Click on any ticket to view the full conversation
   - Reply to tickets by typing in the message box
   - Rate your experience when tickets are resolved

3. **Using the AI Assistant**:
   - Click the floating chat button
   - Ask questions or select from predefined options
   - Get instant answers or escalate to human support

### For Admins

1. **Accessing Admin Dashboard**:
   - Navigate to `/admin/support`
   - View overview statistics and priority tickets
   - Use filters to find specific tickets

2. **Managing Tickets**:
   - Click on tickets to view full details
   - Reply to customers directly
   - Update ticket status and priority
   - Add internal notes for team coordination

3. **Analytics**:
   - View response time metrics
   - Monitor customer satisfaction scores
   - Track resolution rates and team performance

## Testing

### Running Tests

```bash
# Run all support system tests
npm run test:support

# Run specific test suites
node scripts/test-support-system.js --api
node scripts/test-support-system.js --components
node scripts/test-support-system.js --all --coverage

# Run with type checking and linting
node scripts/test-support-system.js --type-check --lint
```

### Test Coverage

The test suite covers:
- API endpoint functionality
- Database operations
- Component rendering and interactions
- User workflows
- Error handling
- Authentication and authorization

## Deployment

### Database Migration

```bash
# Generate and apply database changes
npx prisma generate
npx prisma db push
```

### Environment Setup

1. Configure admin emails in environment variables
2. Set up SMTP credentials for email notifications
3. Configure application URLs for email links
4. Test email delivery in staging environment

## Monitoring

### Key Metrics to Monitor

- **Response Time**: Average time to first admin response
- **Resolution Rate**: Percentage of tickets resolved
- **Customer Satisfaction**: Average rating from feedback
- **Ticket Volume**: Number of tickets created per day/week
- **Category Distribution**: Most common support categories

### Alerts

Set up monitoring for:
- High priority tickets waiting too long
- Unusual spike in ticket volume
- Low customer satisfaction scores
- Email delivery failures

## Best Practices

### For Support Team

1. **Response Times**:
   - Acknowledge tickets within 1 hour during business hours
   - Provide first response within 2 hours for high priority
   - Update customers regularly on progress

2. **Communication**:
   - Use clear, friendly language
   - Provide step-by-step solutions
   - Ask clarifying questions when needed
   - Follow up to ensure satisfaction

3. **Ticket Management**:
   - Update ticket status promptly
   - Use internal notes for team coordination
   - Categorize tickets accurately
   - Close resolved tickets after confirmation

### For Development

1. **Code Quality**:
   - Write comprehensive tests for new features
   - Follow TypeScript best practices
   - Use proper error handling
   - Document API changes

2. **Performance**:
   - Optimize database queries
   - Implement proper pagination
   - Cache frequently accessed data
   - Monitor API response times

## Troubleshooting

### Common Issues

1. **Email Not Sending**:
   - Check SMTP configuration
   - Verify email credentials
   - Test with email service provider

2. **Tickets Not Loading**:
   - Check database connection
   - Verify user authentication
   - Review API error logs

3. **Chatbot Not Responding**:
   - Check component mounting
   - Verify option click handlers
   - Review console for JavaScript errors

### Support Contacts

For technical issues with the support system:
- Development Team: <EMAIL>
- System Administrator: <EMAIL>
- Emergency Contact: +880-XXX-XXXX
