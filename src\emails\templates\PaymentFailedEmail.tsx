/**
 * Payment Failed Email Template
 * Handles payment failures with helpful guidance and retry options
 */

import { Section, Text, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { PaymentEmailData } from '../../lib/services/email';

interface PaymentFailedEmailProps extends PaymentEmailData {
  failureReason?: string;
  retryUrl?: string;
}

export function PaymentFailedEmail({
  customerName,
  orderNumber,
  amount,
  currency,
  paymentMethod,
  failureReason,
  retryUrl,
  orderUrl,
}: PaymentFailedEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';
  const finalRetryUrl = retryUrl || `${orderUrl}?retry=payment`;

  return (
    <EmailLayout previewText={`Payment issue with order #${orderNumber}. Let's get this sorted quickly!`}>
      {/* Header */}
      <Section className="text-center mb-8">
        <EmailHeading level={1}>
          Payment Issue - Order #{orderNumber}
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Hi {customerName}, we encountered an issue processing your payment. Don't worry - your order is still reserved and we can fix this quickly!
        </EmailText>
      </Section>

      {/* Payment Issue Alert */}
      <Section className="bg-red-50 border-l-4 border-red-400 rounded-lg p-6 mb-8">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
            <Text className="text-red-600 font-bold">!</Text>
          </div>
          <div className="flex-1">
            <EmailHeading level={2}>Payment Not Processed</EmailHeading>
            <EmailText>
              We were unable to process your payment for this order. This can happen for various reasons, but it's usually easy to resolve.
            </EmailText>
          </div>
        </div>
      </Section>

      {/* Order Details */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={2}>Order Details</EmailHeading>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <Text className="text-gray-700">Order Number:</Text>
            <Text className="font-semibold text-gray-900">#{orderNumber}</Text>
          </div>
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Amount:</Text>
            <Text className="font-semibold text-gray-900">{currency} {amount}</Text>
          </div>
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Payment Method:</Text>
            <Text className="font-semibold text-gray-900 capitalize">{paymentMethod}</Text>
          </div>
          
          {failureReason && (
            <div className="flex justify-between">
              <Text className="text-gray-700">Issue:</Text>
              <Text className="font-semibold text-red-600">{failureReason}</Text>
            </div>
          )}
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Status:</Text>
            <Text className="font-semibold text-red-600">Payment Failed</Text>
          </div>
        </div>
      </Section>

      {/* Common Solutions */}
      <Section className="mb-8">
        <EmailHeading level={2}>💡 Common Solutions</EmailHeading>
        <EmailText>
          Here are the most common reasons for payment issues and how to fix them:
        </EmailText>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">💳</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Card Information
              </Text>
              <Text className="text-gray-600 text-sm">
                Double-check your card number, expiry date, and CVV. Make sure your billing address matches your card statement.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">💰</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Insufficient Funds
              </Text>
              <Text className="text-gray-600 text-sm">
                Ensure your account has sufficient balance or available credit limit for this transaction.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">🏦</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Bank Security
              </Text>
              <Text className="text-gray-600 text-sm">
                Your bank might have flagged this as unusual activity. Contact them to authorize the transaction.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">🌐</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                International Cards
              </Text>
              <Text className="text-gray-600 text-sm">
                Make sure your card is enabled for international transactions if you're ordering from outside Bangladesh.
              </Text>
            </div>
          </div>
        </div>
      </Section>

      {/* Quick Action */}
      <Section className="bg-green-50 rounded-lg p-6 mb-8 text-center">
        <EmailHeading level={2}>🚀 Try Again Now</EmailHeading>
        <EmailText>
          Your order is still reserved for the next 24 hours. Click below to retry your payment with the same or different payment method.
        </EmailText>
        
        <EmailButton href={finalRetryUrl} variant="primary">
          Retry Payment
        </EmailButton>
      </Section>

      {/* Alternative Payment Methods */}
      <Section className="mb-8">
        <EmailHeading level={2}>💳 Try a Different Payment Method</EmailHeading>
        <EmailText>
          If your current payment method isn't working, we accept several alternatives:
        </EmailText>
        
        <div className="grid grid-cols-1 gap-3">
          <div className="bg-blue-50 rounded-lg p-4">
            <Text className="font-semibold text-blue-900 mb-1">bKash</Text>
            <Text className="text-blue-700 text-sm">
              Quick and secure mobile payment - most popular in Bangladesh
            </Text>
          </div>
          
          <div className="bg-purple-50 rounded-lg p-4">
            <Text className="font-semibold text-purple-900 mb-1">Credit/Debit Cards</Text>
            <Text className="text-purple-700 text-sm">
              Visa, Mastercard, and local bank cards accepted
            </Text>
          </div>
          
          <div className="bg-green-50 rounded-lg p-4">
            <Text className="font-semibold text-green-900 mb-1">Bank Transfer</Text>
            <Text className="text-green-700 text-sm">
              Direct bank transfer for larger orders
            </Text>
          </div>
        </div>
      </Section>

      {/* Urgency */}
      <Section className="bg-yellow-50 border-l-4 border-yellow-400 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>⏰ Order Reservation</EmailHeading>
        <EmailText>
          Your custom design and order details are reserved for <strong>24 hours</strong>. After this time, you'll need to place the order again.
        </EmailText>
        
        <Text className="text-sm text-yellow-700">
          Don't lose your unique creation - complete your payment soon!
        </Text>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-8">
        <div className="space-y-3">
          <EmailButton href={finalRetryUrl} variant="primary">
            Complete Payment Now
          </EmailButton>
          
          <br />
          
          <EmailButton href={orderUrl} variant="outline">
            View Order Details
          </EmailButton>
        </div>
      </Section>

      {/* Support */}
      <Section className="bg-blue-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>Need Help?</EmailHeading>
        <EmailText>
          If you're still having trouble with payment, our support team is ready to help you complete your order.
        </EmailText>
        
        <div className="space-y-3">
          <EmailButton href={`${baseUrl}/support?order=${orderNumber}`} variant="outline">
            Get Payment Help
          </EmailButton>
          
          <Text className="text-sm text-gray-600">
            Or call us at +880-XXX-XXXX (9 AM - 9 PM, 7 days a week)
          </Text>
        </div>
      </Section>

      {/* Footer */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          We're here to make sure you get your custom creation. Let's solve this together!
        </EmailText>
      </Section>
    </EmailLayout>
  );
}
