/**
 * Support Ticket Messages API Routes
 * Handles message creation and retrieval for support tickets
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { emailService } from '@/lib/services/email';

// Validation schemas
const CreateMessageSchema = z.object({
  content: z.string().min(1).max(5000),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE']).optional().default('TEXT'),
  isInternal: z.boolean().optional().default(false),
  attachments: z.array(z.object({
    url: z.string().url(),
    filename: z.string(),
    size: z.number(),
    type: z.string(),
  })).optional(),
});

/**
 * POST /api/support/tickets/[id]/messages - Add a message to a ticket
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = CreateMessageSchema.parse(body);

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    // Verify ticket exists and user has permission
    const where: any = { id: params.id };
    if (!isAdmin) {
      where.userId = session.user.id;
    }

    const ticket = await prisma.supportTicket.findFirst({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Check if ticket is closed
    if (ticket.status === 'CLOSED' && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Cannot add messages to closed tickets' },
        { status: 400 }
      );
    }

    // Create the message
    const message = await prisma.supportMessage.create({
      data: {
        content: validatedData.content,
        messageType: validatedData.messageType,
        senderId: session.user.id,
        senderRole: isAdmin ? 'ADMIN' : 'CUSTOMER',
        ticketId: params.id,
        isInternal: validatedData.isInternal && isAdmin, // Only admins can create internal messages
        attachments: validatedData.attachments || null,
        readByCustomer: !isAdmin, // If customer sent it, mark as read by customer
        readByAdmin: isAdmin, // If admin sent it, mark as read by admin
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Update ticket status and timestamp
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Auto-update ticket status based on who's responding
    if (isAdmin && ticket.status === 'WAITING_FOR_ADMIN') {
      updateData.status = 'WAITING_FOR_CUSTOMER';
    } else if (!isAdmin && ticket.status === 'WAITING_FOR_CUSTOMER') {
      updateData.status = 'WAITING_FOR_ADMIN';
    } else if (!isAdmin && ticket.status === 'OPEN') {
      updateData.status = 'WAITING_FOR_ADMIN';
    }

    await prisma.supportTicket.update({
      where: { id: params.id },
      data: updateData,
    });

    // Send notification emails
    try {
      if (isAdmin && !validatedData.isInternal) {
        // Admin replied to customer - notify customer
        await emailService.sendEmail('support-ticket-reply', {
          to: ticket.user.email || '',
          customerName: ticket.user.name || 'Customer',
          ticketId: params.id,
          subject: ticket.subject,
          message: validatedData.content,
          senderName: session.user.name || 'Support Team',
          ticketUrl: `${process.env.APP_URL}/support/tickets/${params.id}`,
        });
      } else if (!isAdmin) {
        // Customer replied - notify admins
        const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
        for (const adminEmail of adminEmails) {
          await emailService.sendEmail('support-ticket-customer-reply', {
            to: adminEmail.trim(),
            customerName: ticket.user.name || 'Customer',
            ticketId: params.id,
            subject: ticket.subject,
            message: validatedData.content,
            ticketUrl: `${process.env.APP_URL}/admin/support/tickets/${params.id}`,
          });
        }
      }
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: message,
    });
  } catch (error) {
    console.error('Error creating support message:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create support message',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/support/tickets/[id]/messages - Get messages for a ticket
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    const skip = (page - 1) * limit;

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    // Verify ticket exists and user has permission
    const where: any = { id: params.id };
    if (!isAdmin) {
      where.userId = session.user.id;
    }

    const ticket = await prisma.supportTicket.findFirst({
      where,
    });

    if (!ticket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Build message where clause
    const messageWhere: any = {
      ticketId: params.id,
    };

    // Hide internal messages from customers
    if (!isAdmin) {
      messageWhere.isInternal = false;
    }

    // Get messages with pagination
    const [messages, total] = await Promise.all([
      prisma.supportMessage.findMany({
        where: messageWhere,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
        skip,
        take: limit,
      }),
      prisma.supportMessage.count({ where: messageWhere }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        messages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching support messages:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch support messages',
      },
      { status: 500 }
    );
  }
}
