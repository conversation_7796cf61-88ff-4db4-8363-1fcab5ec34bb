#!/usr/bin/env node

/**
 * Ottiq Backup Monitoring System
 * TypeScript-based backup monitoring with web dashboard and alerting
 */

import { promises as fs } from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

// Configuration
interface BackupConfig {
  environment: string;
  backupRoot: string;
  maxBackupAgeHours: number;
  diskUsageThreshold: number;
  alertWebhookUrl?: string;
  emailConfig?: {
    smtp: string;
    from: string;
    to: string[];
  };
}

interface BackupStatus {
  timestamp: string;
  environment: string;
  database: {
    lastBackup: string | null;
    ageHours: number;
    size: string;
    status: 'healthy' | 'warning' | 'critical';
  };
  minio: {
    lastBackup: string | null;
    ageHours: number;
    size: string;
    status: 'healthy' | 'warning' | 'critical';
  };
  storage: {
    usagePercent: number;
    totalSize: string;
    availableSpace: string;
    status: 'healthy' | 'warning' | 'critical';
  };
  services: {
    postgres: boolean;
    minio: boolean;
    status: 'healthy' | 'critical';
  };
  overallStatus: 'healthy' | 'warning' | 'critical';
  alerts: string[];
}

class BackupMonitor {
  private config: BackupConfig;
  private projectRoot: string;

  constructor(environment: string = 'prod') {
    this.projectRoot = path.resolve(__dirname, '..');
    this.config = {
      environment,
      backupRoot: path.join(this.projectRoot, 'backups'),
      maxBackupAgeHours: 26,
      diskUsageThreshold: 85,
      alertWebhookUrl: process.env.BACKUP_WEBHOOK_URL,
      emailConfig: process.env.SMTP_HOST ? {
        smtp: process.env.SMTP_HOST,
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: (process.env.ADMIN_EMAILS || '').split(',').filter(Boolean),
      } : undefined,
    };
  }

  /**
   * Get the status of the most recent database backup
   */
  private async getDatabaseBackupStatus(): Promise<BackupStatus['database']> {
    try {
      const dbBackupDir = path.join(this.config.backupRoot, 'database');
      const pattern = `ottiq_db_${this.config.environment}_*.sql.gz`;
      
      const { stdout } = await execAsync(
        `find "${dbBackupDir}" -name "${pattern}" -type f -printf '%T@ %p\\n' 2>/dev/null | sort -n | tail -1`
      );

      if (!stdout.trim()) {
        return {
          lastBackup: null,
          ageHours: Infinity,
          size: '0',
          status: 'critical',
        };
      }

      const [timestamp, filePath] = stdout.trim().split(' ', 2);
      const backupTime = new Date(parseFloat(timestamp) * 1000);
      const ageHours = (Date.now() - backupTime.getTime()) / (1000 * 60 * 60);

      // Get file size
      const { stdout: sizeOutput } = await execAsync(`du -h "${filePath}" | cut -f1`);
      const size = sizeOutput.trim();

      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (ageHours > this.config.maxBackupAgeHours) {
        status = 'critical';
      } else if (ageHours > this.config.maxBackupAgeHours * 0.8) {
        status = 'warning';
      }

      return {
        lastBackup: backupTime.toISOString(),
        ageHours: Math.round(ageHours * 10) / 10,
        size,
        status,
      };
    } catch (error) {
      console.error('Error checking database backup status:', error);
      return {
        lastBackup: null,
        ageHours: Infinity,
        size: '0',
        status: 'critical',
      };
    }
  }

  /**
   * Get the status of the most recent MinIO backup
   */
  private async getMinioBackupStatus(): Promise<BackupStatus['minio']> {
    try {
      const minioBackupDir = path.join(this.config.backupRoot, 'minio');
      const pattern = `ottiq_minio_${this.config.environment}_*.tar.gz`;
      
      const { stdout } = await execAsync(
        `find "${minioBackupDir}" -name "${pattern}" -type f -printf '%T@ %p\\n' 2>/dev/null | sort -n | tail -1`
      );

      if (!stdout.trim()) {
        return {
          lastBackup: null,
          ageHours: Infinity,
          size: '0',
          status: 'critical',
        };
      }

      const [timestamp, filePath] = stdout.trim().split(' ', 2);
      const backupTime = new Date(parseFloat(timestamp) * 1000);
      const ageHours = (Date.now() - backupTime.getTime()) / (1000 * 60 * 60);

      // Get file size
      const { stdout: sizeOutput } = await execAsync(`du -h "${filePath}" | cut -f1`);
      const size = sizeOutput.trim();

      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (ageHours > this.config.maxBackupAgeHours) {
        status = 'critical';
      } else if (ageHours > this.config.maxBackupAgeHours * 0.8) {
        status = 'warning';
      }

      return {
        lastBackup: backupTime.toISOString(),
        ageHours: Math.round(ageHours * 10) / 10,
        size,
        status,
      };
    } catch (error) {
      console.error('Error checking MinIO backup status:', error);
      return {
        lastBackup: null,
        ageHours: Infinity,
        size: '0',
        status: 'critical',
      };
    }
  }

  /**
   * Get storage usage status
   */
  private async getStorageStatus(): Promise<BackupStatus['storage']> {
    try {
      const { stdout } = await execAsync(`df -h "${this.config.backupRoot}"`);
      const lines = stdout.trim().split('\n');
      const dataLine = lines[lines.length - 1];
      const parts = dataLine.split(/\s+/);
      
      const usagePercent = parseInt(parts[4].replace('%', ''));
      const totalSize = parts[1];
      const availableSpace = parts[3];

      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (usagePercent >= 95) {
        status = 'critical';
      } else if (usagePercent >= this.config.diskUsageThreshold) {
        status = 'warning';
      }

      return {
        usagePercent,
        totalSize,
        availableSpace,
        status,
      };
    } catch (error) {
      console.error('Error checking storage status:', error);
      return {
        usagePercent: 0,
        totalSize: 'unknown',
        availableSpace: 'unknown',
        status: 'critical',
      };
    }
  }

  /**
   * Check if Docker services are running
   */
  private async getServicesStatus(): Promise<BackupStatus['services']> {
    try {
      const { stdout } = await execAsync('docker ps --format "table {{.Names}}"');
      const runningContainers = stdout.split('\n').map(line => line.trim());

      const postgres = runningContainers.includes('ottiq-postgres');
      const minio = runningContainers.includes('ottiq-minio');

      const status = postgres && minio ? 'healthy' : 'critical';

      return {
        postgres,
        minio,
        status,
      };
    } catch (error) {
      console.error('Error checking services status:', error);
      return {
        postgres: false,
        minio: false,
        status: 'critical',
      };
    }
  }

  /**
   * Generate alerts based on status
   */
  private generateAlerts(status: Omit<BackupStatus, 'alerts' | 'overallStatus'>): string[] {
    const alerts: string[] = [];

    // Database backup alerts
    if (status.database.status === 'critical') {
      if (status.database.lastBackup === null) {
        alerts.push('No database backup found');
      } else {
        alerts.push(`Database backup is ${status.database.ageHours}h old (max: ${this.config.maxBackupAgeHours}h)`);
      }
    } else if (status.database.status === 'warning') {
      alerts.push(`Database backup is getting old: ${status.database.ageHours}h`);
    }

    // MinIO backup alerts
    if (status.minio.status === 'critical') {
      if (status.minio.lastBackup === null) {
        alerts.push('No MinIO backup found');
      } else {
        alerts.push(`MinIO backup is ${status.minio.ageHours}h old (max: ${this.config.maxBackupAgeHours}h)`);
      }
    } else if (status.minio.status === 'warning') {
      alerts.push(`MinIO backup is getting old: ${status.minio.ageHours}h`);
    }

    // Storage alerts
    if (status.storage.status === 'critical') {
      alerts.push(`Critical disk usage: ${status.storage.usagePercent}%`);
    } else if (status.storage.status === 'warning') {
      alerts.push(`High disk usage: ${status.storage.usagePercent}%`);
    }

    // Service alerts
    if (status.services.status === 'critical') {
      if (!status.services.postgres) {
        alerts.push('PostgreSQL container is not running');
      }
      if (!status.services.minio) {
        alerts.push('MinIO container is not running');
      }
    }

    return alerts;
  }

  /**
   * Send alert notification
   */
  private async sendAlert(message: string): Promise<void> {
    console.log(`ALERT: ${message}`);

    // Log to alerts file
    const alertsFile = path.join(this.config.backupRoot, 'alerts.log');
    const alertEntry = `[${new Date().toISOString()}] ${message}\n`;
    await fs.appendFile(alertsFile, alertEntry).catch(console.error);

    // Send webhook notification
    if (this.config.alertWebhookUrl) {
      try {
        const response = await fetch(this.config.alertWebhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 Ottiq Backup Alert (${this.config.environment}): ${message}`,
            environment: this.config.environment,
            timestamp: new Date().toISOString(),
          }),
        });

        if (!response.ok) {
          console.error('Failed to send webhook alert:', response.statusText);
        }
      } catch (error) {
        console.error('Error sending webhook alert:', error);
      }
    }
  }

  /**
   * Get comprehensive backup status
   */
  public async getStatus(): Promise<BackupStatus> {
    console.log(`Checking backup status for ${this.config.environment} environment...`);

    const [database, minio, storage, services] = await Promise.all([
      this.getDatabaseBackupStatus(),
      this.getMinioBackupStatus(),
      this.getStorageStatus(),
      this.getServicesStatus(),
    ]);

    const partialStatus = {
      timestamp: new Date().toISOString(),
      environment: this.config.environment,
      database,
      minio,
      storage,
      services,
    };

    const alerts = this.generateAlerts(partialStatus);

    // Determine overall status
    let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (
      database.status === 'critical' ||
      minio.status === 'critical' ||
      storage.status === 'critical' ||
      services.status === 'critical'
    ) {
      overallStatus = 'critical';
    } else if (
      database.status === 'warning' ||
      minio.status === 'warning' ||
      storage.status === 'warning'
    ) {
      overallStatus = 'warning';
    }

    const status: BackupStatus = {
      ...partialStatus,
      alerts,
      overallStatus,
    };

    // Send alerts for critical issues
    if (overallStatus === 'critical') {
      for (const alert of alerts) {
        await this.sendAlert(alert);
      }
    }

    return status;
  }

  /**
   * Save status to file
   */
  public async saveStatus(status: BackupStatus): Promise<void> {
    const statusFile = path.join(this.config.backupRoot, 'status.json');
    await fs.writeFile(statusFile, JSON.stringify(status, null, 2));
  }

  /**
   * Generate HTML status report
   */
  public generateHtmlReport(status: BackupStatus): string {
    const statusColor = {
      healthy: '#10b981',
      warning: '#f59e0b',
      critical: '#ef4444',
    };

    return `
<!DOCTYPE html>
<html>
<head>
    <title>Ottiq Backup Status - ${status.environment}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; padding: 20px; background: #f8fafc; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .status-card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .healthy { background-color: ${statusColor.healthy}; }
        .warning { background-color: ${statusColor.warning}; }
        .critical { background-color: ${statusColor.critical}; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .alerts { background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin-top: 20px; }
        .alert-item { margin-bottom: 5px; color: #dc2626; }
        .timestamp { color: #6b7280; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <span class="status-indicator ${status.overallStatus}"></span>
                Ottiq Backup Status - ${status.environment.toUpperCase()}
            </h1>
            <p class="timestamp">Last updated: ${new Date(status.timestamp).toLocaleString()}</p>
        </div>

        <div class="grid">
            <div class="status-card">
                <h3><span class="status-indicator ${status.database.status}"></span>Database Backup</h3>
                <div class="metric"><span>Last Backup:</span><span>${status.database.lastBackup ? new Date(status.database.lastBackup).toLocaleString() : 'None'}</span></div>
                <div class="metric"><span>Age:</span><span>${status.database.ageHours}h</span></div>
                <div class="metric"><span>Size:</span><span>${status.database.size}</span></div>
            </div>

            <div class="status-card">
                <h3><span class="status-indicator ${status.minio.status}"></span>MinIO Backup</h3>
                <div class="metric"><span>Last Backup:</span><span>${status.minio.lastBackup ? new Date(status.minio.lastBackup).toLocaleString() : 'None'}</span></div>
                <div class="metric"><span>Age:</span><span>${status.minio.ageHours}h</span></div>
                <div class="metric"><span>Size:</span><span>${status.minio.size}</span></div>
            </div>

            <div class="status-card">
                <h3><span class="status-indicator ${status.storage.status}"></span>Storage</h3>
                <div class="metric"><span>Usage:</span><span>${status.storage.usagePercent}%</span></div>
                <div class="metric"><span>Total Size:</span><span>${status.storage.totalSize}</span></div>
                <div class="metric"><span>Available:</span><span>${status.storage.availableSpace}</span></div>
            </div>

            <div class="status-card">
                <h3><span class="status-indicator ${status.services.status}"></span>Services</h3>
                <div class="metric"><span>PostgreSQL:</span><span>${status.services.postgres ? '✅ Running' : '❌ Stopped'}</span></div>
                <div class="metric"><span>MinIO:</span><span>${status.services.minio ? '✅ Running' : '❌ Stopped'}</span></div>
            </div>
        </div>

        ${status.alerts.length > 0 ? `
        <div class="alerts">
            <h3>🚨 Active Alerts</h3>
            ${status.alerts.map(alert => `<div class="alert-item">• ${alert}</div>`).join('')}
        </div>
        ` : ''}
    </div>
</body>
</html>`;
  }
}

// CLI interface
async function main() {
  const environment = process.argv[2] || 'prod';
  const outputFormat = process.argv[3] || 'json';

  const monitor = new BackupMonitor(environment);
  const status = await monitor.getStatus();

  // Save status to file
  await monitor.saveStatus(status);

  // Output based on format
  if (outputFormat === 'html') {
    const html = monitor.generateHtmlReport(status);
    const htmlFile = path.join(monitor['config'].backupRoot, 'status.html');
    await fs.writeFile(htmlFile, html);
    console.log(`HTML report saved to: ${htmlFile}`);
  } else {
    console.log(JSON.stringify(status, null, 2));
  }

  // Exit with appropriate code
  process.exit(status.overallStatus === 'critical' ? 1 : 0);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { BackupMonitor, BackupStatus };
