import { Page, expect } from '@playwright/test';

export class ProductHelpers {
  constructor(private page: Page) {}

  /**
   * Navigate to product selection page
   */
  async goToProductSelection() {
    await this.page.goto('/products');
    await this.page.waitForLoadState('networkidle');
    return this;
  }

  /**
   * Select a product category with emotional engagement
   */
  async selectCategory(category: 'hoodies' | 't-shirts' | 'accessories' | 'jackets') {
    await this.goToProductSelection();
    
    // Look for category cards with emotional messaging
    const categoryCard = this.page.getByTestId(`category-${category}`);
    await expect(categoryCard).toBeVisible();
    
    // Verify emotional copy is present
    const emotionalText = categoryCard.getByText(/express|create|imagine|unique|style/i);
    await expect(emotionalText).toBeVisible();
    
    await categoryCard.click();
    
    // Wait for category page to load
    await this.page.waitForLoadState('networkidle');
    await expect(this.page.getByRole('heading', { name: new RegExp(category, 'i') })).toBeVisible();
    
    return this;
  }

  /**
   * Select a specific product with emotional validation
   */
  async selectProduct(productName?: string) {
    // If no specific product name, select the first available product
    const productCards = this.page.getByTestId(/product-card/);
    await expect(productCards.first()).toBeVisible();
    
    let selectedProduct;
    if (productName) {
      selectedProduct = this.page.getByTestId(`product-card-${productName.toLowerCase().replace(/\s+/g, '-')}`);
    } else {
      selectedProduct = productCards.first();
    }
    
    // Verify product has emotional elements
    const productImage = selectedProduct.getByRole('img');
    const productTitle = selectedProduct.getByRole('heading');
    const emotionalCopy = selectedProduct.getByText(/wear your|express|create|unique/i);
    
    await expect(productImage).toBeVisible();
    await expect(productTitle).toBeVisible();
    await expect(emotionalCopy).toBeVisible();
    
    // Click to select product
    await selectedProduct.click();
    
    // Wait for product detail page
    await this.page.waitForLoadState('networkidle');
    await expect(this.page.getByRole('button', { name: /customize|create|design/i })).toBeVisible();
    
    return this;
  }

  /**
   * Start customization process
   */
  async startCustomization() {
    const customizeButton = this.page.getByRole('button', { name: /customize|create|design/i });
    await expect(customizeButton).toBeVisible();
    await customizeButton.click();
    
    // Wait for customization interface to load
    await this.page.waitForLoadState('networkidle');
    
    // Verify we're in the customization interface
    const canvas = this.page.getByTestId('design-canvas');
    const toolPanel = this.page.getByTestId('design-tools');
    
    await expect(canvas).toBeVisible();
    await expect(toolPanel).toBeVisible();
    
    return this;
  }

  /**
   * Add text to design with emotional messaging
   */
  async addTextToDesign(text: string = 'My Style') {
    // Click text tool
    const textTool = this.page.getByTestId('tool-text');
    await expect(textTool).toBeVisible();
    await textTool.click();
    
    // Click on canvas to add text
    const canvas = this.page.getByTestId('design-canvas');
    await canvas.click({ position: { x: 200, y: 150 } });
    
    // Enter text
    const textInput = this.page.getByTestId('text-input');
    await expect(textInput).toBeVisible();
    await textInput.fill(text);
    await textInput.press('Enter');
    
    // Verify text appears on canvas
    await expect(this.page.getByText(text)).toBeVisible();
    
    return this;
  }

  /**
   * Change product color
   */
  async selectColor(colorName: string = 'black') {
    const colorPicker = this.page.getByTestId('color-picker');
    await expect(colorPicker).toBeVisible();
    
    const colorOption = this.page.getByTestId(`color-${colorName}`);
    await expect(colorOption).toBeVisible();
    await colorOption.click();
    
    // Wait for color change to apply
    await this.page.waitForTimeout(1000);
    
    return this;
  }

  /**
   * Upload custom image/artwork
   */
  async uploadCustomImage(imagePath: string = 'test-assets/sample-design.png') {
    const uploadButton = this.page.getByTestId('upload-image');
    await expect(uploadButton).toBeVisible();
    
    // Set up file chooser
    const fileChooserPromise = this.page.waitForEvent('filechooser');
    await uploadButton.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(imagePath);
    
    // Wait for image to be processed and appear
    await this.page.waitForTimeout(2000);
    
    return this;
  }

  /**
   * Save design with emotional validation
   */
  async saveDesign(designName: string = 'My Creation') {
    const saveButton = this.page.getByRole('button', { name: /save|keep|preserve/i });
    await expect(saveButton).toBeVisible();
    await saveButton.click();
    
    // Fill design name
    const nameInput = this.page.getByTestId('design-name-input');
    if (await nameInput.isVisible()) {
      await nameInput.fill(designName);
    }
    
    // Confirm save
    const confirmButton = this.page.getByRole('button', { name: /save|confirm/i });
    await confirmButton.click();
    
    // Wait for save confirmation
    await expect(this.page.getByText(/saved|created|preserved/i)).toBeVisible();
    
    return this;
  }

  /**
   * Proceed to try-on experience
   */
  async proceedToTryOn() {
    const tryOnButton = this.page.getByRole('button', { name: /try on|see how it looks|preview/i });
    await expect(tryOnButton).toBeVisible();
    await tryOnButton.click();
    
    // Wait for try-on interface
    await this.page.waitForLoadState('networkidle');
    
    return this;
  }

  /**
   * Verify emotional design elements are present
   */
  async verifyEmotionalElements() {
    // Check for inspirational copy
    const inspirationalText = this.page.getByText(/express yourself|make it yours|wear your imagination|unique style/i);
    await expect(inspirationalText).toBeVisible();
    
    // Check for progress indicators that build excitement
    const progressIndicator = this.page.getByTestId('design-progress');
    if (await progressIndicator.isVisible()) {
      const progressText = await progressIndicator.textContent();
      expect(progressText).toMatch(/creating|designing|building|crafting/i);
    }
    
    // Check for social proof elements
    const socialProof = this.page.getByText(/others created|popular choice|trending/i);
    // Social proof is optional, so we don't require it
    
    return this;
  }

  /**
   * Test mobile design experience
   */
  async testMobileDesignExperience() {
    await this.page.setViewportSize({ width: 375, height: 667 });
    
    // Verify touch-friendly design tools
    const tools = this.page.getByTestId(/tool-/);
    const toolCount = await tools.count();
    
    for (let i = 0; i < toolCount; i++) {
      const tool = tools.nth(i);
      const boundingBox = await tool.boundingBox();
      if (boundingBox) {
        expect(boundingBox.height).toBeGreaterThanOrEqual(44);
        expect(boundingBox.width).toBeGreaterThanOrEqual(44);
      }
    }
    
    // Test pinch-to-zoom on canvas (if supported)
    const canvas = this.page.getByTestId('design-canvas');
    await canvas.hover();
    
    // Simulate touch gestures
    await this.page.touchscreen.tap(200, 200);
    
    return this;
  }
}
