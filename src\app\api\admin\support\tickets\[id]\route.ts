/**
 * Admin Individual Support Ticket API Routes
 * Handles admin-specific operations on individual tickets
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { emailService } from '@/lib/services/email';

// Validation schemas
const AdminUpdateTicketSchema = z.object({
  status: z.enum(['OPEN', 'IN_PROGRESS', 'WAITING_FOR_CUSTOMER', 'WAITING_FOR_ADMIN', 'RESOLVED', 'CLOSED', 'CANCELLED']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  assignedToId: z.string().optional(),
  resolution: z.string().max(5000).optional(),
  category: z.string().optional(),
});

/**
 * GET /api/admin/support/tickets/[id] - Get specific ticket (admin view)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const ticket = await prisma.supportTicket.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Mark customer messages as read by admin
    await prisma.supportMessage.updateMany({
      where: {
        ticketId: params.id,
        senderRole: 'CUSTOMER',
        readByAdmin: false,
      },
      data: {
        readByAdmin: true,
        readAt: new Date(),
      },
    });

    // Get related customer information
    const customerStats = await prisma.user.findUnique({
      where: { id: ticket.userId },
      include: {
        _count: {
          select: {
            orders: true,
            supportTickets: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        ...ticket,
        customerStats: customerStats?._count,
      },
    });
  } catch (error) {
    console.error('Error fetching admin support ticket:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch support ticket',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/support/tickets/[id] - Update ticket (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = AdminUpdateTicketSchema.parse(body);

    // Check if ticket exists
    const existingTicket = await prisma.supportTicket.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingTicket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = { ...validatedData };

    if (validatedData.status === 'RESOLVED') {
      updateData.resolvedAt = new Date();
      updateData.resolvedById = session.user.id;
    }

    if (validatedData.assignedToId) {
      updateData.assignedAt = new Date();
    }

    // Update the ticket
    const updatedTicket = await prisma.supportTicket.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    // Create system message for status changes
    if (validatedData.status && validatedData.status !== existingTicket.status) {
      await prisma.supportMessage.create({
        data: {
          content: `Ticket status changed from ${existingTicket.status} to ${validatedData.status}`,
          messageType: 'SYSTEM',
          senderId: session.user.id,
          senderRole: 'ADMIN',
          ticketId: params.id,
          isInternal: true,
          readByCustomer: false,
          readByAdmin: true,
        },
      });
    }

    // Send notification emails based on the update
    try {
      if (validatedData.status === 'RESOLVED') {
        // Ticket resolved - notify customer
        await emailService.sendEmail('support-ticket-resolved', {
          to: existingTicket.user.email || '',
          customerName: existingTicket.user.name || 'Customer',
          ticketId: params.id,
          subject: existingTicket.subject,
          resolution: validatedData.resolution || 'Your issue has been resolved.',
          ticketUrl: `${process.env.APP_URL}/support/tickets/${params.id}`,
        });
      } else if (validatedData.status === 'CLOSED') {
        // Ticket closed - notify customer
        await emailService.sendEmail('support-ticket-closed', {
          to: existingTicket.user.email || '',
          customerName: existingTicket.user.name || 'Customer',
          ticketId: params.id,
          subject: existingTicket.subject,
          ticketUrl: `${process.env.APP_URL}/support/tickets/${params.id}`,
        });
      }
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: updatedTicket,
    });
  } catch (error) {
    console.error('Error updating admin support ticket:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update support ticket',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/support/tickets/[id] - Delete ticket (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check if ticket exists
    const existingTicket = await prisma.supportTicket.findUnique({
      where: { id: params.id },
    });

    if (!existingTicket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Delete the ticket (this will cascade delete messages)
    await prisma.supportTicket.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: 'Ticket deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting support ticket:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete support ticket',
      },
      { status: 500 }
    );
  }
}
