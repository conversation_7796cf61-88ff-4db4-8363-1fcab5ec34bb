/**
 * SupportChatBot Component Tests
 * Tests for the AI-powered support chatbot
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SupportChatBot } from '@/components/support/SupportChatBot';

// Mock useResponsive hook
jest.mock('@/hooks/useResponsive', () => ({
  useResponsive: () => ({
    isMobile: false,
  }),
}));

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
});

describe('SupportChatBot', () => {
  const mockOnClose = jest.fn();
  const mockOnCreateTicket = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onCreateTicket: mockOnCreateTicket,
  };

  it('should render the chatbot when open', () => {
    render(<SupportChatBot {...defaultProps} />);

    expect(screen.getByText('Ottiq AI Assistant')).toBeInTheDocument();
    expect(screen.getByText('Always here to help')).toBeInTheDocument();
    expect(screen.getByText(/Hi! I'm Ottiq's AI assistant/)).toBeInTheDocument();
  });

  it('should not render when closed', () => {
    render(<SupportChatBot {...defaultProps} isOpen={false} />);

    expect(screen.queryByText('Ottiq AI Assistant')).not.toBeInTheDocument();
  });

  it('should display welcome message with options', () => {
    render(<SupportChatBot {...defaultProps} />);

    expect(screen.getByText('📦 Order Status')).toBeInTheDocument();
    expect(screen.getByText('👕 Product Questions')).toBeInTheDocument();
    expect(screen.getByText('🔧 Technical Issues')).toBeInTheDocument();
    expect(screen.getByText('💳 Billing Questions')).toBeInTheDocument();
    expect(screen.getByText('🎫 Create Support Ticket')).toBeInTheDocument();
  });

  it('should handle order status option click', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    await user.click(screen.getByText('📦 Order Status'));

    // Should show user's choice
    expect(screen.getByText('📦 Order Status')).toBeInTheDocument();

    // Advance timers to simulate typing delay
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/I can help you check your order status/)).toBeInTheDocument();
    });

    // Should show new options
    expect(screen.getByText('📱 Check Order Dashboard')).toBeInTheDocument();
    expect(screen.getByText('🔍 Search by Order Number')).toBeInTheDocument();
  });

  it('should handle technical help option', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    await user.click(screen.getByText('🔧 Technical Issues'));

    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/Let me help you with technical issues/)).toBeInTheDocument();
    });

    expect(screen.getByText('🖥️ Website Not Loading')).toBeInTheDocument();
    expect(screen.getByText('🎨 Design Editor Problems')).toBeInTheDocument();
    expect(screen.getByText('📱 Mobile App Issues')).toBeInTheDocument();
    expect(screen.getByText('🔐 Login Problems')).toBeInTheDocument();
  });

  it('should provide website troubleshooting steps', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    // Navigate to technical issues
    await user.click(screen.getByText('🔧 Technical Issues'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('🖥️ Website Not Loading')).toBeInTheDocument();
    });

    // Click on website issue
    await user.click(screen.getByText('🖥️ Website Not Loading'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/Clear your browser cache and cookies/)).toBeInTheDocument();
    });

    expect(screen.getByText('✅ This Helped')).toBeInTheDocument();
    expect(screen.getByText('🎫 Still Need Help')).toBeInTheDocument();
  });

  it('should handle create ticket flow', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    await user.click(screen.getByText('🎫 Create Support Ticket'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/I'll help you create a support ticket/)).toBeInTheDocument();
    });

    expect(screen.getByText('🎫 Create Ticket Now')).toBeInTheDocument();
    expect(screen.getByText('🔙 Go Back')).toBeInTheDocument();

    // Confirm ticket creation
    await user.click(screen.getByText('🎫 Create Ticket Now'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/Perfect! I'm opening the support ticket form/)).toBeInTheDocument();
    });

    // Should call onCreateTicket after delay
    jest.advanceTimersByTime(1000);
    expect(mockOnCreateTicket).toHaveBeenCalled();
  });

  it('should handle text input and provide appropriate responses', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    const input = screen.getByPlaceholderText('Type your question...');
    const sendButton = screen.getByText('Send');

    // Type a message about orders
    await user.type(input, 'Where is my order?');
    await user.click(sendButton);

    // Should show user message
    expect(screen.getByText('Where is my order?')).toBeInTheDocument();

    jest.advanceTimersByTime(1000);

    // Should trigger order status flow
    await waitFor(() => {
      expect(screen.getByText(/I can help you check your order status/)).toBeInTheDocument();
    });
  });

  it('should handle login-related queries', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    const input = screen.getByPlaceholderText('Type your question...');
    await user.type(input, 'I cannot login to my account');
    await user.click(screen.getByText('Send'));

    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/For login problems, try these steps/)).toBeInTheDocument();
    });

    expect(screen.getByText('🔐 Reset Password')).toBeInTheDocument();
    expect(screen.getByText('✅ This Helped')).toBeInTheDocument();
  });

  it('should open external links', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    // Navigate to order status and click dashboard
    await user.click(screen.getByText('📦 Order Status'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('📱 Check Order Dashboard')).toBeInTheDocument();
    });

    await user.click(screen.getByText('📱 Check Order Dashboard'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/You can view all your orders in your dashboard/)).toBeInTheDocument();
    });

    // Should open dashboard in new tab
    jest.advanceTimersByTime(500);
    expect(window.open).toHaveBeenCalledWith('/dashboard/orders', '_blank');
  });

  it('should handle restart conversation', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    // Navigate through some options
    await user.click(screen.getByText('📦 Order Status'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/I can help you check your order status/)).toBeInTheDocument();
    });

    // Go back to main menu
    await user.click(screen.getByText('🎫 Contact Support'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('🔙 Go Back')).toBeInTheDocument();
    });

    await user.click(screen.getByText('🔙 Go Back'));

    // Should restart conversation (clear messages)
    // The welcome message should appear again
    await waitFor(() => {
      expect(screen.getByText(/Hi! I'm Ottiq's AI assistant/)).toBeInTheDocument();
    });
  });

  it('should handle end chat', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<SupportChatBot {...defaultProps} />);

    // Navigate to a resolved state
    await user.click(screen.getByText('🔧 Technical Issues'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('🖥️ Website Not Loading')).toBeInTheDocument();
    });

    await user.click(screen.getByText('🖥️ Website Not Loading'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('✅ This Helped')).toBeInTheDocument();
    });

    await user.click(screen.getByText('✅ This Helped'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText('👋 End Chat')).toBeInTheDocument();
    });

    await user.click(screen.getByText('👋 End Chat'));
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(screen.getByText(/Thanks for chatting with me/)).toBeInTheDocument();
    });

    // Should close chat after delay
    jest.advanceTimersByTime(2000);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should call onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    
    render(<SupportChatBot {...defaultProps} />);

    await user.click(screen.getByText('✕'));

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should disable input during typing animation', () => {
    render(<SupportChatBot {...defaultProps} />);

    const input = screen.getByPlaceholderText('Type your question...');
    const sendButton = screen.getByText('Send');

    // Initially should be enabled
    expect(input).not.toBeDisabled();
    expect(sendButton).not.toBeDisabled();

    // After clicking an option, should show typing indicator
    fireEvent.click(screen.getByText('📦 Order Status'));

    // During typing, input should be disabled
    expect(input).toBeDisabled();
    expect(sendButton).toBeDisabled();
  });
});
