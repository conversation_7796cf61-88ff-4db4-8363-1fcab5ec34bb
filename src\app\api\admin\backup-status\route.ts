/**
 * Backup Status API Endpoint
 * Provides backup system status and health information for admin dashboard
 */

import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

// Types
interface BackupStatusResponse {
  timestamp: string;
  environment: string;
  database: {
    lastBackup: string | null;
    ageHours: number;
    size: string;
    status: 'healthy' | 'warning' | 'critical';
  };
  minio: {
    lastBackup: string | null;
    ageHours: number;
    size: string;
    status: 'healthy' | 'warning' | 'critical';
  };
  storage: {
    usagePercent: number;
    totalSize: string;
    availableSpace: string;
    status: 'healthy' | 'warning' | 'critical';
  };
  services: {
    postgres: boolean;
    minio: boolean;
    status: 'healthy' | 'critical';
  };
  overallStatus: 'healthy' | 'warning' | 'critical';
  alerts: string[];
  lastCheck: string;
}

/**
 * Check if user has admin privileges
 */
async function checkAdminAccess(request: NextRequest): Promise<boolean> {
  // In a real implementation, you would check the user's session/token
  // For now, we'll check for a simple admin header or environment variable
  const adminKey = request.headers.get('x-admin-key');
  const expectedKey = process.env.ADMIN_API_KEY;
  
  if (!expectedKey) {
    // If no admin key is set, allow access in development
    return process.env.NODE_ENV === 'development';
  }
  
  return adminKey === expectedKey;
}

/**
 * Get backup status from monitoring system
 */
async function getBackupStatus(): Promise<BackupStatusResponse> {
  const projectRoot = path.resolve(process.cwd());
  const backupRoot = path.join(projectRoot, 'backups');
  const statusFile = path.join(backupRoot, 'status.json');
  
  try {
    // Try to read existing status file first
    if (await fs.access(statusFile).then(() => true).catch(() => false)) {
      const statusContent = await fs.readFile(statusFile, 'utf-8');
      const status = JSON.parse(statusContent) as BackupStatusResponse;
      
      // Check if status is recent (less than 1 hour old)
      const statusAge = Date.now() - new Date(status.timestamp).getTime();
      if (statusAge < 60 * 60 * 1000) { // 1 hour
        return {
          ...status,
          lastCheck: status.timestamp,
        };
      }
    }
    
    // If no recent status file, generate new status
    return await generateFreshStatus();
    
  } catch (error) {
    console.error('Error reading backup status:', error);
    return await generateFreshStatus();
  }
}

/**
 * Generate fresh backup status
 */
async function generateFreshStatus(): Promise<BackupStatusResponse> {
  const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
  const projectRoot = path.resolve(process.cwd());
  const backupRoot = path.join(projectRoot, 'backups');
  
  try {
    // Run the TypeScript monitoring script
    const monitorScript = path.join(projectRoot, 'scripts', 'backup-monitor.ts');
    const { stdout } = await execAsync(`npx ts-node "${monitorScript}" ${environment}`);
    
    const status = JSON.parse(stdout) as BackupStatusResponse;
    return {
      ...status,
      lastCheck: new Date().toISOString(),
    };
    
  } catch (error) {
    console.error('Error generating backup status:', error);
    
    // Return a fallback status
    return {
      timestamp: new Date().toISOString(),
      environment,
      database: {
        lastBackup: null,
        ageHours: Infinity,
        size: 'unknown',
        status: 'critical',
      },
      minio: {
        lastBackup: null,
        ageHours: Infinity,
        size: 'unknown',
        status: 'critical',
      },
      storage: {
        usagePercent: 0,
        totalSize: 'unknown',
        availableSpace: 'unknown',
        status: 'critical',
      },
      services: {
        postgres: false,
        minio: false,
        status: 'critical',
      },
      overallStatus: 'critical',
      alerts: ['Unable to check backup status - monitoring system error'],
      lastCheck: new Date().toISOString(),
    };
  }
}

/**
 * GET /api/admin/backup-status
 * Returns current backup system status
 */
export async function GET(request: NextRequest) {
  try {
    // Check admin access
    if (!(await checkAdminAccess(request))) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }
    
    // Get backup status
    const status = await getBackupStatus();
    
    // Return status with appropriate HTTP status code
    const httpStatus = status.overallStatus === 'critical' ? 503 : 200;
    
    return NextResponse.json(status, { status: httpStatus });
    
  } catch (error) {
    console.error('Backup status API error:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/backup-status
 * Triggers a fresh backup status check
 */
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    if (!(await checkAdminAccess(request))) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }
    
    // Force generate fresh status
    const status = await generateFreshStatus();
    
    // Save the fresh status
    const projectRoot = path.resolve(process.cwd());
    const backupRoot = path.join(projectRoot, 'backups');
    const statusFile = path.join(backupRoot, 'status.json');
    
    try {
      await fs.mkdir(backupRoot, { recursive: true });
      await fs.writeFile(statusFile, JSON.stringify(status, null, 2));
    } catch (saveError) {
      console.error('Error saving backup status:', saveError);
    }
    
    // Return fresh status
    const httpStatus = status.overallStatus === 'critical' ? 503 : 200;
    
    return NextResponse.json({
      ...status,
      refreshed: true,
    }, { status: httpStatus });
    
  } catch (error) {
    console.error('Backup status refresh API error:', error);
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
