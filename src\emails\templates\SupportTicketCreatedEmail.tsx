/**
 * Support Ticket Created Email Template (Admin Notification)
 * Notifies admins when a new support ticket is created
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { SupportTicketCreatedEmailData } from '../../lib/services/email';

interface SupportTicketCreatedEmailProps extends SupportTicketCreatedEmailData {}

export function SupportTicketCreatedEmail({
  customerName,
  ticketId,
  subject,
  description,
  priority,
  category,
  ticketUrl,
}: SupportTicketCreatedEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  const priorityColors = {
    LOW: '#10B981',
    MEDIUM: '#F59E0B',
    HIGH: '#EF4444',
    URGENT: '#DC2626',
  };

  const priorityColor = priorityColors[priority as keyof typeof priorityColors] || '#6B7280';

  return (
    <EmailLayout previewText={`New support ticket from ${customerName}: ${subject}`}>
      {/* Header */}
      <Section className="text-center mb-6">
        <EmailHeading level={1}>
          🎫 New Support Ticket
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          A customer needs your help! Please review and respond promptly.
        </EmailText>
      </Section>

      {/* Ticket Details */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-6">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Text className="text-sm font-medium text-gray-600 mb-1">Ticket ID</Text>
            <Text className="text-lg font-mono text-gray-900">#{ticketId.slice(-8)}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-600 mb-1">Priority</Text>
            <Text 
              className="inline-block px-3 py-1 rounded-full text-sm font-medium text-white"
              style={{ backgroundColor: priorityColor }}
            >
              {priority}
            </Text>
          </div>
        </div>

        <div className="mb-4">
          <Text className="text-sm font-medium text-gray-600 mb-1">Customer</Text>
          <Text className="text-lg text-gray-900">{customerName}</Text>
        </div>

        <div className="mb-4">
          <Text className="text-sm font-medium text-gray-600 mb-1">Category</Text>
          <Text className="text-base text-gray-900 capitalize">{category}</Text>
        </div>

        <div className="mb-4">
          <Text className="text-sm font-medium text-gray-600 mb-1">Subject</Text>
          <Text className="text-lg font-medium text-gray-900">{subject}</Text>
        </div>

        <div>
          <Text className="text-sm font-medium text-gray-600 mb-2">Description</Text>
          <Text className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap">
            {description}
          </Text>
        </div>
      </Section>

      {/* Action Button */}
      <Section className="text-center mb-6">
        <EmailButton href={ticketUrl}>
          View & Respond to Ticket
        </EmailButton>
      </Section>

      {/* Quick Response Tips */}
      <Section className="bg-blue-50 rounded-lg p-4 mb-6">
        <EmailHeading level={3} className="text-blue-900 mb-3">
          💡 Quick Response Tips
        </EmailHeading>
        
        <div className="space-y-2">
          <EmailText className="text-blue-800 text-sm">
            • Acknowledge receipt within 1 hour during business hours
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            • Ask clarifying questions if needed
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            • Provide clear, actionable solutions
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            • Follow up to ensure customer satisfaction
          </EmailText>
        </div>
      </Section>

      <Hr className="my-6" />

      {/* Footer */}
      <Section className="text-center">
        <EmailText className="text-gray-500 text-sm">
          This is an automated notification from the Ottiq support system.
          <br />
          Please respond promptly to maintain our high customer satisfaction standards.
        </EmailText>
      </Section>
    </EmailLayout>
  );
}
