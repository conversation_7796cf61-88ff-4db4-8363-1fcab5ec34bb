#!/usr/bin/env node

/**
 * Email Template Validation Script
 * Validates all email templates can be rendered without errors
 */

import { render } from '@react-email/render';
import { promises as fs } from 'fs';
import path from 'path';

// Test data for different email types
const testData = {
  'order-confirmation': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    orderNumber: 'TEST-123456',
    orderDate: new Date().toLocaleDateString(),
    totalAmount: '2500.00',
    currency: 'BDT',
    items: [
      {
        name: 'Custom T-Shirt',
        quantity: 1,
        price: '1500.00',
        customization: {
          name: 'Bold Streetwear Design',
          previewImage: 'https://example.com/design.jpg',
        },
      },
      {
        name: 'Custom Hoodie',
        quantity: 1,
        price: '1000.00',
      },
    ],
    shippingAddress: {
      name: 'Test Customer',
      address: '123 Test Street, Apartment 4B',
      city: 'Dhaka',
      postalCode: '1000',
      country: 'Bangladesh',
    },
    estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    orderUrl: 'https://ottiq.com/orders/test-123',
  },
  'order-shipped': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    orderNumber: 'TEST-123456',
    orderDate: new Date().toLocaleDateString(),
    totalAmount: '2500.00',
    currency: 'BDT',
    items: [
      {
        name: 'Custom T-Shirt',
        quantity: 1,
        price: '1500.00',
        customization: {
          name: 'Bold Streetwear Design',
          previewImage: 'https://example.com/design.jpg',
        },
      },
    ],
    shippingAddress: {
      name: 'Test Customer',
      address: '123 Test Street, Apartment 4B',
      city: 'Dhaka',
      postalCode: '1000',
      country: 'Bangladesh',
    },
    trackingNumber: 'TRK123456789',
    estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    orderUrl: 'https://ottiq.com/orders/test-123',
  },
  'order-delivered': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    orderNumber: 'TEST-123456',
    orderDate: new Date().toLocaleDateString(),
    totalAmount: '2500.00',
    currency: 'BDT',
    items: [
      {
        name: 'Custom T-Shirt',
        quantity: 1,
        price: '1500.00',
        customization: {
          name: 'Bold Streetwear Design',
          previewImage: 'https://example.com/design.jpg',
        },
      },
    ],
    shippingAddress: {
      name: 'Test Customer',
      address: '123 Test Street, Apartment 4B',
      city: 'Dhaka',
      postalCode: '1000',
      country: 'Bangladesh',
    },
    orderUrl: 'https://ottiq.com/orders/test-123',
  },
  'order-cancelled': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    orderNumber: 'TEST-123456',
    orderDate: new Date().toLocaleDateString(),
    totalAmount: '2500.00',
    currency: 'BDT',
    items: [
      {
        name: 'Custom T-Shirt',
        quantity: 1,
        price: '1500.00',
      },
    ],
    shippingAddress: {
      name: 'Test Customer',
      address: '123 Test Street, Apartment 4B',
      city: 'Dhaka',
      postalCode: '1000',
      country: 'Bangladesh',
    },
    orderUrl: 'https://ottiq.com/orders/test-123',
    cancellationReason: 'Customer requested cancellation',
    refundAmount: '2500.00',
    refundMethod: 'Original payment method',
    refundTimeframe: '5-7 business days',
  },
  'payment-confirmation': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    orderNumber: 'TEST-123456',
    amount: '2500.00',
    currency: 'BDT',
    paymentMethod: 'bkash',
    transactionId: 'TXN123456789',
    orderUrl: 'https://ottiq.com/orders/test-123',
  },
  'payment-failed': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    orderNumber: 'TEST-123456',
    amount: '2500.00',
    currency: 'BDT',
    paymentMethod: 'bkash',
    orderUrl: 'https://ottiq.com/orders/test-123',
    failureReason: 'Insufficient funds',
    retryUrl: 'https://ottiq.com/orders/test-123?retry=payment',
  },
  'ai-tryon-ready': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    productName: 'Custom T-Shirt',
    tryOnImageUrl: 'https://example.com/tryon-result.jpg',
    customizationName: 'Bold Streetwear Design',
    orderUrl: 'https://ottiq.com/create?design=test-design',
  },
  'welcome': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
  },
  'password-reset': {
    to: '<EMAIL>',
    customerName: 'Test Customer',
    resetUrl: 'https://ottiq.com/reset-password?token=test-token-123',
    expiresIn: '1 hour',
  },
};

interface ValidationResult {
  template: string;
  success: boolean;
  error?: string;
  htmlLength?: number;
}

async function validateTemplate(templateName: string, data: any): Promise<ValidationResult> {
  try {
    console.log(`Validating ${templateName} template...`);
    
    // Dynamically import the template
    const templatePath = `../src/emails/templates/${getTemplateFileName(templateName)}`;
    const templateModule = await import(templatePath);
    const TemplateComponent = templateModule[getTemplateComponentName(templateName)];
    
    if (!TemplateComponent) {
      return {
        template: templateName,
        success: false,
        error: `Template component not found: ${getTemplateComponentName(templateName)}`,
      };
    }
    
    // Render the template
    const html = render(TemplateComponent(data));
    
    // Basic validation checks
    if (!html || html.length < 100) {
      return {
        template: templateName,
        success: false,
        error: 'Rendered HTML is too short or empty',
        htmlLength: html.length,
      };
    }
    
    // Check for basic HTML structure
    if (!html.includes('<html>') || !html.includes('<body>')) {
      return {
        template: templateName,
        success: false,
        error: 'Rendered HTML missing basic structure',
        htmlLength: html.length,
      };
    }
    
    // Check for customer name in the content
    if (!html.includes(data.customerName)) {
      return {
        template: templateName,
        success: false,
        error: 'Customer name not found in rendered HTML',
        htmlLength: html.length,
      };
    }
    
    return {
      template: templateName,
      success: true,
      htmlLength: html.length,
    };
    
  } catch (error) {
    return {
      template: templateName,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

function getTemplateFileName(templateName: string): string {
  const nameMap: Record<string, string> = {
    'order-confirmation': 'OrderConfirmationEmail',
    'order-shipped': 'OrderShippedEmail',
    'order-delivered': 'OrderDeliveredEmail',
    'order-cancelled': 'OrderCancelledEmail',
    'payment-confirmation': 'PaymentConfirmationEmail',
    'payment-failed': 'PaymentFailedEmail',
    'ai-tryon-ready': 'AiTryOnReadyEmail',
    'welcome': 'WelcomeEmail',
    'password-reset': 'PasswordResetEmail',
  };
  
  return nameMap[templateName] || templateName;
}

function getTemplateComponentName(templateName: string): string {
  return getTemplateFileName(templateName);
}

async function validateAllTemplates(): Promise<void> {
  console.log('🧪 Starting email template validation...\n');
  
  const results: ValidationResult[] = [];
  
  for (const [templateName, data] of Object.entries(testData)) {
    const result = await validateTemplate(templateName, data);
    results.push(result);
  }
  
  // Print results
  console.log('\n📊 Validation Results:');
  console.log('='.repeat(60));
  
  let successCount = 0;
  let failureCount = 0;
  
  for (const result of results) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const size = result.htmlLength ? `(${result.htmlLength} chars)` : '';
    
    console.log(`${status} ${result.template} ${size}`);
    
    if (!result.success && result.error) {
      console.log(`    Error: ${result.error}`);
    }
    
    if (result.success) {
      successCount++;
    } else {
      failureCount++;
    }
  }
  
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${successCount}`);
  console.log(`❌ Failed: ${failureCount}`);
  console.log(`📊 Total: ${results.length}`);
  
  if (failureCount > 0) {
    console.log('\n❌ Some templates failed validation. Please fix the issues above.');
    process.exit(1);
  } else {
    console.log('\n🎉 All email templates validated successfully!');
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateAllTemplates().catch((error) => {
    console.error('❌ Validation script failed:', error);
    process.exit(1);
  });
}

export { validateAllTemplates, validateTemplate };
