/**
 * Support Ticket Closed Email Template (Customer Notification)
 * Notifies customers when their support ticket has been closed
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { SupportTicketClosedEmailData } from '../../lib/services/email';

interface SupportTicketClosedEmailProps extends SupportTicketClosedEmailData {}

export function SupportTicketClosedEmail({
  customerName,
  ticketId,
  subject,
  ticketUrl,
}: SupportTicketClosedEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`Your support ticket has been closed: ${subject}`}>
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-support-closed.jpg`}
          alt="Support ticket closed"
          width="600"
          height="200"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          📋 Ticket Closed, {customerName}
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Your support ticket has been closed. We hope we were able to help you successfully!
        </EmailText>
      </Section>

      {/* Ticket Info */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-6">
        <div className="text-center mb-4">
          <Text className="text-sm font-medium text-gray-600">Closed Ticket</Text>
          <Text className="text-lg font-mono text-gray-900">#{ticketId.slice(-8)}</Text>
        </div>

        <div className="text-center">
          <Text className="text-sm font-medium text-gray-600 mb-1">Subject</Text>
          <Text className="text-lg font-medium text-gray-900">{subject}</Text>
        </div>
      </Section>

      {/* View History */}
      <Section className="text-center mb-8">
        <EmailButton href={ticketUrl} className="mb-4">
          View Ticket History
        </EmailButton>
        
        <EmailText className="text-gray-600 text-sm">
          You can still access the full conversation history anytime
        </EmailText>
      </Section>

      {/* Feedback Request */}
      <Section className="bg-gradient-to-r from-warm-500 to-amber-500 text-white rounded-lg p-6 mb-6">
        <EmailHeading level={3} className="text-white mb-3">
          💭 We'd Love Your Feedback
        </EmailHeading>
        
        <EmailText className="text-white/90 mb-4">
          Help us improve our support experience! Your feedback is invaluable in helping us serve you and other customers better.
        </EmailText>

        <div className="text-center">
          <EmailButton 
            href={`${ticketUrl}?feedback=true`}
            className="bg-white text-warm-600 hover:bg-gray-100"
          >
            Share Your Experience
          </EmailButton>
        </div>
      </Section>

      {/* Need More Help */}
      <Section className="bg-blue-50 rounded-lg p-4 mb-6">
        <EmailHeading level={4} className="text-blue-900 mb-3">
          Need More Help?
        </EmailHeading>
        
        <EmailText className="text-blue-800 mb-3">
          If you have any follow-up questions or need additional assistance, we're always here to help!
        </EmailText>

        <div className="space-y-2">
          <EmailText className="text-blue-800 text-sm">
            🎫 <strong>New ticket:</strong> <a href={`${baseUrl}/support`} className="text-blue-600 underline">Create a new support ticket</a>
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            📚 <strong>Self-help:</strong> Browse our <a href={`${baseUrl}/help`} className="text-blue-600 underline">Help Center</a>
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            💬 <strong>Quick chat:</strong> Use our live chat during business hours
          </EmailText>
        </div>
      </Section>

      {/* Thank You */}
      <Section className="text-center mb-6">
        <EmailHeading level={3} className="text-gray-900 mb-3">
          🌟 Thank You for Choosing Ottiq
        </EmailHeading>
        
        <EmailText className="text-gray-700">
          We appreciate your patience and trust in our support team. Your satisfaction is our priority, and we're always working to improve your Ottiq experience.
        </EmailText>
      </Section>

      {/* Quick Links */}
      <Section className="bg-gray-50 rounded-lg p-4 mb-6">
        <EmailHeading level={4} className="text-gray-900 mb-3">
          🚀 Continue Your Ottiq Journey
        </EmailHeading>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <EmailText className="text-gray-700 text-sm">
              <a href={`${baseUrl}/create`} className="text-warm-600 underline font-medium">
                ✨ Create New Design
              </a>
            </EmailText>
          </div>
          <div>
            <EmailText className="text-gray-700 text-sm">
              <a href={`${baseUrl}/templates`} className="text-warm-600 underline font-medium">
                🎨 Browse Templates
              </a>
            </EmailText>
          </div>
          <div>
            <EmailText className="text-gray-700 text-sm">
              <a href={`${baseUrl}/dashboard`} className="text-warm-600 underline font-medium">
                📊 View Dashboard
              </a>
            </EmailText>
          </div>
          <div>
            <EmailText className="text-gray-700 text-sm">
              <a href={`${baseUrl}/orders`} className="text-warm-600 underline font-medium">
                📦 Track Orders
              </a>
            </EmailText>
          </div>
        </div>
      </Section>

      <Hr className="my-6" />

      {/* Footer */}
      <Section className="text-center">
        <EmailText className="text-gray-500 text-sm">
          This email was sent regarding your support ticket #{ticketId.slice(-8)}.
          <br />
          If you need to reopen this ticket, please contact our support team.
        </EmailText>
      </Section>
    </EmailLayout>
  );
}
