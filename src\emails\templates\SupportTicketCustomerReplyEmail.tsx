/**
 * Support Ticket Customer Reply Email Template (Admin Notification)
 * Notifies admins when a customer replies to their support ticket
 */

import { Section, Text, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { SupportTicketCustomerReplyEmailData } from '../../lib/services/email';

interface SupportTicketCustomerReplyEmailProps extends SupportTicketCustomerReplyEmailData {}

export function SupportTicketCustomerReplyEmail({
  customerName,
  ticketId,
  subject,
  message,
  ticketUrl,
}: SupportTicketCustomerReplyEmailProps) {
  return (
    <EmailLayout previewText={`${customerName} replied to ticket: ${subject}`}>
      {/* Header */}
      <Section className="text-center mb-6">
        <EmailHeading level={1}>
          💬 Customer Reply
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          {customerName} has responded to their support ticket. Please review and respond promptly.
        </EmailText>
      </Section>

      {/* Ticket Details */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-6">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <Text className="text-sm font-medium text-gray-600 mb-1">Ticket ID</Text>
            <Text className="text-lg font-mono text-gray-900">#{ticketId.slice(-8)}</Text>
          </div>
          <div>
            <Text className="text-sm font-medium text-gray-600 mb-1">Customer</Text>
            <Text className="text-lg text-gray-900">{customerName}</Text>
          </div>
        </div>

        <div className="mb-4">
          <Text className="text-sm font-medium text-gray-600 mb-1">Subject</Text>
          <Text className="text-lg font-medium text-gray-900">{subject}</Text>
        </div>
      </Section>

      {/* Customer Message */}
      <Section className="bg-white border-l-4 border-blue-500 pl-6 py-4 mb-6">
        <EmailHeading level={3} className="text-gray-900 mb-3">
          Customer's Latest Message
        </EmailHeading>
        
        <EmailText className="text-gray-700 leading-relaxed whitespace-pre-wrap">
          {message}
        </EmailText>
      </Section>

      {/* Action Button */}
      <Section className="text-center mb-6">
        <EmailButton href={ticketUrl}>
          View & Respond to Ticket
        </EmailButton>
      </Section>

      {/* Response Guidelines */}
      <Section className="bg-blue-50 rounded-lg p-4 mb-6">
        <EmailHeading level={3} className="text-blue-900 mb-3">
          📋 Response Guidelines
        </EmailHeading>
        
        <div className="space-y-2">
          <EmailText className="text-blue-800 text-sm">
            • Respond within 2 hours during business hours
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            • Address all points raised by the customer
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            • Provide clear next steps if applicable
          </EmailText>
          <EmailText className="text-blue-800 text-sm">
            • Maintain our friendly, helpful tone
          </EmailText>
        </div>
      </Section>

      <Hr className="my-6" />

      {/* Footer */}
      <Section className="text-center">
        <EmailText className="text-gray-500 text-sm">
          This is an automated notification from the Ottiq support system.
          <br />
          Customer is waiting for your response - please prioritize accordingly.
        </EmailText>
      </Section>
    </EmailLayout>
  );
}
