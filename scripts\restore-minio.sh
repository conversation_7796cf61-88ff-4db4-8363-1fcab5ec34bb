#!/bin/bash

# Ottiq MinIO Restore Script
# Restores MinIO object storage from backup files
# Usage: ./restore-minio.sh <backup_file> [environment]
# Environment: dev, prod (default: prod)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_FILE="${1:-}"
ENVIRONMENT="${2:-prod}"

# Validate arguments
if [[ -z "$BACKUP_FILE" ]]; then
    echo "Usage: $0 <backup_file> [environment]"
    echo "Example: $0 ottiq_minio_prod_20241211_120000.tar.gz prod"
    exit 1
fi

# Load environment variables
if [[ "$ENVIRONMENT" == "prod" ]]; then
    ENV_FILE="$PROJECT_ROOT/.env.docker"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"
else
    ENV_FILE="$PROJECT_ROOT/.env"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"
fi

# Source environment variables
if [[ -f "$ENV_FILE" ]]; then
    set -a
    source "$ENV_FILE"
    set +a
else
    echo "Error: Environment file $ENV_FILE not found"
    exit 1
fi

# Restore configuration
BACKUP_DIR="$PROJECT_ROOT/backups/minio"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$BACKUP_DIR/restore_${TIMESTAMP}.log"
TEMP_DIR="/tmp/ottiq_minio_restore_$$"

# MinIO settings
MINIO_CONTAINER="ottiq-minio"
MINIO_DATA_PATH="/data"
MINIO_VOLUME="ottiq_minio_data"

# Create temp directory
mkdir -p "$TEMP_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
cleanup() {
    local exit_code=$?
    
    # Clean up temporary files
    rm -rf "$TEMP_DIR" 2>/dev/null || true
    
    if [[ $exit_code -ne 0 ]]; then
        log "ERROR: MinIO restore failed with exit code $exit_code"
        send_alert "MinIO restore failed for $ENVIRONMENT environment"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Function to send alerts
send_alert() {
    local message="$1"
    log "ALERT: $message"
    echo "RESTORE ALERT: $message" >> "$BACKUP_DIR/alerts.log"
}

# Function to check if MinIO container is running
check_container() {
    if ! docker ps --format "table {{.Names}}" | grep -q "^${MINIO_CONTAINER}$"; then
        log "ERROR: MinIO container '$MINIO_CONTAINER' is not running"
        return 1
    fi
    return 0
}

# Function to test MinIO connection
test_connection() {
    log "Testing MinIO connection..."
    
    # Try to access MinIO health endpoint
    if docker exec "$MINIO_CONTAINER" curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1; then
        log "MinIO connection successful"
        return 0
    else
        log "ERROR: Cannot connect to MinIO"
        return 1
    fi
}

# Function to validate backup file
validate_backup_file() {
    local backup_path="$1"
    
    log "Validating backup file: $backup_path"
    
    # Check if file exists
    if [[ ! -f "$backup_path" ]]; then
        log "ERROR: Backup file not found: $backup_path"
        return 1
    fi
    
    # Check if file is not empty
    if [[ ! -s "$backup_path" ]]; then
        log "ERROR: Backup file is empty: $backup_path"
        return 1
    fi
    
    # Verify checksum if available
    local checksum_file="${backup_path}.sha256"
    if [[ -f "$checksum_file" ]]; then
        log "Verifying backup file checksum..."
        if cd "$(dirname "$backup_path")" && sha256sum -c "$(basename "$checksum_file")" > /dev/null 2>&1; then
            log "Backup file checksum verification passed"
        else
            log "ERROR: Backup file checksum verification failed"
            return 1
        fi
    else
        log "WARNING: No checksum file found, skipping verification"
    fi
    
    # Test archive integrity
    log "Testing backup archive integrity..."
    if tar -tzf "$backup_path" > /dev/null 2>&1; then
        log "Backup archive integrity check passed"
    else
        log "ERROR: Backup archive is corrupted"
        return 1
    fi
    
    return 0
}

# Function to create pre-restore backup
create_pre_restore_backup() {
    log "Creating pre-restore backup..."
    
    local pre_restore_backup="ottiq_minio_${ENVIRONMENT}_pre_restore_${TIMESTAMP}.tar.gz"
    local pre_restore_path="$BACKUP_DIR/$pre_restore_backup"
    
    # Check if MinIO volume exists and has data
    if docker volume inspect "$MINIO_VOLUME" > /dev/null 2>&1; then
        # Create backup of current MinIO data
        if docker run --rm \
            -v "$MINIO_VOLUME":/source:ro \
            -v "$BACKUP_DIR":/backup \
            alpine:latest \
            tar czf "/backup/$pre_restore_backup" -C /source . 2>> "$LOG_FILE"; then
            
            log "Pre-restore backup created: $pre_restore_backup"
            return 0
        else
            log "ERROR: Failed to create pre-restore backup"
            return 1
        fi
    else
        log "WARNING: MinIO volume not found, skipping pre-restore backup"
        return 0
    fi
}

# Function to stop MinIO container
stop_minio_container() {
    log "Stopping MinIO container for restore..."
    
    if docker stop "$MINIO_CONTAINER" 2>/dev/null; then
        log "MinIO container stopped successfully"
        
        # Wait for container to fully stop
        local timeout=30
        local count=0
        while docker ps --format "table {{.Names}}" | grep -q "^${MINIO_CONTAINER}$" && [[ $count -lt $timeout ]]; do
            sleep 1
            ((count++))
        done
        
        if [[ $count -ge $timeout ]]; then
            log "WARNING: MinIO container did not stop within timeout"
        fi
        
        return 0
    else
        log "ERROR: Failed to stop MinIO container"
        return 1
    fi
}

# Function to start MinIO container
start_minio_container() {
    log "Starting MinIO container..."
    
    if docker start "$MINIO_CONTAINER" 2>/dev/null; then
        log "MinIO container started successfully"
        
        # Wait for container to be ready
        local timeout=60
        local count=0
        while ! docker exec "$MINIO_CONTAINER" curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1 && [[ $count -lt $timeout ]]; do
            sleep 2
            ((count+=2))
        done
        
        if [[ $count -ge $timeout ]]; then
            log "WARNING: MinIO container did not become ready within timeout"
            return 1
        fi
        
        log "MinIO container is ready"
        return 0
    else
        log "ERROR: Failed to start MinIO container"
        return 1
    fi
}

# Function to restore MinIO data
restore_minio_data() {
    local backup_path="$1"
    
    log "Starting MinIO data restore from: $backup_path"
    
    # Stop MinIO container
    stop_minio_container || return 1
    
    # Clear existing MinIO volume data
    log "Clearing existing MinIO volume data..."
    if docker run --rm \
        -v "$MINIO_VOLUME":/target \
        alpine:latest \
        sh -c "rm -rf /target/* /target/.*" 2>/dev/null; then
        
        log "Existing MinIO data cleared"
    else
        log "WARNING: Failed to clear existing MinIO data"
    fi
    
    # Restore data from backup
    log "Restoring MinIO data from backup..."
    if docker run --rm \
        -v "$MINIO_VOLUME":/target \
        -v "$(dirname "$backup_path")":/backup:ro \
        alpine:latest \
        tar xzf "/backup/$(basename "$backup_path")" -C /target 2>> "$LOG_FILE"; then
        
        log "MinIO data restored successfully"
    else
        log "ERROR: Failed to restore MinIO data"
        return 1
    fi
    
    # Start MinIO container
    start_minio_container || return 1
    
    return 0
}

# Function to verify restore
verify_restore() {
    log "Verifying MinIO restore..."
    
    # Test MinIO connection
    if ! test_connection; then
        log "ERROR: Cannot connect to restored MinIO"
        return 1
    fi
    
    # Check if data was restored
    local storage_usage=$(docker exec "$MINIO_CONTAINER" du -sh "$MINIO_DATA_PATH" 2>/dev/null | cut -f1 || echo "0")
    log "Restored MinIO storage usage: $storage_usage"
    
    # List restored buckets
    log "Restored MinIO contents:"
    docker exec "$MINIO_CONTAINER" find "$MINIO_DATA_PATH" -type d -name "*" 2>/dev/null | while read -r bucket_path; do
        if [[ "$bucket_path" != "$MINIO_DATA_PATH" ]]; then
            local bucket_name=$(basename "$bucket_path")
            local object_count=$(docker exec "$MINIO_CONTAINER" find "$bucket_path" -type f 2>/dev/null | wc -l)
            local bucket_size=$(docker exec "$MINIO_CONTAINER" du -sh "$bucket_path" 2>/dev/null | cut -f1 || echo "0")
            log "  Bucket: $bucket_name - Objects: $object_count - Size: $bucket_size"
        fi
    done
    
    log "MinIO restore verification completed"
    return 0
}

# Main execution
main() {
    log "=== Ottiq MinIO Restore Started ==="
    log "Environment: $ENVIRONMENT"
    log "Backup file: $BACKUP_FILE"
    log "Timestamp: $TIMESTAMP"
    
    # Resolve backup file path
    local backup_path
    if [[ "$BACKUP_FILE" == /* ]]; then
        # Absolute path
        backup_path="$BACKUP_FILE"
    elif [[ -f "$BACKUP_DIR/$BACKUP_FILE" ]]; then
        # Relative to backup directory
        backup_path="$BACKUP_DIR/$BACKUP_FILE"
    elif [[ -f "$BACKUP_FILE" ]]; then
        # Relative to current directory
        backup_path="$BACKUP_FILE"
    else
        log "ERROR: Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    # Pre-flight checks
    check_container || exit 1
    test_connection || exit 1
    validate_backup_file "$backup_path" || exit 1
    
    # Confirmation prompt
    echo ""
    echo "WARNING: This will completely replace all MinIO data!"
    echo "Environment: $ENVIRONMENT"
    echo "Container: $MINIO_CONTAINER"
    echo "Volume: $MINIO_VOLUME"
    echo "Backup file: $backup_path"
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log "Restore cancelled by user"
        exit 0
    fi
    
    # Create pre-restore backup
    create_pre_restore_backup || exit 1
    
    # Perform restore
    restore_minio_data "$backup_path" || exit 1
    
    # Verify restore
    verify_restore || exit 1
    
    log "=== Ottiq MinIO Restore Completed Successfully ==="
}

# Run main function
main "$@"
