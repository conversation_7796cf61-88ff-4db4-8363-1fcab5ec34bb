/**
 * Order Cancelled Email Template
 * Handles order cancellation with empathy and next steps
 */

import { Section, Text, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { OrderEmailData } from '../../lib/services/email';

interface OrderCancelledEmailProps extends OrderEmailData {
  cancellationReason?: string;
  refundAmount?: string;
  refundMethod?: string;
  refundTimeframe?: string;
}

export function OrderCancelledEmail({
  customerName,
  orderNumber,
  totalAmount,
  currency,
  items,
  cancellationReason,
  refundAmount,
  refundMethod,
  refundTimeframe,
  orderUrl,
}: OrderCancelledEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`Your Ottiq order #${orderNumber} has been cancelled. We're here to help with your next steps.`}>
      {/* Header */}
      <Section className="text-center mb-8">
        <EmailHeading level={1}>
          Order Cancelled - #{orderNumber}
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Hi {customerName}, we've cancelled your order as requested. We're sorry to see this one go, but we're here to help with whatever you need next.
        </EmailText>
      </Section>

      {/* Cancellation Details */}
      <Section className="bg-red-50 rounded-lg p-6 mb-8">
        <EmailHeading level={2}>Cancellation Details</EmailHeading>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <Text className="text-gray-700">Order Number:</Text>
            <Text className="font-semibold text-gray-900">#{orderNumber}</Text>
          </div>
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Order Amount:</Text>
            <Text className="font-semibold text-gray-900">{currency} {totalAmount}</Text>
          </div>
          
          {cancellationReason && (
            <div className="flex justify-between">
              <Text className="text-gray-700">Reason:</Text>
              <Text className="font-semibold text-gray-900">{cancellationReason}</Text>
            </div>
          )}
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Status:</Text>
            <Text className="font-semibold text-red-600">Cancelled</Text>
          </div>
        </div>
      </Section>

      {/* Cancelled Items */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={2}>Cancelled Items</EmailHeading>
        
        <div className="space-y-3">
          {items.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
              <div className="flex-1">
                <Text className="font-semibold text-gray-900 mb-1">
                  {item.name}
                </Text>
                {item.customization && (
                  <Text className="text-sm text-gray-600 mb-1">
                    Custom Design: {item.customization.name}
                  </Text>
                )}
                <Text className="text-sm text-gray-600">
                  Quantity: {item.quantity} × {currency} {item.price}
                </Text>
              </div>
              <Text className="text-red-600 font-semibold text-sm">
                Cancelled
              </Text>
            </div>
          ))}
        </div>
      </Section>

      {/* Refund Information */}
      {refundAmount && (
        <Section className="bg-green-50 rounded-lg p-6 mb-8">
          <EmailHeading level={2}>💰 Refund Information</EmailHeading>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <Text className="text-gray-700">Refund Amount:</Text>
              <Text className="font-semibold text-green-700">{currency} {refundAmount}</Text>
            </div>
            
            {refundMethod && (
              <div className="flex justify-between">
                <Text className="text-gray-700">Refund Method:</Text>
                <Text className="font-semibold text-gray-900">{refundMethod}</Text>
              </div>
            )}
            
            {refundTimeframe && (
              <div className="flex justify-between">
                <Text className="text-gray-700">Expected Timeframe:</Text>
                <Text className="font-semibold text-gray-900">{refundTimeframe}</Text>
              </div>
            )}
          </div>
          
          <Hr className="my-4" />
          
          <EmailText className="text-sm text-green-700 mb-0">
            Your refund is being processed and will appear in your account within the specified timeframe. You'll receive a separate confirmation once the refund is complete.
          </EmailText>
        </Section>
      )}

      {/* What's Next */}
      <Section className="mb-8">
        <EmailHeading level={2}>We're Still Here for You</EmailHeading>
        <EmailText>
          Even though this order didn't work out, we'd love to help you find the perfect custom piece. Here are some ways we can assist:
        </EmailText>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">🎨</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Try a Different Design
              </Text>
              <Text className="text-gray-600 text-sm">
                Browse our templates or create something completely new that matches your style perfectly.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">💬</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Get Style Advice
              </Text>
              <Text className="text-gray-600 text-sm">
                Our team can help you find the right product, size, or design that fits your vision.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-primary-600 font-bold">🎁</Text>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Gift Someone Special
              </Text>
              <Text className="text-gray-600 text-sm">
                Maybe this is the perfect time to create a custom gift for someone you care about.
              </Text>
            </div>
          </div>
        </div>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-8">
        <EmailHeading level={2}>Ready to Try Again?</EmailHeading>
        <EmailText>
          We believe everyone deserves to wear their imagination. Let's find the perfect way to express your unique style.
        </EmailText>
        
        <div className="space-y-3">
          <EmailButton href={`${baseUrl}/create`} variant="primary">
            Start a New Design
          </EmailButton>
          
          <br />
          
          <EmailButton href={`${baseUrl}/templates`} variant="outline">
            Browse Templates
          </EmailButton>
        </div>
      </Section>

      {/* Support */}
      <Section className="bg-blue-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>Need Help or Have Questions?</EmailHeading>
        <EmailText>
          Our support team is here to help with any questions about your cancellation, refund, or future orders.
        </EmailText>
        
        <EmailButton href={`${baseUrl}/support`} variant="outline">
          Contact Support
        </EmailButton>
      </Section>

      {/* Order Details */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          Need to reference the cancelled order details?
        </EmailText>
        <EmailButton href={orderUrl} variant="outline">
          View Order Details
        </EmailButton>
      </Section>
    </EmailLayout>
  );
}
