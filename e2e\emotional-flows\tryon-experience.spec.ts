import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { ProductHelpers } from '../utils/product-helpers';
import { TryOnHelpers } from '../utils/tryon-helpers';

test.describe('Emotional AI Try-On Experience', () => {
  let authHelpers: AuthHelpers;
  let productHelpers: ProductHelpers;
  let tryOnHelpers: TryOnHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    productHelpers = new ProductHelpers(page);
    tryOnHelpers = new TryOnHelpers(page);
    await authHelpers.mockAuthentication('regular');
    
    // Create a design to try on
    await productHelpers.selectCategory('t-shirts');
    await productHelpers.selectProduct();
    await productHelpers.startCustomization();
    await productHelpers.addTextToDesign('My Style');
    await productHelpers.selectColor('navy');
    await productHelpers.saveDesign('Try-On Test');
    await productHelpers.proceedToTryOn();
  });

  test('should create excitement and anticipation for AI try-on', async ({ page }) => {
    await test.step('Try-on introduction should build excitement', async () => {
      // Should show inspiring introduction
      await expect(page.getByText(/see yourself wearing|bring your vision to life|imagine how amazing/i)).toBeVisible();
      
      // Should explain the magic about to happen
      const introText = page.getByTestId('tryon-intro');
      if (await introText.isVisible()) {
        const text = await introText.textContent();
        expect(text).toMatch(/AI technology|see how it looks|personalized|just for you/i);
      }
      
      // Should have encouraging call-to-action
      const uploadPrompt = page.getByTestId('upload-prompt');
      await expect(uploadPrompt).toBeVisible();
      const promptText = await uploadPrompt.textContent();
      expect(promptText).toMatch(/upload your photo|let's see you in it|show us your style/i);
    });

    await test.step('Photo upload should feel safe and exciting', async () => {
      // Upload area should be welcoming
      const uploadArea = page.getByTestId('photo-upload-area');
      await expect(uploadArea).toBeVisible();
      
      // Should show privacy assurance
      const privacyNote = page.getByTestId('privacy-note');
      if (await privacyNote.isVisible()) {
        const privacyText = await privacyNote.textContent();
        expect(privacyText).toMatch(/private|secure|not stored|just for you/i);
      }
      
      // Should have helpful photo tips
      const photoTips = page.getByTestId('photo-tips');
      if (await photoTips.isVisible()) {
        await expect(page.getByText(/good lighting|face the camera|clear photo/i)).toBeVisible();
      }
      
      // Upload photo and verify encouraging feedback
      await tryOnHelpers.uploadUserPhoto();
      
      // Should show positive upload confirmation
      await expect(page.getByText(/great photo|perfect|processing your image/i)).toBeVisible();
    });

    await test.step('AI processing should maintain engagement', async () => {
      await tryOnHelpers.waitForAIProcessing();
      
      // Should show progress with exciting messages
      const processingMessages = [
        /analyzing your photo/i,
        /applying your design/i,
        /creating your look/i,
        /almost ready/i,
        /finalizing the magic/i
      ];
      
      // At least one encouraging message should be visible
      let messageFound = false;
      for (const messagePattern of processingMessages) {
        const message = page.getByText(messagePattern);
        if (await message.isVisible()) {
          messageFound = true;
          break;
        }
      }
      expect(messageFound).toBeTruthy();
      
      // Should show progress indicator
      const progressBar = page.getByTestId('processing-progress');
      if (await progressBar.isVisible()) {
        // Progress should be visually engaging
        const progressValue = await progressBar.getAttribute('value');
        expect(progressValue).toBeTruthy();
      }
    });
  });

  test('should deliver amazing try-on results with emotional impact', async ({ page }) => {
    await test.step('Try-on result should create wow moment', async () => {
      await tryOnHelpers.uploadUserPhoto();
      await tryOnHelpers.waitForAIProcessing();
      await tryOnHelpers.verifyTryOnResult();
      
      // Should show celebratory messaging
      await expect(page.getByText(/amazing|incredible|perfect|stunning|you look great/i)).toBeVisible();
      
      // Should provide emotional validation
      const resultFeedback = page.getByTestId('result-feedback');
      if (await resultFeedback.isVisible()) {
        const feedbackText = await resultFeedback.textContent();
        expect(feedbackText).toMatch(/looks amazing on you|perfect fit|great choice|suits you/i);
      }
      
      // Should show high-quality result
      const resultImage = page.getByTestId('tryon-result').getByRole('img');
      await expect(resultImage).toBeVisible();
      
      // Verify image quality indicators
      const qualityBadge = page.getByTestId('quality-indicator');
      if (await qualityBadge.isVisible()) {
        const qualityText = await qualityBadge.textContent();
        expect(qualityText).toMatch(/high quality|excellent|premium/i);
      }
    });

    await test.step('Multiple angles should enhance the experience', async () => {
      await tryOnHelpers.testDifferentAngles();
      
      // Each angle should maintain emotional engagement
      const angleButtons = page.getByTestId(/angle-/);
      const angleCount = await angleButtons.count();
      
      if (angleCount > 1) {
        // Test front view
        const frontView = page.getByTestId('angle-front');
        if (await frontView.isVisible()) {
          await frontView.click();
          await page.waitForTimeout(2000);
          
          // Should show angle-specific feedback
          const angleFeedback = page.getByTestId('angle-feedback');
          if (await angleFeedback.isVisible()) {
            const feedbackText = await angleFeedback.textContent();
            expect(feedbackText).toMatch(/front view|classic look|perfect angle/i);
          }
        }
        
        // Test side view if available
        const sideView = page.getByTestId('angle-side');
        if (await sideView.isVisible()) {
          await sideView.click();
          await page.waitForTimeout(2000);
          
          const sideFeedback = page.getByTestId('angle-feedback');
          if (await sideFeedback.isVisible()) {
            const feedbackText = await sideFeedback.textContent();
            expect(feedbackText).toMatch(/side profile|great silhouette|nice fit/i);
          }
        }
      }
    });

    await test.step('Social sharing should build pride and excitement', async () => {
      await tryOnHelpers.shareResult('social');
      
      // Should encourage sharing with pride-building copy
      const shareModal = page.getByTestId('share-modal');
      if (await shareModal.isVisible()) {
        await expect(page.getByText(/show off your style|share your look|let everyone see/i)).toBeVisible();
        
        // Should have multiple sharing options
        const shareOptions = page.getByTestId(/share-/);
        const optionCount = await shareOptions.count();
        expect(optionCount).toBeGreaterThan(0);
        
        // Test social media sharing
        const socialShare = page.getByTestId('share-social');
        if (await socialShare.isVisible()) {
          await socialShare.click();
          
          // Should show social sharing options
          const socialPlatforms = page.getByTestId(/social-/);
          const platformCount = await socialPlatforms.count();
          expect(platformCount).toBeGreaterThan(0);
        }
      }
    });
  });

  test('should handle try-on failures gracefully with emotional support', async ({ page }) => {
    await test.step('AI service failures should show supportive fallback', async () => {
      await tryOnHelpers.testFallbackExperience();
      
      // Should show understanding and supportive messaging
      await expect(page.getByText(/temporarily unavailable|working to fix this|try again soon/i)).toBeVisible();
      
      // Should offer alternative experiences
      const alternatives = page.getByTestId('fallback-options');
      await expect(alternatives).toBeVisible();
      
      // Should still allow proceeding
      const continueButton = page.getByRole('button', { name: /continue|proceed|skip for now/i });
      await expect(continueButton).toBeVisible();
    });

    await test.step('Photo quality issues should provide helpful guidance', async () => {
      // Simulate poor quality photo upload
      await page.route('**/api/ai/photo-analysis', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ 
            quality: 'poor',
            issues: ['low_light', 'blurry'],
            suggestions: ['Use better lighting', 'Take a clearer photo']
          })
        });
      });
      
      await tryOnHelpers.uploadUserPhoto();
      
      // Should show helpful, non-critical feedback
      const qualityFeedback = page.getByTestId('photo-quality-feedback');
      if (await qualityFeedback.isVisible()) {
        const feedbackText = await qualityFeedback.textContent();
        expect(feedbackText).toMatch(/better lighting|clearer photo|try again/i);
        expect(feedbackText).not.toMatch(/bad|wrong|error|failed/i);
      }
      
      // Should offer to retake photo
      const retakeButton = page.getByRole('button', { name: /try another photo|retake|upload different/i });
      await expect(retakeButton).toBeVisible();
    });

    await test.step('Processing timeouts should maintain user engagement', async () => {
      // Simulate slow processing
      await page.route('**/api/ai/tryon', route => {
        // Delay response significantly
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ result: 'success', image_url: '/test-result.jpg' })
          });
        }, 10000);
      });
      
      await tryOnHelpers.uploadUserPhoto();
      
      // Should show patience-building messages
      const extendedProcessing = page.getByTestId('extended-processing');
      if (await extendedProcessing.isVisible()) {
        const processingText = await extendedProcessing.textContent();
        expect(processingText).toMatch(/taking a bit longer|worth the wait|creating something special/i);
      }
      
      // Should offer option to continue without try-on
      const skipOption = page.getByRole('button', { name: /skip try-on|continue without/i });
      if (await skipOption.isVisible()) {
        await skipOption.click();
        
        // Should proceed to checkout
        await expect(page.getByText(/checkout|order|purchase/i)).toBeVisible();
      }
    });
  });

  test('should optimize try-on experience for mobile devices', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await test.step('Mobile photo upload should be intuitive', async () => {
      await tryOnHelpers.testMobileTryOnExperience();
      
      // Should support camera access on mobile
      const cameraButton = page.getByRole('button', { name: /take photo|use camera/i });
      if (await cameraButton.isVisible()) {
        // Note: Can't actually test camera in headless mode
        await expect(cameraButton).toBeVisible();
      }
      
      // Upload area should be touch-friendly
      const uploadArea = page.getByTestId('photo-upload-area');
      const uploadBox = await uploadArea.boundingBox();
      if (uploadBox) {
        expect(uploadBox.height).toBeGreaterThanOrEqual(120);
      }
    });

    await test.step('Mobile result display should be optimized', async () => {
      await tryOnHelpers.uploadUserPhoto();
      await tryOnHelpers.waitForAIProcessing();
      
      // Result should fit mobile screen
      const result = page.getByTestId('tryon-result');
      await expect(result).toBeVisible();
      
      const resultBox = await result.boundingBox();
      if (resultBox) {
        expect(resultBox.width).toBeLessThanOrEqual(375);
      }
      
      // Mobile-specific controls should be touch-friendly
      const mobileControls = page.getByTestId('mobile-controls');
      if (await mobileControls.isVisible()) {
        const controls = mobileControls.getByRole('button');
        const controlCount = await controls.count();
        
        for (let i = 0; i < controlCount; i++) {
          const control = controls.nth(i);
          const controlBox = await control.boundingBox();
          if (controlBox) {
            expect(controlBox.height).toBeGreaterThanOrEqual(44);
          }
        }
      }
    });

    await test.step('Mobile sharing should be seamless', async () => {
      const shareButton = page.getByRole('button', { name: /share/i });
      if (await shareButton.isVisible()) {
        await shareButton.click();
        
        // Should show mobile-optimized sharing
        const mobileShare = page.getByTestId('mobile-share');
        if (await mobileShare.isVisible()) {
          // Should support native mobile sharing if available
          const nativeShare = page.getByRole('button', { name: /share via|native share/i });
          if (await nativeShare.isVisible()) {
            await expect(nativeShare).toBeVisible();
          }
        }
      }
    });
  });

  test('should maintain accessibility throughout try-on experience', async ({ page }) => {
    await test.step('Try-on interface should be screen reader friendly', async () => {
      await tryOnHelpers.testTryOnAccessibility();
      
      // Upload area should have proper labels
      const uploadArea = page.getByTestId('photo-upload-area');
      const ariaLabel = await uploadArea.getAttribute('aria-label');
      expect(ariaLabel).toBeTruthy();
      expect(ariaLabel).toMatch(/upload|photo|image/i);
      
      // Processing state should be announced
      await tryOnHelpers.uploadUserPhoto();
      
      const processingAnnouncement = page.getByRole('status');
      if (await processingAnnouncement.isVisible()) {
        const announcementText = await processingAnnouncement.textContent();
        expect(announcementText).toMatch(/processing|analyzing|creating/i);
      }
    });

    await test.step('Try-on results should have proper alt text', async () => {
      await tryOnHelpers.waitForAIProcessing();
      
      const resultImage = page.getByTestId('tryon-result').getByRole('img');
      const altText = await resultImage.getAttribute('alt');
      expect(altText).toBeTruthy();
      expect(altText).toMatch(/try-on result|wearing|preview/i);
    });

    await test.step('Interactive elements should be keyboard accessible', async () => {
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      
      // Should focus on interactive elements
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Test angle switching with keyboard
      const angleButtons = page.getByTestId(/angle-/);
      const angleCount = await angleButtons.count();
      
      if (angleCount > 0) {
        const firstAngle = angleButtons.first();
        await firstAngle.focus();
        await page.keyboard.press('Enter');
        await page.waitForTimeout(1000);
      }
    });
  });
});
