import { Page, expect } from '@playwright/test';

export class CheckoutHelpers {
  constructor(private page: Page) {}

  /**
   * Navigate to checkout page
   */
  async goToCheckout() {
    await this.page.goto('/checkout');
    await this.page.waitForLoadState('networkidle');
    return this;
  }

  /**
   * Verify order summary with emotional elements
   */
  async verifyOrderSummary() {
    const orderSummary = this.page.getByTestId('order-summary');
    await expect(orderSummary).toBeVisible();
    
    // Check for product image
    const productImage = orderSummary.getByRole('img');
    await expect(productImage).toBeVisible();
    
    // Check for emotional product description
    const productDescription = orderSummary.getByTestId('product-description');
    await expect(productDescription).toBeVisible();
    const descriptionText = await productDescription.textContent();
    expect(descriptionText).toMatch(/your creation|custom design|unique style/i);
    
    // Check for pricing
    const totalPrice = this.page.getByTestId('total-price');
    await expect(totalPrice).toBeVisible();
    
    return this;
  }

  /**
   * Fill shipping information
   */
  async fillShippingInfo(shippingData = {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    address: '123 Style Street',
    city: 'Fashion City',
    state: 'CA',
    zipCode: '90210',
    country: 'US'
  }) {
    // Fill shipping form
    await this.page.getByTestId('shipping-first-name').fill(shippingData.firstName);
    await this.page.getByTestId('shipping-last-name').fill(shippingData.lastName);
    await this.page.getByTestId('shipping-email').fill(shippingData.email);
    await this.page.getByTestId('shipping-address').fill(shippingData.address);
    await this.page.getByTestId('shipping-city').fill(shippingData.city);
    await this.page.getByTestId('shipping-state').selectOption(shippingData.state);
    await this.page.getByTestId('shipping-zip').fill(shippingData.zipCode);
    await this.page.getByTestId('shipping-country').selectOption(shippingData.country);
    
    return this;
  }

  /**
   * Select shipping method with emotional messaging
   */
  async selectShippingMethod(method: 'standard' | 'express' | 'overnight' = 'standard') {
    const shippingOptions = this.page.getByTestId('shipping-options');
    await expect(shippingOptions).toBeVisible();
    
    const shippingMethod = this.page.getByTestId(`shipping-${method}`);
    await expect(shippingMethod).toBeVisible();
    
    // Verify emotional messaging for shipping
    const methodText = await shippingMethod.textContent();
    if (method === 'express') {
      expect(methodText).toMatch(/faster|sooner|can't wait/i);
    } else if (method === 'overnight') {
      expect(methodText).toMatch(/tomorrow|next day|immediate/i);
    }
    
    await shippingMethod.click();
    
    return this;
  }

  /**
   * Fill payment information (test mode)
   */
  async fillPaymentInfo(paymentData = {
    cardNumber: '****************',
    expiryMonth: '12',
    expiryYear: '2025',
    cvc: '123',
    name: 'Test User'
  }) {
    // Wait for payment form to load
    const paymentForm = this.page.getByTestId('payment-form');
    await expect(paymentForm).toBeVisible();
    
    // Fill credit card information
    await this.page.getByTestId('card-number').fill(paymentData.cardNumber);
    await this.page.getByTestId('card-expiry-month').selectOption(paymentData.expiryMonth);
    await this.page.getByTestId('card-expiry-year').selectOption(paymentData.expiryYear);
    await this.page.getByTestId('card-cvc').fill(paymentData.cvc);
    await this.page.getByTestId('card-name').fill(paymentData.name);
    
    return this;
  }

  /**
   * Apply discount code
   */
  async applyDiscountCode(code: string = 'TESTCODE10') {
    const discountSection = this.page.getByTestId('discount-section');
    if (await discountSection.isVisible()) {
      const discountInput = this.page.getByTestId('discount-code');
      await discountInput.fill(code);
      
      const applyButton = this.page.getByRole('button', { name: /apply|use code/i });
      await applyButton.click();
      
      // Wait for discount to be applied or error message
      await this.page.waitForTimeout(2000);
    }
    
    return this;
  }

  /**
   * Complete purchase with emotional validation
   */
  async completePurchase() {
    // Verify final order details
    await this.verifyOrderSummary();
    
    // Look for emotional purchase button
    const purchaseButton = this.page.getByRole('button', { 
      name: /complete order|make it mine|bring it to life|create my style/i 
    });
    await expect(purchaseButton).toBeVisible();
    
    // Verify button is enabled
    await expect(purchaseButton).toBeEnabled();
    
    // Click purchase button
    await purchaseButton.click();
    
    // Wait for processing
    await expect(this.page.getByText(/processing|creating|preparing/i)).toBeVisible();
    
    return this;
  }

  /**
   * Verify order confirmation with emotional messaging
   */
  async verifyOrderConfirmation() {
    // Wait for confirmation page
    await this.page.waitForLoadState('networkidle');
    
    // Check for success message with emotional copy
    const successMessage = this.page.getByTestId('order-success');
    await expect(successMessage).toBeVisible();
    
    const successText = await successMessage.textContent();
    expect(successText).toMatch(/congratulations|amazing|created|yours|coming to life/i);
    
    // Check for order number
    const orderNumber = this.page.getByTestId('order-number');
    await expect(orderNumber).toBeVisible();
    
    // Check for estimated delivery
    const deliveryInfo = this.page.getByTestId('delivery-info');
    await expect(deliveryInfo).toBeVisible();
    
    // Check for next steps with emotional engagement
    const nextSteps = this.page.getByTestId('next-steps');
    await expect(nextSteps).toBeVisible();
    const nextStepsText = await nextSteps.textContent();
    expect(nextStepsText).toMatch(/track|updates|share|excited/i);
    
    return this;
  }

  /**
   * Test payment security features
   */
  async testPaymentSecurity() {
    // Verify SSL indicators
    expect(this.page.url()).toContain('https://');
    
    // Check for security badges
    const securityBadges = this.page.getByTestId('security-badges');
    if (await securityBadges.isVisible()) {
      await expect(securityBadges).toBeVisible();
    }
    
    // Verify payment form is secure
    const paymentForm = this.page.getByTestId('payment-form');
    const formAction = await paymentForm.getAttribute('action');
    if (formAction) {
      expect(formAction).toContain('https://');
    }
    
    return this;
  }

  /**
   * Test mobile checkout experience
   */
  async testMobileCheckoutExperience() {
    await this.page.setViewportSize({ width: 375, height: 667 });
    
    // Verify mobile-optimized form fields
    const formFields = this.page.getByRole('textbox');
    const fieldCount = await formFields.count();
    
    for (let i = 0; i < fieldCount; i++) {
      const field = formFields.nth(i);
      const fieldBox = await field.boundingBox();
      if (fieldBox) {
        expect(fieldBox.height).toBeGreaterThanOrEqual(44); // Touch-friendly
      }
    }
    
    // Test mobile payment button
    const purchaseButton = this.page.getByRole('button', { 
      name: /complete order|make it mine/i 
    });
    const buttonBox = await purchaseButton.boundingBox();
    if (buttonBox) {
      expect(buttonBox.height).toBeGreaterThanOrEqual(48);
      expect(buttonBox.width).toBeGreaterThan(200); // Wide enough for mobile
    }
    
    return this;
  }

  /**
   * Test checkout accessibility
   */
  async testCheckoutAccessibility() {
    // Check form labels
    const formFields = this.page.getByRole('textbox');
    const fieldCount = await formFields.count();
    
    for (let i = 0; i < fieldCount; i++) {
      const field = formFields.nth(i);
      const fieldId = await field.getAttribute('id');
      if (fieldId) {
        const label = this.page.locator(`label[for="${fieldId}"]`);
        await expect(label).toBeVisible();
      }
    }
    
    // Check for error message accessibility
    const errorMessages = this.page.getByRole('alert');
    const errorCount = await errorMessages.count();
    
    for (let i = 0; i < errorCount; i++) {
      const error = errorMessages.nth(i);
      const errorText = await error.textContent();
      expect(errorText?.trim()).toBeTruthy();
    }
    
    // Check for proper heading hierarchy
    const headings = this.page.getByRole('heading');
    const headingCount = await headings.count();
    expect(headingCount).toBeGreaterThan(0);
    
    return this;
  }

  /**
   * Test abandoned cart recovery
   */
  async testAbandonedCartRecovery() {
    // Fill some information but don't complete
    await this.fillShippingInfo();
    
    // Leave the page
    await this.page.goto('/');
    
    // Come back to checkout
    await this.goToCheckout();
    
    // Verify information is preserved
    const firstNameField = this.page.getByTestId('shipping-first-name');
    const savedValue = await firstNameField.inputValue();
    expect(savedValue).toBe('Test');
    
    return this;
  }
}
