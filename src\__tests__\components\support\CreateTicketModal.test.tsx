/**
 * CreateTicketModal Component Tests
 * Tests for the support ticket creation modal
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CreateTicketModal } from '@/components/support/CreateTicketModal';

// Mock fetch
global.fetch = jest.fn();

// Mock useResponsive hook
jest.mock('@/hooks/useResponsive', () => ({
  useResponsive: () => ({
    isMobile: false,
  }),
}));

describe('CreateTicketModal', () => {
  const mockOnClose = jest.fn();
  const mockOnTicketCreated = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onTicketCreated: mockOnTicketCreated,
  };

  it('should render the modal when open', () => {
    render(<CreateTicketModal {...defaultProps} />);

    expect(screen.getByText('🎫 Create Support Ticket')).toBeInTheDocument();
    expect(screen.getByText('Tell us how we can help you. Our support team will respond as soon as possible.')).toBeInTheDocument();
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
    expect(screen.getByLabelText('Priority')).toBeInTheDocument();
    expect(screen.getByLabelText('Subject')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
  });

  it('should not render when closed', () => {
    render(<CreateTicketModal {...defaultProps} isOpen={false} />);

    expect(screen.queryByText('🎫 Create Support Ticket')).not.toBeInTheDocument();
  });

  it('should handle form submission successfully', async () => {
    const user = userEvent.setup();
    
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { id: 'ticket-123' },
      }),
    });

    render(<CreateTicketModal {...defaultProps} />);

    // Fill out the form
    await user.selectOptions(screen.getByLabelText('Category'), 'technical');
    await user.selectOptions(screen.getByLabelText('Priority'), 'HIGH');
    await user.type(screen.getByLabelText('Subject'), 'Test support issue');
    await user.type(
      screen.getByLabelText('Description'),
      'This is a detailed description of the support issue that needs to be resolved.'
    );

    // Submit the form
    await user.click(screen.getByText('🚀 Create Ticket'));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: 'Test support issue',
          description: 'This is a detailed description of the support issue that needs to be resolved.',
          category: 'technical',
          priority: 'HIGH',
        }),
      });
    });

    await waitFor(() => {
      expect(mockOnTicketCreated).toHaveBeenCalled();
    });
  });

  it('should display validation errors', async () => {
    const user = userEvent.setup();
    
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        success: false,
        error: 'Validation failed',
        details: [
          { path: ['subject'], message: 'Subject is too short' },
          { path: ['description'], message: 'Description is too short' },
        ],
      }),
    });

    render(<CreateTicketModal {...defaultProps} />);

    // Try to submit with minimal data
    await user.type(screen.getByLabelText('Subject'), 'Hi');
    await user.type(screen.getByLabelText('Description'), 'Help');
    await user.click(screen.getByText('🚀 Create Ticket'));

    await waitFor(() => {
      expect(screen.getByText('Validation failed')).toBeInTheDocument();
    });
  });

  it('should handle API errors', async () => {
    const user = userEvent.setup();
    
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<CreateTicketModal {...defaultProps} />);

    // Fill out valid form
    await user.selectOptions(screen.getByLabelText('Category'), 'general');
    await user.type(screen.getByLabelText('Subject'), 'Valid subject line');
    await user.type(
      screen.getByLabelText('Description'),
      'This is a valid description that meets the minimum length requirements.'
    );

    await user.click(screen.getByText('🚀 Create Ticket'));

    await waitFor(() => {
      expect(screen.getByText('Failed to create ticket. Please try again.')).toBeInTheDocument();
    });
  });

  it('should disable submit button when form is invalid', () => {
    render(<CreateTicketModal {...defaultProps} />);

    const submitButton = screen.getByText('🚀 Create Ticket');
    expect(submitButton).toBeDisabled();
  });

  it('should enable submit button when form is valid', async () => {
    const user = userEvent.setup();
    
    render(<CreateTicketModal {...defaultProps} />);

    // Fill out required fields
    await user.type(screen.getByLabelText('Subject'), 'Valid subject');
    await user.type(screen.getByLabelText('Description'), 'Valid description that is long enough');

    const submitButton = screen.getByText('🚀 Create Ticket');
    expect(submitButton).not.toBeDisabled();
  });

  it('should show character counts', async () => {
    const user = userEvent.setup();
    
    render(<CreateTicketModal {...defaultProps} />);

    const subjectInput = screen.getByLabelText('Subject');
    const descriptionInput = screen.getByLabelText('Description');

    // Initially should show 0 characters
    expect(screen.getByText('0/200')).toBeInTheDocument();
    expect(screen.getByText('0/5000')).toBeInTheDocument();

    // Type some text
    await user.type(subjectInput, 'Test subject');
    await user.type(descriptionInput, 'Test description');

    // Should update character counts
    expect(screen.getByText('12/200')).toBeInTheDocument();
    expect(screen.getByText('16/5000')).toBeInTheDocument();
  });

  it('should call onClose when cancel button is clicked', async () => {
    const user = userEvent.setup();
    
    render(<CreateTicketModal {...defaultProps} />);

    await user.click(screen.getByText('Cancel'));

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should call onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    
    render(<CreateTicketModal {...defaultProps} />);

    await user.click(screen.getByText('✕'));

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should show loading state during submission', async () => {
    const user = userEvent.setup();
    
    // Mock a slow response
    (global.fetch as jest.Mock).mockImplementationOnce(
      () => new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ success: true, data: { id: 'ticket-123' } }),
      }), 100))
    );

    render(<CreateTicketModal {...defaultProps} />);

    // Fill out form
    await user.type(screen.getByLabelText('Subject'), 'Test subject');
    await user.type(screen.getByLabelText('Description'), 'Test description that is long enough');

    // Submit form
    await user.click(screen.getByText('🚀 Create Ticket'));

    // Should show loading state
    expect(screen.getByText('Creating Ticket...')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeDisabled();
  });

  it('should reset form after successful submission', async () => {
    const user = userEvent.setup();
    
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { id: 'ticket-123' },
      }),
    });

    render(<CreateTicketModal {...defaultProps} />);

    const subjectInput = screen.getByLabelText('Subject') as HTMLInputElement;
    const descriptionInput = screen.getByLabelText('Description') as HTMLTextAreaElement;

    // Fill out form
    await user.type(subjectInput, 'Test subject');
    await user.type(descriptionInput, 'Test description that is long enough');

    expect(subjectInput.value).toBe('Test subject');
    expect(descriptionInput.value).toBe('Test description that is long enough');

    // Submit form
    await user.click(screen.getByText('🚀 Create Ticket'));

    await waitFor(() => {
      expect(mockOnTicketCreated).toHaveBeenCalled();
    });

    // Form should be reset (this would happen in the actual component)
    // Note: In the actual implementation, the form is reset after successful submission
  });
});
