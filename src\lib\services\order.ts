/**
 * Order Management Service
 * 
 * Handles order lifecycle management, status updates, and integration
 * with pricing engine, payment system, and shipping.
 */

import { PrismaClient } from '@prisma/client';
import { PricingEngine } from '@/lib/pricing/engine';
import { emailService } from './email';
import {
  Order,
  OrderStatus,
  PaymentStatus,
  ShippingStatus,
  CheckoutAddress
} from '@/types/payment';

const prisma = new PrismaClient();

export interface CreateOrderRequest {
  userId: string;
  items: OrderItem[];
  shippingAddress: CheckoutAddress;
  billingAddress?: CheckoutAddress;
  shippingMethod?: string;
  giftMessage?: string;
  isGift?: boolean;
  giftRecipientName?: string;
  giftRecipientEmail?: string;
  couponCode?: string;
}

export interface OrderItem {
  productId: string;
  customizationId?: string;
  quantity: number;
  variantId?: string;
  fabricId?: string;
  printSize?: string;
  qualityTier?: string;
}

export interface OrderCalculation {
  subtotal: number;
  shippingCost: number;
  processingFee: number;
  discount: number;
  tax: number;
  total: number;
  currency: string;
  breakdown: OrderBreakdownItem[];
}

export interface OrderBreakdownItem {
  id: string;
  label: string;
  amount: number;
  type: 'positive' | 'negative' | 'neutral';
  description?: string;
}

export interface OrderUpdateRequest {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  trackingNumber?: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  adminNotes?: string;
}

export class OrderService {
  private pricingEngine = PricingEngine.getInstance();

  /**
   * Calculate order totals using the pricing engine
   */
  async calculateOrderTotals(items: OrderItem[], shippingAddress: CheckoutAddress, shippingMethod = 'standard'): Promise<OrderCalculation> {
    let subtotal = 0;
    const breakdown: OrderBreakdownItem[] = [];

    // Calculate item prices using pricing engine
    for (const item of items) {
      const pricingRequest = {
        productId: item.productId,
        variantId: item.variantId,
        customizationId: item.customizationId,
        quantity: item.quantity,
        fabricId: item.fabricId,
        printSize: item.printSize as any,
        qualityTier: item.qualityTier as any,
      };

      const pricingResult = await this.pricingEngine.calculatePrice(pricingRequest);
      
      if (pricingResult.success && pricingResult.data) {
        const itemTotal = pricingResult.data.totalPrice;
        subtotal += itemTotal;

        breakdown.push({
          id: `item-${item.productId}`,
          label: `Product (${item.quantity}x)`,
          amount: itemTotal,
          type: 'positive',
          description: pricingResult.data.breakdown?.map(b => b.description).join(', '),
        });
      }
    }

    // Calculate shipping cost
    const shippingCost = this.calculateShippingCost(shippingMethod, shippingAddress.district, subtotal);
    if (shippingCost > 0) {
      breakdown.push({
        id: 'shipping',
        label: 'Shipping',
        amount: shippingCost,
        type: 'positive',
        description: `${shippingMethod} delivery to ${shippingAddress.district}`,
      });
    } else {
      breakdown.push({
        id: 'free-shipping',
        label: 'Free Shipping',
        amount: 0,
        type: 'neutral',
        description: 'Free shipping on orders over ৳1000',
      });
    }

    // Calculate processing fee (if any)
    const processingFee = 0; // No processing fee for now

    // Calculate discount (placeholder)
    const discount = 0;

    // Calculate tax (Bangladesh VAT - placeholder)
    const tax = 0;

    const total = subtotal + shippingCost + processingFee + tax - discount;

    return {
      subtotal,
      shippingCost,
      processingFee,
      discount,
      tax,
      total,
      currency: 'BDT',
      breakdown,
    };
  }

  /**
   * Create a new order
   */
  async createOrder(request: CreateOrderRequest): Promise<{ success: boolean; order?: any; error?: string }> {
    try {
      // Calculate order totals
      const calculation = await this.calculateOrderTotals(
        request.items,
        request.shippingAddress,
        request.shippingMethod
      );

      // Generate order number
      const orderNumber = this.generateOrderNumber();

      // Create order with transaction
      const order = await prisma.$transaction(async (tx) => {
        // Create the order
        const newOrder = await tx.order.create({
          data: {
            orderNumber,
            status: 'PENDING',
            totalAmount: calculation.total,
            currency: calculation.currency,
            shippingAddress: request.shippingAddress,
            billingAddress: request.billingAddress || request.shippingAddress,
            giftMessage: request.giftMessage,
            isGift: request.isGift || false,
            giftRecipientName: request.giftRecipientName,
            giftRecipientEmail: request.giftRecipientEmail,
            shippingCost: calculation.shippingCost,
            paymentStatus: 'PENDING',
            userId: request.userId,
          },
        });

        // Create order items
        for (const item of request.items) {
          // Get product details for snapshot
          const product = await tx.product.findUnique({
            where: { id: item.productId },
            include: {
              variants: {
                where: { id: item.variantId || undefined },
                include: {
                  size: true,
                  color: true,
                  fabric: true,
                },
              },
            },
          });

          await tx.orderItem.create({
            data: {
              quantity: item.quantity,
              price: calculation.subtotal / request.items.length, // Simplified - should calculate per item
              orderId: newOrder.id,
              productId: item.productId,
              customizationId: item.customizationId,
              productSnapshot: {
                product: product,
                variant: product?.variants[0] || null,
                pricingDetails: calculation.breakdown,
              },
            },
          });
        }

        // Create shipping info
        await tx.shippingInfo.create({
          data: {
            method: request.shippingMethod || 'standard',
            cost: calculation.shippingCost,
            zone: this.getShippingZone(request.shippingAddress.district),
            recipientName: request.shippingAddress.name,
            recipientPhone: request.shippingAddress.phone,
            address: request.shippingAddress,
            status: 'PENDING',
            estimatedDelivery: this.calculateEstimatedDelivery(request.shippingMethod || 'standard'),
            orderId: newOrder.id,
          },
        });

        return newOrder;
      });

      console.log('Order created successfully:', {
        orderId: order.id,
        orderNumber: order.orderNumber,
        userId: request.userId,
        totalAmount: order.totalAmount,
      });

      // Send order confirmation email
      await this.sendOrderConfirmationEmail(order, request);

      return { success: true, order };
    } catch (error) {
      console.error('Order creation error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create order' 
      };
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string, updates: OrderUpdateRequest, updatedBy?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const updateData: any = {};

      if (updates.status) {
        updateData.status = updates.status;
      }

      if (updates.paymentStatus) {
        updateData.paymentStatus = updates.paymentStatus;
      }

      if (updates.trackingNumber) {
        updateData.trackingNumber = updates.trackingNumber;
      }

      if (updates.estimatedDelivery) {
        updateData.estimatedDelivery = updates.estimatedDelivery;
      }

      if (updates.actualDelivery) {
        updateData.actualDelivery = updates.actualDelivery;
      }

      await prisma.order.update({
        where: { id: orderId },
        data: updateData,
      });

      // Update shipping info if tracking number provided
      if (updates.trackingNumber) {
        await prisma.shippingInfo.updateMany({
          where: { orderId },
          data: {
            trackingNumber: updates.trackingNumber,
            status: 'CONFIRMED',
            statusHistory: {
              push: {
                status: 'CONFIRMED',
                timestamp: new Date(),
                notes: 'Tracking number assigned',
                updatedBy,
              },
            },
          },
        });
      }

      console.log('Order updated successfully:', {
        orderId,
        updates,
        updatedBy,
      });

      // Send appropriate email notifications based on status changes
      await this.handleOrderStatusEmailNotifications(orderId, updates);

      return { success: true };
    } catch (error) {
      console.error('Order update error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update order' 
      };
    }
  }

  /**
   * Get order with full details
   */
  async getOrderDetails(orderId: string, userId?: string): Promise<any> {
    const where: any = { id: orderId };
    if (userId) {
      where.userId = userId;
    }

    return await prisma.order.findFirst({
      where,
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true,
                heroImage: true,
              },
            },
            customization: {
              select: {
                id: true,
                name: true,
                previewImage: true,
              },
            },
          },
        },
        payments: {
          select: {
            id: true,
            status: true,
            method: true,
            amount: true,
            bkashTransactionId: true,
            createdAt: true,
          },
        },
        shipping: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId: string, reason: string, cancelledBy?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { payments: true },
      });

      if (!order) {
        return { success: false, error: 'Order not found' };
      }

      if (!['PENDING', 'PAYMENT_PENDING', 'CONFIRMED'].includes(order.status)) {
        return { success: false, error: 'Order cannot be cancelled at this stage' };
      }

      // Update order status
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'CANCELLED',
          paymentStatus: 'CANCELLED',
        },
      });

      // Cancel any pending payments
      await prisma.payment.updateMany({
        where: {
          orderId,
          status: { in: ['PENDING', 'PROCESSING'] },
        },
        data: {
          status: 'CANCELLED',
          failureReason: `Order cancelled: ${reason}`,
        },
      });

      // Update shipping status
      await prisma.shippingInfo.updateMany({
        where: { orderId },
        data: {
          status: 'CANCELLED',
          statusHistory: {
            push: {
              status: 'CANCELLED',
              timestamp: new Date(),
              notes: `Order cancelled: ${reason}`,
              updatedBy: cancelledBy,
            },
          },
        },
      });

      console.log('Order cancelled successfully:', {
        orderId,
        reason,
        cancelledBy,
      });

      return { success: true };
    } catch (error) {
      console.error('Order cancellation error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to cancel order' 
      };
    }
  }

  // Helper methods
  private generateOrderNumber(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `OTTIQ-${timestamp}-${random}`;
  }

  private calculateShippingCost(method: string, district: string, subtotal: number): number {
    // Free shipping threshold
    if (subtotal >= 1000) {
      return 0;
    }

    const shippingRates: Record<string, Record<string, number>> = {
      standard: {
        dhaka: 60,
        chittagong: 80,
        sylhet: 100,
        rajshahi: 100,
        khulna: 100,
        barisal: 120,
        rangpur: 120,
        mymensingh: 100,
        default: 120,
      },
      express: {
        dhaka: 120,
        chittagong: 150,
        sylhet: 180,
        default: 200,
      },
    };

    const methodRates = shippingRates[method] || shippingRates.standard;
    return methodRates[district.toLowerCase()] || methodRates.default;
  }

  private getShippingZone(district: string): string {
    const zones: Record<string, string> = {
      dhaka: 'dhaka',
      chittagong: 'chittagong',
      sylhet: 'sylhet',
      rajshahi: 'rajshahi',
      khulna: 'khulna',
      barisal: 'barisal',
      rangpur: 'rangpur',
      mymensingh: 'mymensingh',
    };

    return zones[district.toLowerCase()] || 'other';
  }

  private calculateEstimatedDelivery(method: string): Date {
    const now = new Date();
    const deliveryDays = method === 'express' ? 2 : 5;
    return new Date(now.getTime() + deliveryDays * 24 * 60 * 60 * 1000);
  }

  /**
   * Handle email notifications based on order status changes
   */
  private async handleOrderStatusEmailNotifications(orderId: string, updates: any): Promise<void> {
    try {
      // Send shipped email when tracking number is added or status changes to SHIPPED
      if (updates.trackingNumber || updates.status === 'SHIPPED') {
        const order = await prisma.order.findUnique({
          where: { id: orderId },
          include: { shipping: true },
        });

        if (order?.shipping?.trackingNumber) {
          await this.sendOrderShippedEmail(orderId, order.shipping.trackingNumber);
        }
      }

      // Send delivered email when status changes to DELIVERED
      if (updates.status === 'DELIVERED') {
        await this.sendOrderDeliveredEmail(orderId);
      }

      // Send cancelled email when status changes to CANCELLED
      if (updates.status === 'CANCELLED') {
        await this.sendOrderCancelledEmail(orderId, updates.cancellationReason);
      }
    } catch (error) {
      console.error('Error handling order status email notifications:', error);
    }
  }

  /**
   * Send order confirmation email
   */
  private async sendOrderConfirmationEmail(order: any, request: CreateOrderRequest): Promise<void> {
    try {
      // Get user details
      const user = await prisma.user.findUnique({
        where: { id: request.userId },
        select: { name: true, email: true },
      });

      if (!user?.email) {
        console.warn('Cannot send order confirmation email: user email not found');
        return;
      }

      // Get order items with product details
      const orderItems = await prisma.orderItem.findMany({
        where: { orderId: order.id },
        include: {
          product: true,
          customization: {
            select: {
              name: true,
              previewImage: true,
            },
          },
        },
      });

      // Get shipping info
      const shippingInfo = await prisma.shippingInfo.findUnique({
        where: { orderId: order.id },
      });

      // Prepare email data
      const emailData = {
        to: user.email,
        customerName: user.name || 'Valued Customer',
        orderNumber: order.orderNumber,
        orderDate: order.createdAt.toLocaleDateString(),
        totalAmount: order.totalAmount.toString(),
        currency: order.currency,
        items: orderItems.map(item => ({
          name: item.product.name,
          quantity: item.quantity,
          price: item.price.toString(),
          customization: item.customization ? {
            name: item.customization.name,
            previewImage: item.customization.previewImage,
          } : undefined,
        })),
        shippingAddress: {
          name: request.shippingAddress.name,
          address: request.shippingAddress.address,
          city: request.shippingAddress.city,
          postalCode: request.shippingAddress.postalCode || '',
          country: request.shippingAddress.country || 'Bangladesh',
        },
        estimatedDelivery: shippingInfo?.estimatedDelivery?.toLocaleDateString(),
        orderUrl: `${process.env.APP_URL}/orders/${order.id}`,
      };

      // Send email
      const result = await emailService.sendEmail('order-confirmation', emailData, {
        priority: 'high',
      });

      if (result.success) {
        console.log('Order confirmation email sent successfully:', {
          orderId: order.id,
          email: user.email,
          messageId: result.messageId,
        });
      } else {
        console.error('Failed to send order confirmation email:', result.error);
      }
    } catch (error) {
      console.error('Error sending order confirmation email:', error);
    }
  }

  /**
   * Send order shipped email
   */
  async sendOrderShippedEmail(orderId: string, trackingNumber: string): Promise<void> {
    try {
      // Get order with user and items
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          user: { select: { name: true, email: true } },
          items: {
            include: {
              product: true,
              customization: {
                select: { name: true, previewImage: true },
              },
            },
          },
          shipping: true,
        },
      });

      if (!order?.user?.email) {
        console.warn('Cannot send order shipped email: order or user email not found');
        return;
      }

      // Prepare email data
      const emailData = {
        to: order.user.email,
        customerName: order.user.name || 'Valued Customer',
        orderNumber: order.orderNumber,
        orderDate: order.createdAt.toLocaleDateString(),
        totalAmount: order.totalAmount.toString(),
        currency: order.currency,
        items: order.items.map(item => ({
          name: item.product.name,
          quantity: item.quantity,
          price: item.price.toString(),
          customization: item.customization ? {
            name: item.customization.name,
            previewImage: item.customization.previewImage,
          } : undefined,
        })),
        shippingAddress: order.shippingAddress as any,
        trackingNumber,
        estimatedDelivery: order.shipping?.estimatedDelivery?.toLocaleDateString(),
        orderUrl: `${process.env.APP_URL}/orders/${order.id}`,
      };

      // Send email
      const result = await emailService.sendEmail('order-shipped', emailData, {
        priority: 'high',
      });

      if (result.success) {
        console.log('Order shipped email sent successfully:', {
          orderId: order.id,
          email: order.user.email,
          messageId: result.messageId,
        });
      } else {
        console.error('Failed to send order shipped email:', result.error);
      }
    } catch (error) {
      console.error('Error sending order shipped email:', error);
    }
  }

  /**
   * Send order delivered email
   */
  async sendOrderDeliveredEmail(orderId: string): Promise<void> {
    try {
      // Get order with user and items
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          user: { select: { name: true, email: true } },
          items: {
            include: {
              product: true,
              customization: {
                select: { name: true, previewImage: true },
              },
            },
          },
        },
      });

      if (!order?.user?.email) {
        console.warn('Cannot send order delivered email: order or user email not found');
        return;
      }

      // Prepare email data
      const emailData = {
        to: order.user.email,
        customerName: order.user.name || 'Valued Customer',
        orderNumber: order.orderNumber,
        orderDate: order.createdAt.toLocaleDateString(),
        totalAmount: order.totalAmount.toString(),
        currency: order.currency,
        items: order.items.map(item => ({
          name: item.product.name,
          quantity: item.quantity,
          price: item.price.toString(),
          customization: item.customization ? {
            name: item.customization.name,
            previewImage: item.customization.previewImage,
          } : undefined,
        })),
        shippingAddress: order.shippingAddress as any,
        orderUrl: `${process.env.APP_URL}/orders/${order.id}`,
      };

      // Send email
      const result = await emailService.sendEmail('order-delivered', emailData, {
        priority: 'normal',
      });

      if (result.success) {
        console.log('Order delivered email sent successfully:', {
          orderId: order.id,
          email: order.user.email,
          messageId: result.messageId,
        });
      } else {
        console.error('Failed to send order delivered email:', result.error);
      }
    } catch (error) {
      console.error('Error sending order delivered email:', error);
    }
  }

  /**
   * Send order cancelled email
   */
  async sendOrderCancelledEmail(orderId: string, cancellationReason?: string): Promise<void> {
    try {
      // Get order with user and items
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          user: { select: { name: true, email: true } },
          items: {
            include: {
              product: true,
              customization: {
                select: { name: true, previewImage: true },
              },
            },
          },
        },
      });

      if (!order?.user?.email) {
        console.warn('Cannot send order cancelled email: order or user email not found');
        return;
      }

      // Prepare email data
      const emailData = {
        to: order.user.email,
        customerName: order.user.name || 'Valued Customer',
        orderNumber: order.orderNumber,
        orderDate: order.createdAt.toLocaleDateString(),
        totalAmount: order.totalAmount.toString(),
        currency: order.currency,
        items: order.items.map(item => ({
          name: item.product.name,
          quantity: item.quantity,
          price: item.price.toString(),
          customization: item.customization ? {
            name: item.customization.name,
            previewImage: item.customization.previewImage,
          } : undefined,
        })),
        shippingAddress: order.shippingAddress as any,
        orderUrl: `${process.env.APP_URL}/orders/${order.id}`,
        cancellationReason,
        refundAmount: order.totalAmount.toString(), // Assuming full refund
        refundMethod: 'Original payment method',
        refundTimeframe: '5-7 business days',
      };

      // Send email
      const result = await emailService.sendEmail('order-cancelled', emailData, {
        priority: 'high',
      });

      if (result.success) {
        console.log('Order cancelled email sent successfully:', {
          orderId: order.id,
          email: order.user.email,
          messageId: result.messageId,
        });
      } else {
        console.error('Failed to send order cancelled email:', result.error);
      }
    } catch (error) {
      console.error('Error sending order cancelled email:', error);
    }
  }
}
