/**
 * Redis Client Configuration for Ottiq
 * 
 * Provides Redis connection management for caching, rate limiting,
 * and session storage with proper error handling and reconnection logic.
 */

import { createClient, RedisClientType } from 'redis';

// Redis configuration
const REDIS_CONFIG = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    connectTimeout: 10000, // 10 seconds
    lazyConnect: true,
  },
  // Retry configuration
  retry: {
    retries: 3,
    delay: (attempt: number) => Math.min(attempt * 50, 500),
  },
} as const;

/**
 * Redis Client Singleton
 */
class RedisManager {
  private client: RedisClientType | null = null;
  private isConnecting = false;
  private isConnected = false;

  /**
   * Get or create Redis client instance
   */
  async getClient(): Promise<RedisClientType> {
    if (this.client && this.isConnected) {
      return this.client;
    }

    if (this.isConnecting) {
      // Wait for existing connection attempt
      while (this.isConnecting) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      if (this.client && this.isConnected) {
        return this.client;
      }
    }

    return this.connect();
  }

  /**
   * Connect to Redis
   */
  private async connect(): Promise<RedisClientType> {
    this.isConnecting = true;

    try {
      console.log('Connecting to Redis...');
      
      this.client = createClient(REDIS_CONFIG) as RedisClientType;

      // Error handling
      this.client.on('error', (error) => {
        console.error('Redis Client Error:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis Client Connected');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('Redis Client Disconnected');
        this.isConnected = false;
      });

      this.client.on('reconnecting', () => {
        console.log('Redis Client Reconnecting...');
        this.isConnected = false;
      });

      // Connect to Redis
      await this.client.connect();
      
      this.isConnected = true;
      console.log('Redis connection established successfully');
      
      return this.client;
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      this.isConnected = false;
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Check if Redis is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const client = await this.getClient();
      await client.ping();
      return true;
    } catch (error) {
      console.error('Redis availability check failed:', error);
      return false;
    }
  }

  /**
   * Gracefully disconnect from Redis
   */
  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      try {
        await this.client.disconnect();
        console.log('Redis client disconnected gracefully');
      } catch (error) {
        console.error('Error disconnecting Redis client:', error);
      } finally {
        this.client = null;
        this.isConnected = false;
      }
    }
  }

  /**
   * Get connection status
   */
  getStatus(): { connected: boolean; connecting: boolean } {
    return {
      connected: this.isConnected,
      connecting: this.isConnecting,
    };
  }
}

// Export singleton instance
export const redisManager = new RedisManager();

/**
 * Get Redis client with error handling
 */
export async function getRedisClient(): Promise<RedisClientType | null> {
  try {
    return await redisManager.getClient();
  } catch (error) {
    console.error('Failed to get Redis client:', error);
    return null;
  }
}

/**
 * Redis utility functions
 */
export const RedisUtils = {
  /**
   * Set key with expiration
   */
  async setex(key: string, seconds: number, value: string): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;
      
      await client.setEx(key, seconds, value);
      return true;
    } catch (error) {
      console.error('Redis SETEX error:', error);
      return false;
    }
  },

  /**
   * Get key value
   */
  async get(key: string): Promise<string | null> {
    try {
      const client = await getRedisClient();
      if (!client) return null;
      
      return await client.get(key);
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  },

  /**
   * Delete key
   */
  async del(key: string): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;
      
      await client.del(key);
      return true;
    } catch (error) {
      console.error('Redis DEL error:', error);
      return false;
    }
  },

  /**
   * Increment key value
   */
  async incr(key: string): Promise<number | null> {
    try {
      const client = await getRedisClient();
      if (!client) return null;
      
      return await client.incr(key);
    } catch (error) {
      console.error('Redis INCR error:', error);
      return null;
    }
  },

  /**
   * Set key expiration
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;
      
      await client.expire(key, seconds);
      return true;
    } catch (error) {
      console.error('Redis EXPIRE error:', error);
      return false;
    }
  },

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;
      
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis EXISTS error:', error);
      return false;
    }
  },

  /**
   * Get multiple keys
   */
  async mget(keys: string[]): Promise<(string | null)[]> {
    try {
      const client = await getRedisClient();
      if (!client) return keys.map(() => null);
      
      return await client.mGet(keys);
    } catch (error) {
      console.error('Redis MGET error:', error);
      return keys.map(() => null);
    }
  },

  /**
   * Set multiple keys
   */
  async mset(keyValues: Record<string, string>): Promise<boolean> {
    try {
      const client = await getRedisClient();
      if (!client) return false;
      
      await client.mSet(keyValues);
      return true;
    } catch (error) {
      console.error('Redis MSET error:', error);
      return false;
    }
  },
};

// Graceful shutdown handling
if (typeof process !== 'undefined') {
  process.on('SIGINT', async () => {
    console.log('Shutting down Redis connection...');
    await redisManager.disconnect();
  });

  process.on('SIGTERM', async () => {
    console.log('Shutting down Redis connection...');
    await redisManager.disconnect();
  });
}
