/**
 * Order Delivered Email Template
 * Celebrates successful delivery and encourages engagement
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { OrderEmailData } from '../../lib/services/email';

interface OrderDeliveredEmailProps extends OrderEmailData {}

export function OrderDeliveredEmail({
  customerName,
  orderNumber,
  items,
  orderUrl,
}: OrderDeliveredEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`Your Ottiq order #${orderNumber} has been delivered! Time to shine in your unique style.`}>
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-delivered.jpg`}
          alt="Your order has been delivered - Time to shine!"
          width="600"
          height="300"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          ✨ Your Style Has Arrived, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          The wait is over! Your custom creation is now in your hands. It's time to step out and show the world your unique style.
        </EmailText>
      </Section>

      {/* Celebration Section */}
      <Section className="bg-gradient-to-r from-primary-50 to-warm-50 rounded-lg p-6 mb-8 text-center">
        <EmailHeading level={2}>🎉 Congratulations!</EmailHeading>
        <EmailText>
          You now own a truly one-of-a-kind piece that reflects your personality and creativity. No one else in the world has exactly what you have!
        </EmailText>
      </Section>

      {/* Order Summary */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={2}>Your Delivered Items</EmailHeading>
        
        <div className="space-y-4">
          {items.map((item, index) => (
            <div key={index} className="flex items-center space-x-4 p-4 bg-white rounded-lg">
              {item.customization?.previewImage && (
                <Img
                  src={item.customization.previewImage}
                  alt={item.name}
                  width="80"
                  height="80"
                  className="rounded-lg"
                />
              )}
              <div className="flex-1">
                <Text className="font-semibold text-gray-900 mb-1">
                  {item.name}
                </Text>
                {item.customization && (
                  <Text className="text-sm text-primary-600 mb-1">
                    Custom Design: {item.customization.name}
                  </Text>
                )}
                <Text className="text-sm text-gray-600">
                  Quantity: {item.quantity}
                </Text>
              </div>
              <div className="text-center">
                <Text className="text-green-600 font-semibold text-sm">
                  ✓ Delivered
                </Text>
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Care Instructions */}
      <Section className="mb-8">
        <EmailHeading level={2}>💎 Care for Your Custom Creation</EmailHeading>
        
        <div className="bg-blue-50 rounded-lg p-6">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Text className="text-blue-600 font-bold">🧺</Text>
              <EmailText className="mb-2">
                <strong>Washing:</strong> Turn inside out and wash in cold water with similar colors. Use gentle cycle to preserve the design.
              </EmailText>
            </div>

            <div className="flex items-start space-x-3">
              <Text className="text-blue-600 font-bold">🌡️</Text>
              <EmailText className="mb-2">
                <strong>Drying:</strong> Air dry when possible, or use low heat in the dryer. Avoid direct sunlight to prevent fading.
              </EmailText>
            </div>

            <div className="flex items-start space-x-3">
              <Text className="text-blue-600 font-bold">👕</Text>
              <EmailText className="mb-2">
                <strong>Ironing:</strong> If needed, iron inside out on low heat. Avoid ironing directly over the design.
              </EmailText>
            </div>
          </div>
        </div>
      </Section>

      {/* Share Your Style */}
      <Section className="bg-gradient-to-r from-accent-50 to-primary-50 rounded-lg p-6 mb-8 text-center">
        <EmailHeading level={2}>📸 Show Off Your Style!</EmailHeading>
        <EmailText>
          We're dying to see how you style your custom piece! Share your photos and inspire others to express their creativity.
        </EmailText>
        
        <div className="space-y-3">
          <Text className="font-semibold text-gray-800">
            Tag us and use these hashtags:
          </Text>
          <Text className="text-primary-600 font-mono text-sm">
            @ottiq #OttiqStyle #WearYourImagination #CustomFashion
          </Text>
          
          <div className="mt-4">
            <EmailButton href="https://instagram.com/ottiq" variant="primary">
              Share on Instagram
            </EmailButton>
          </div>
        </div>
      </Section>

      {/* Review Request */}
      <Section className="mb-8">
        <EmailHeading level={2}>⭐ How Was Your Experience?</EmailHeading>
        <EmailText>
          Your feedback helps us improve and helps other style creators discover Ottiq. Would you mind sharing your experience?
        </EmailText>
        
        <div className="text-center space-y-3">
          <EmailButton href={`${baseUrl}/review/${orderNumber}`} variant="outline">
            Leave a Review
          </EmailButton>
        </div>
      </Section>

      {/* Next Steps */}
      <Section className="text-center mb-8">
        <EmailHeading level={2}>What's Next?</EmailHeading>
        <EmailText>
          Ready to create your next unique piece? Or maybe gift someone special with custom fashion?
        </EmailText>
        
        <div className="space-y-3">
          <EmailButton href={`${baseUrl}/create`} variant="primary">
            Create Another Design
          </EmailButton>
          
          <br />
          
          <EmailButton href={`${baseUrl}/gift`} variant="outline">
            Gift Custom Fashion
          </EmailButton>
        </div>
      </Section>

      {/* Customer Support */}
      <Section className="bg-gray-50 rounded-lg p-6 text-center">
        <EmailHeading level={3}>Need Help?</EmailHeading>
        <EmailText>
          If you have any questions about your order or need styling advice, our team is here to help!
        </EmailText>
        
        <EmailButton href={`${baseUrl}/support`} variant="outline">
          Contact Support
        </EmailButton>
      </Section>

      {/* Order Details Link */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          Need to reference your order details?
        </EmailText>
        <EmailButton href={orderUrl} variant="outline">
          View Order #{orderNumber}
        </EmailButton>
      </Section>
    </EmailLayout>
  );
}
