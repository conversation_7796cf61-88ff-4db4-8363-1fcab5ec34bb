/**
 * Support Messages API Tests
 * Tests for support ticket message creation and retrieval
 */

import { NextRequest } from 'next/server';
import { POST, GET } from '@/app/api/support/tickets/[id]/messages/route';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';

// Mock dependencies
jest.mock('next-auth');
jest.mock('@/lib/prisma', () => ({
  prisma: {
    supportTicket: {
      findFirst: jest.fn(),
      update: jest.fn(),
    },
    supportMessage: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      updateMany: jest.fn(),
    },
  },
}));

jest.mock('@/lib/services/email', () => ({
  emailService: {
    sendEmail: jest.fn().mockResolvedValue({ success: true, messageId: 'test-id' }),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('/api/support/tickets/[id]/messages', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST - Create Message', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
    };

    const mockTicket = {
      id: 'ticket-123',
      subject: 'Test Ticket',
      status: 'OPEN',
      userId: mockUser.id,
      user: mockUser,
    };

    const validMessageData = {
      content: 'This is a test message for the support ticket.',
      messageType: 'TEXT',
    };

    it('should create a message successfully', async () => {
      // Mock authenticated user
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      // Mock ticket exists
      mockPrisma.supportTicket.findFirst.mockResolvedValue(mockTicket as any);

      // Mock message creation
      const mockMessage = {
        id: 'message-123',
        ...validMessageData,
        senderId: mockUser.id,
        senderRole: 'CUSTOMER',
        ticketId: mockTicket.id,
        sender: mockUser,
      };

      mockPrisma.supportMessage.create.mockResolvedValue(mockMessage as any);
      mockPrisma.supportTicket.update.mockResolvedValue({} as any);

      // Create request
      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages', {
        method: 'POST',
        body: JSON.stringify(validMessageData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Execute
      const response = await POST(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      // Assertions
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockMessage);
      expect(mockPrisma.supportMessage.create).toHaveBeenCalledWith({
        data: {
          content: validMessageData.content,
          messageType: validMessageData.messageType,
          senderId: mockUser.id,
          senderRole: 'CUSTOMER',
          ticketId: mockTicket.id,
          isInternal: false,
          attachments: null,
          readByCustomer: true,
          readByAdmin: false,
        },
        include: expect.any(Object),
      });
    });

    it('should require authentication', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages', {
        method: 'POST',
        body: JSON.stringify(validMessageData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });

    it('should validate ticket ownership', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      // Mock ticket not found (user doesn't own it)
      mockPrisma.supportTicket.findFirst.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages', {
        method: 'POST',
        body: JSON.stringify(validMessageData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Ticket not found');
    });

    it('should prevent messages on closed tickets', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      const closedTicket = { ...mockTicket, status: 'CLOSED' };
      mockPrisma.supportTicket.findFirst.mockResolvedValue(closedTicket as any);

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages', {
        method: 'POST',
        body: JSON.stringify(validMessageData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Cannot add messages to closed tickets');
    });

    it('should validate message content', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      const invalidData = {
        content: '', // Empty content
        messageType: 'TEXT',
      };

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Validation failed');
    });
  });

  describe('GET - List Messages', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
    };

    const mockTicket = {
      id: 'ticket-123',
      userId: mockUser.id,
    };

    it('should fetch messages successfully', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      mockPrisma.supportTicket.findFirst.mockResolvedValue(mockTicket as any);

      const mockMessages = [
        {
          id: 'message-1',
          content: 'First message',
          senderRole: 'CUSTOMER',
          sender: mockUser,
        },
        {
          id: 'message-2',
          content: 'Admin response',
          senderRole: 'ADMIN',
          sender: { id: 'admin-1', name: 'Admin' },
        },
      ];

      mockPrisma.supportMessage.findMany.mockResolvedValue(mockMessages as any);
      mockPrisma.supportMessage.count.mockResolvedValue(2);

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages');

      const response = await GET(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.messages).toEqual(mockMessages);
      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 50,
        total: 2,
        pages: 1,
      });
    });

    it('should hide internal messages from customers', async () => {
      mockGetServerSession.mockResolvedValue({
        user: mockUser,
      } as any);

      mockPrisma.supportTicket.findFirst.mockResolvedValue(mockTicket as any);
      mockPrisma.supportMessage.findMany.mockResolvedValue([]);
      mockPrisma.supportMessage.count.mockResolvedValue(0);

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages');

      await GET(request, { params: { id: 'ticket-123' } });

      expect(mockPrisma.supportMessage.findMany).toHaveBeenCalledWith({
        where: {
          ticketId: 'ticket-123',
          isInternal: false, // Should exclude internal messages
        },
        include: expect.any(Object),
        orderBy: expect.any(Object),
        skip: 0,
        take: 50,
      });
    });

    it('should require authentication', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/support/tickets/ticket-123/messages');

      const response = await GET(request, { params: { id: 'ticket-123' } });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });
  });
});
