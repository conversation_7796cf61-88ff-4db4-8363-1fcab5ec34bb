/**
 * Support Ticket Reply Email Template (Customer Notification)
 * Notifies customers when support team replies to their ticket
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { SupportTicketReplyEmailData } from '../../lib/services/email';

interface SupportTicketReplyEmailProps extends SupportTicketReplyEmailData {}

export function SupportTicketReplyEmail({
  customerName,
  ticketId,
  subject,
  message,
  senderName,
  ticketUrl,
}: SupportTicketReplyEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`${senderName} replied to your support ticket: ${subject}`}>
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-support-reply.jpg`}
          alt="Support team replied to your ticket"
          width="600"
          height="200"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          💬 We've Got an Update for You, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Our support team has responded to your ticket. We're here to help make your Ottiq experience amazing!
        </EmailText>
      </Section>

      {/* Ticket Info */}
      <Section className="bg-warm-50 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <Text className="text-sm font-medium text-gray-600">Ticket</Text>
            <Text className="text-lg font-mono text-gray-900">#{ticketId.slice(-8)}</Text>
          </div>
          <div className="text-right">
            <Text className="text-sm font-medium text-gray-600">From</Text>
            <Text className="text-lg text-warm-600 font-medium">{senderName}</Text>
          </div>
        </div>

        <div className="mb-4">
          <Text className="text-sm font-medium text-gray-600 mb-1">Subject</Text>
          <Text className="text-lg font-medium text-gray-900">{subject}</Text>
        </div>
      </Section>

      {/* Reply Message */}
      <Section className="bg-white border-l-4 border-warm-500 pl-6 py-4 mb-6">
        <EmailHeading level={3} className="text-gray-900 mb-3">
          Latest Response
        </EmailHeading>
        
        <EmailText className="text-gray-700 leading-relaxed whitespace-pre-wrap">
          {message}
        </EmailText>
      </Section>

      {/* Action Buttons */}
      <Section className="text-center mb-8">
        <EmailButton href={ticketUrl} className="mb-4">
          View Full Conversation
        </EmailButton>
        
        <EmailText className="text-gray-600 text-sm">
          Click above to view the full ticket history and continue the conversation
        </EmailText>
      </Section>

      {/* Support Promise */}
      <Section className="bg-gradient-to-r from-warm-500 to-amber-500 text-white rounded-lg p-6 mb-6">
        <EmailHeading level={3} className="text-white mb-3">
          ✨ Our Promise to You
        </EmailHeading>
        
        <EmailText className="text-white/90">
          We're committed to resolving your issue quickly and completely. Your satisfaction is our top priority, and we won't rest until you're 100% happy with your Ottiq experience.
        </EmailText>
      </Section>

      {/* Quick Actions */}
      <Section className="bg-gray-50 rounded-lg p-4 mb-6">
        <EmailHeading level={4} className="text-gray-900 mb-3">
          Need More Help?
        </EmailHeading>
        
        <div className="space-y-2">
          <EmailText className="text-gray-700 text-sm">
            📞 <strong>Urgent?</strong> Call us at +880-XXX-XXXX (9 AM - 6 PM)
          </EmailText>
          <EmailText className="text-gray-700 text-sm">
            💬 <strong>Quick question?</strong> Reply directly to this email
          </EmailText>
          <EmailText className="text-gray-700 text-sm">
            📚 <strong>Self-help?</strong> Check our <a href={`${baseUrl}/help`} className="text-warm-600 underline">Help Center</a>
          </EmailText>
        </div>
      </Section>

      <Hr className="my-6" />

      {/* Footer */}
      <Section className="text-center">
        <EmailText className="text-gray-500 text-sm">
          This email was sent regarding your support ticket #{ticketId.slice(-8)}.
          <br />
          If you didn't expect this email, please contact us immediately.
        </EmailText>
      </Section>
    </EmailLayout>
  );
}
