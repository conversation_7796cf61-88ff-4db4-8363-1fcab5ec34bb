import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { ProductHelpers } from '../utils/product-helpers';
import { TryOnHelpers } from '../utils/tryon-helpers';
import { CheckoutHelpers } from '../utils/checkout-helpers';

test.describe('Emotional Checkout Experience', () => {
  let authHelpers: AuthHelpers;
  let productHelpers: ProductHelpers;
  let tryOnHelpers: TryOnHelpers;
  let checkoutHelpers: CheckoutHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    productHelpers = new ProductHelpers(page);
    tryOnHelpers = new TryOnHelpers(page);
    checkoutHelpers = new CheckoutHelpers(page);
    await authHelpers.mockAuthentication('regular');
    
    // Create a complete design and proceed to checkout
    await productHelpers.selectCategory('hoodies');
    await productHelpers.selectProduct();
    await productHelpers.startCustomization();
    await productHelpers.addTextToDesign('My Creation');
    await productHelpers.selectColor('charcoal');
    await productHelpers.saveDesign('Checkout Test');
    await productHelpers.proceedToTryOn();
    await tryOnHelpers.uploadUserPhoto();
    await tryOnHelpers.waitForAIProcessing();
    await tryOnHelpers.proceedToCheckout();
  });

  test('should create excitement and urgency for purchase completion', async ({ page }) => {
    await test.step('Checkout introduction should build anticipation', async () => {
      // Should show exciting checkout messaging
      await expect(page.getByText(/almost yours|make it official|bring it to life|final step/i)).toBeVisible();
      
      // Should remind user of their creation
      const creationReminder = page.getByTestId('creation-reminder');
      if (await creationReminder.isVisible()) {
        await expect(page.getByText(/your unique design|custom creation|personalized/i)).toBeVisible();
      }
      
      // Should show progress indicator
      const checkoutProgress = page.getByTestId('checkout-progress');
      if (await checkoutProgress.isVisible()) {
        const progressText = await checkoutProgress.textContent();
        expect(progressText).toMatch(/step|final|almost|complete/i);
      }
    });

    await test.step('Order summary should reinforce emotional connection', async () => {
      await checkoutHelpers.verifyOrderSummary();
      
      // Should show personalized product description
      const productDescription = page.getByTestId('product-description');
      const descriptionText = await productDescription.textContent();
      expect(descriptionText).toMatch(/your creation|custom design|made just for you/i);
      
      // Should show try-on result if available
      const tryOnPreview = page.getByTestId('tryon-preview');
      if (await tryOnPreview.isVisible()) {
        await expect(page.getByText(/how it looks on you|your preview/i)).toBeVisible();
      }
      
      // Should highlight uniqueness
      const uniquenessNote = page.getByTestId('uniqueness-note');
      if (await uniquenessNote.isVisible()) {
        const uniqueText = await uniquenessNote.textContent();
        expect(uniqueText).toMatch(/one of a kind|unique|exclusively yours/i);
      }
    });

    await test.step('Shipping options should enhance excitement', async () => {
      await checkoutHelpers.selectShippingMethod('express');
      
      // Express shipping should have excitement-building copy
      const expressOption = page.getByTestId('shipping-express');
      const expressText = await expressOption.textContent();
      expect(expressText).toMatch(/faster|sooner|can't wait|get it quicker/i);
      
      // Should show delivery timeline with anticipation
      const deliveryTimeline = page.getByTestId('delivery-timeline');
      if (await deliveryTimeline.isVisible()) {
        const timelineText = await deliveryTimeline.textContent();
        expect(timelineText).toMatch(/arrives|delivered|in your hands/i);
      }
    });
  });

  test('should provide secure and confident payment experience', async ({ page }) => {
    await test.step('Payment form should feel secure and trustworthy', async () => {
      await checkoutHelpers.testPaymentSecurity();
      
      // Should show security indicators
      const securityBadges = page.getByTestId('security-badges');
      if (await securityBadges.isVisible()) {
        await expect(page.getByText(/secure|encrypted|protected|safe/i)).toBeVisible();
      }
      
      // Should have trust signals
      const trustSignals = page.getByTestId('trust-signals');
      if (await trustSignals.isVisible()) {
        await expect(page.getByText(/money back guarantee|secure checkout|trusted/i)).toBeVisible();
      }
    });

    await test.step('Payment completion should build final excitement', async () => {
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.fillPaymentInfo();
      
      // Final purchase button should be emotionally compelling
      const purchaseButton = page.getByRole('button', { 
        name: /complete order|make it mine|bring it to life|create my style/i 
      });
      await expect(purchaseButton).toBeVisible();
      
      // Button should show final price with confidence
      const buttonText = await purchaseButton.textContent();
      expect(buttonText).toMatch(/complete|make it mine|bring to life/i);
      
      // Should show what happens next
      const nextStepsPreview = page.getByTestId('next-steps-preview');
      if (await nextStepsPreview.isVisible()) {
        await expect(page.getByText(/we'll start creating|production begins|coming to life/i)).toBeVisible();
      }
    });

    await test.step('Order processing should maintain engagement', async () => {
      await checkoutHelpers.completePurchase();
      
      // Should show engaging processing messages
      const processingMessages = [
        /processing your order/i,
        /creating your design/i,
        /preparing your item/i,
        /almost complete/i
      ];
      
      let messageFound = false;
      for (const messagePattern of processingMessages) {
        const message = page.getByText(messagePattern);
        if (await message.isVisible()) {
          messageFound = true;
          break;
        }
      }
      expect(messageFound).toBeTruthy();
    });
  });

  test('should celebrate successful order with emotional validation', async ({ page }) => {
    await test.step('Order confirmation should be celebratory', async () => {
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      await checkoutHelpers.verifyOrderConfirmation();
      
      // Should show celebration messaging
      await expect(page.getByText(/congratulations|amazing|your creation is coming to life/i)).toBeVisible();
      
      // Should show order details with excitement
      const orderDetails = page.getByTestId('order-details');
      await expect(orderDetails).toBeVisible();
      
      // Should show what's happening next
      const productionInfo = page.getByTestId('production-info');
      if (await productionInfo.isVisible()) {
        const productionText = await productionInfo.textContent();
        expect(productionText).toMatch(/creating|crafting|bringing to life|making/i);
      }
    });

    await test.step('Post-purchase engagement should build community', async () => {
      // Should encourage sharing
      const shareSuccess = page.getByTestId('share-success');
      if (await shareSuccess.isVisible()) {
        await expect(page.getByText(/share your creation|tell your friends|show off/i)).toBeVisible();
      }
      
      // Should show tracking information
      const trackingInfo = page.getByTestId('tracking-info');
      if (await trackingInfo.isVisible()) {
        await expect(page.getByText(/track your order|follow progress|updates/i)).toBeVisible();
      }
      
      // Should suggest next actions
      const nextActions = page.getByTestId('next-actions');
      if (await nextActions.isVisible()) {
        await expect(page.getByText(/create another|design more|explore/i)).toBeVisible();
      }
    });

    await test.step('Email confirmation should extend the experience', async () => {
      // Should show email confirmation notice
      const emailNotice = page.getByTestId('email-confirmation');
      if (await emailNotice.isVisible()) {
        const emailText = await emailNotice.textContent();
        expect(emailText).toMatch(/confirmation email|check your inbox|sent to/i);
      }
      
      // Should mention what's in the email
      const emailContents = page.getByTestId('email-contents');
      if (await emailContents.isVisible()) {
        await expect(page.getByText(/order details|tracking|updates/i)).toBeVisible();
      }
    });
  });

  test('should handle checkout errors with supportive recovery', async ({ page }) => {
    await test.step('Payment errors should be handled gracefully', async () => {
      await checkoutHelpers.fillShippingInfo();
      
      // Simulate payment error
      await page.route('**/api/payment/**', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ 
            error: 'payment_declined',
            message: 'Your card was declined'
          })
        });
      });
      
      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      
      // Should show supportive error message
      const errorMessage = page.getByTestId('payment-error');
      await expect(errorMessage).toBeVisible();
      
      const errorText = await errorMessage.textContent();
      expect(errorText).toMatch(/payment issue|try different card|we're here to help/i);
      expect(errorText).not.toMatch(/failed|error|wrong|invalid/i);
      
      // Should offer solutions
      const errorSolutions = page.getByTestId('error-solutions');
      if (await errorSolutions.isVisible()) {
        await expect(page.getByText(/try another card|contact support|different payment/i)).toBeVisible();
      }
    });

    await test.step('Form validation should be helpful', async () => {
      // Try to submit with missing information
      const purchaseButton = page.getByRole('button', { name: /complete order/i });
      await purchaseButton.click();
      
      // Should show helpful validation messages
      const validationErrors = page.getByRole('alert');
      const errorCount = await validationErrors.count();
      
      if (errorCount > 0) {
        for (let i = 0; i < errorCount; i++) {
          const error = validationErrors.nth(i);
          const errorText = await error.textContent();
          expect(errorText).toMatch(/please|required|needed|missing/i);
          expect(errorText).not.toMatch(/error|invalid|wrong/i);
        }
      }
    });

    await test.step('Inventory issues should be handled emotionally', async () => {
      // Simulate out of stock
      await page.route('**/api/inventory/**', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ 
            error: 'out_of_stock',
            message: 'This item is currently out of stock'
          })
        });
      });
      
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      
      // Should show understanding message
      const stockError = page.getByTestId('stock-error');
      if (await stockError.isVisible()) {
        const stockText = await stockError.textContent();
        expect(stockText).toMatch(/popular item|high demand|back in stock soon/i);
        
        // Should offer alternatives
        const alternatives = page.getByTestId('stock-alternatives');
        if (await alternatives.isVisible()) {
          await expect(page.getByText(/similar items|notify when available|different color/i)).toBeVisible();
        }
      }
    });
  });

  test('should optimize checkout for mobile experience', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await test.step('Mobile checkout should be streamlined', async () => {
      await checkoutHelpers.testMobileCheckoutExperience();
      
      // Form should be mobile-optimized
      const checkoutForm = page.getByTestId('checkout-form');
      await expect(checkoutForm).toBeVisible();
      
      // Should use appropriate input types for mobile
      const emailField = page.getByTestId('shipping-email');
      const emailType = await emailField.getAttribute('type');
      expect(emailType).toBe('email');
      
      const phoneField = page.getByTestId('shipping-phone');
      if (await phoneField.isVisible()) {
        const phoneType = await phoneField.getAttribute('type');
        expect(phoneType).toBe('tel');
      }
    });

    await test.step('Mobile payment should be touch-optimized', async () => {
      // Payment buttons should be large enough
      const paymentButtons = page.getByRole('button');
      const buttonCount = await paymentButtons.count();
      
      for (let i = 0; i < Math.min(buttonCount, 5); i++) {
        const button = paymentButtons.nth(i);
        const buttonBox = await button.boundingBox();
        if (buttonBox) {
          expect(buttonBox.height).toBeGreaterThanOrEqual(44);
        }
      }
      
      // Should support mobile payment methods
      const mobilePayment = page.getByTestId('mobile-payment');
      if (await mobilePayment.isVisible()) {
        await expect(page.getByText(/apple pay|google pay|mobile wallet/i)).toBeVisible();
      }
    });

    await test.step('Mobile confirmation should be engaging', async () => {
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      await checkoutHelpers.verifyOrderConfirmation();
      
      // Should show mobile-optimized success screen
      const mobileSuccess = page.getByTestId('mobile-success');
      if (await mobileSuccess.isVisible()) {
        // Should have large, celebratory elements
        const successIcon = mobileSuccess.getByTestId('success-icon');
        if (await successIcon.isVisible()) {
          const iconBox = await successIcon.boundingBox();
          if (iconBox) {
            expect(iconBox.height).toBeGreaterThanOrEqual(60);
          }
        }
      }
    });
  });

  test('should maintain accessibility throughout checkout', async ({ page }) => {
    await test.step('Checkout form should be screen reader friendly', async () => {
      await checkoutHelpers.testCheckoutAccessibility();
      
      // Should have proper form structure
      const fieldsets = page.getByRole('group');
      const fieldsetCount = await fieldsets.count();
      
      if (fieldsetCount > 0) {
        for (let i = 0; i < fieldsetCount; i++) {
          const fieldset = fieldsets.nth(i);
          const legend = fieldset.locator('legend');
          if (await legend.isVisible()) {
            const legendText = await legend.textContent();
            expect(legendText?.trim()).toBeTruthy();
          }
        }
      }
    });

    await test.step('Error messages should be accessible', async () => {
      // Trigger validation errors
      const purchaseButton = page.getByRole('button', { name: /complete order/i });
      await purchaseButton.click();
      
      // Error messages should be properly associated
      const errorMessages = page.getByRole('alert');
      const errorCount = await errorMessages.count();
      
      for (let i = 0; i < errorCount; i++) {
        const error = errorMessages.nth(i);
        const errorId = await error.getAttribute('id');
        
        if (errorId) {
          // Should be associated with form field
          const associatedField = page.locator(`[aria-describedby*="${errorId}"]`);
          await expect(associatedField).toBeVisible();
        }
      }
    });

    await test.step('Success confirmation should be announced', async () => {
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      
      // Success should be announced to screen readers
      const successAnnouncement = page.getByRole('status');
      if (await successAnnouncement.isVisible()) {
        const announcementText = await successAnnouncement.textContent();
        expect(announcementText).toMatch(/order complete|success|confirmed/i);
      }
    });
  });
});
