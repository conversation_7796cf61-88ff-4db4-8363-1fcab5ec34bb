import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { ProductHelpers } from '../utils/product-helpers';
import { TryOnHelpers } from '../utils/tryon-helpers';
import { CheckoutHelpers } from '../utils/checkout-helpers';

test.describe('Complete Emotional Journey: Select → Customize → Try-On → Checkout', () => {
  let authHelpers: AuthHelpers;
  let productHelpers: ProductHelpers;
  let tryOnHelpers: TryOnHelpers;
  let checkoutHelpers: CheckoutHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    productHelpers = new ProductHelpers(page);
    tryOnHelpers = new TryOnHelpers(page);
    checkoutHelpers = new CheckoutHelpers(page);

    // Start with authentication
    await authHelpers.mockAuthentication('regular');
  });

  test('should complete full emotional journey on mobile', async ({ page }) => {
    // Set mobile viewport for mobile-first experience
    await page.setViewportSize({ width: 375, height: 667 });

    // Step 1: Product Selection with Emotional Engagement
    await test.step('Select product category with emotional validation', async () => {
      await productHelpers.selectCategory('hoodies');
      await productHelpers.verifyEmotionalElements();
    });

    await test.step('Select specific product', async () => {
      await productHelpers.selectProduct();
      
      // Verify emotional product messaging
      await expect(page.getByText(/express yourself|make it yours|wear your imagination/i)).toBeVisible();
    });

    // Step 2: Customization with Creative Expression
    await test.step('Start customization process', async () => {
      await productHelpers.startCustomization();
      
      // Verify inspirational design interface
      await expect(page.getByText(/create something unique|design your style/i)).toBeVisible();
    });

    await test.step('Customize design with personal touches', async () => {
      // Add personal text
      await productHelpers.addTextToDesign('My Vibe');
      
      // Change color to express personality
      await productHelpers.selectColor('navy');
      
      // Verify emotional feedback
      await expect(page.getByText(/looking great|perfect choice|amazing/i)).toBeVisible();
    });

    await test.step('Save design with emotional validation', async () => {
      await productHelpers.saveDesign('My Personal Style');
      
      // Verify save confirmation with emotional messaging
      await expect(page.getByText(/your creation is saved|design preserved|ready for the next step/i)).toBeVisible();
    });

    // Step 3: AI Try-On Experience
    await test.step('Proceed to try-on experience', async () => {
      await productHelpers.proceedToTryOn();
      
      // Verify try-on interface with excitement-building copy
      await expect(page.getByText(/see how it looks|bring your vision to life/i)).toBeVisible();
    });

    await test.step('Upload photo and experience AI magic', async () => {
      await tryOnHelpers.uploadUserPhoto();
      await tryOnHelpers.waitForAIProcessing();
      
      // Verify emotional engagement during processing
      await expect(page.getByText(/creating your look|almost ready|this is going to be amazing/i)).toBeVisible();
    });

    await test.step('Verify and interact with try-on result', async () => {
      await tryOnHelpers.verifyTryOnResult();
      await tryOnHelpers.verifyEmotionalEngagement();
      
      // Test different angles if available
      await tryOnHelpers.testDifferentAngles();
      
      // Save the look
      await tryOnHelpers.saveToProfile();
    });

    // Step 4: Emotional Checkout Experience
    await test.step('Proceed to checkout with excitement', async () => {
      await tryOnHelpers.proceedToCheckout();
      
      // Verify emotional checkout messaging
      await expect(page.getByText(/make it yours|bring it to life|almost there/i)).toBeVisible();
    });

    await test.step('Complete order with emotional validation', async () => {
      await checkoutHelpers.verifyOrderSummary();
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.selectShippingMethod('express');
      await checkoutHelpers.fillPaymentInfo();
      
      // Verify final emotional messaging before purchase
      const purchaseButton = page.getByRole('button', { name: /make it mine|create my style/i });
      await expect(purchaseButton).toBeVisible();
      
      // Complete the purchase
      await checkoutHelpers.completePurchase();
    });

    await test.step('Verify emotional order confirmation', async () => {
      await checkoutHelpers.verifyOrderConfirmation();
      
      // Verify celebration and next steps
      await expect(page.getByText(/congratulations|your creation is coming to life/i)).toBeVisible();
      await expect(page.getByText(/share your style|tell your friends/i)).toBeVisible();
    });
  });

  test('should handle emotional journey on desktop with enhanced features', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 });

    // Complete journey with desktop-specific features
    await test.step('Enhanced product browsing on desktop', async () => {
      await productHelpers.selectCategory('t-shirts');
      
      // Verify desktop-specific features like hover effects
      const productCards = page.getByTestId(/product-card/);
      await productCards.first().hover();
      
      // Should show enhanced preview or animation
      await page.waitForTimeout(500);
      
      await productHelpers.selectProduct();
    });

    await test.step('Advanced customization features on desktop', async () => {
      await productHelpers.startCustomization();
      
      // Test desktop-specific design tools
      await productHelpers.addTextToDesign('Desktop Design');
      
      // Test image upload if available
      try {
        await productHelpers.uploadCustomImage();
      } catch (error) {
        // Image upload might not be available in test environment
        console.log('Image upload not available in test environment');
      }
      
      await productHelpers.saveDesign('Desktop Creation');
    });

    await test.step('Enhanced try-on experience on desktop', async () => {
      await productHelpers.proceedToTryOn();
      await tryOnHelpers.uploadUserPhoto();
      await tryOnHelpers.waitForAIProcessing();
      await tryOnHelpers.verifyTryOnResult();
      
      // Test desktop-specific features like sharing
      await tryOnHelpers.shareResult('social');
    });

    await test.step('Complete desktop checkout', async () => {
      await tryOnHelpers.proceedToCheckout();
      await checkoutHelpers.verifyOrderSummary();
      await checkoutHelpers.fillShippingInfo();
      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      await checkoutHelpers.verifyOrderConfirmation();
    });
  });

  test('should maintain emotional engagement throughout errors and recovery', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await test.step('Handle product selection errors gracefully', async () => {
      // Simulate network error during product loading
      await page.route('**/api/products/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Service temporarily unavailable' })
        });
      });

      await productHelpers.goToProductSelection();
      
      // Should show encouraging error message
      await expect(page.getByText(/temporarily unavailable|try again|we're working on it/i)).toBeVisible();
      
      // Clear the route to allow recovery
      await page.unroute('**/api/products/**');
      
      // Retry should work
      await page.reload();
      await productHelpers.selectCategory('hoodies');
    });

    await test.step('Handle AI try-on failures with emotional fallback', async () => {
      await productHelpers.selectProduct();
      await productHelpers.startCustomization();
      await productHelpers.addTextToDesign('Test Design');
      await productHelpers.saveDesign('Test');
      await productHelpers.proceedToTryOn();
      
      // Test fallback experience
      await tryOnHelpers.testFallbackExperience();
      
      // Should still allow proceeding to checkout
      await expect(page.getByRole('button', { name: /continue|proceed|checkout/i })).toBeVisible();
    });

    await test.step('Handle payment errors with supportive messaging', async () => {
      await page.goto('/checkout');
      await checkoutHelpers.fillShippingInfo();
      
      // Simulate payment error
      await page.route('**/api/payment/**', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Payment declined' })
        });
      });

      await checkoutHelpers.fillPaymentInfo();
      await checkoutHelpers.completePurchase();
      
      // Should show supportive error message
      await expect(page.getByText(/payment issue|try different card|we're here to help/i)).toBeVisible();
    });
  });

  test('should provide accessibility throughout emotional journey', async ({ page }) => {
    await test.step('Verify accessibility in product selection', async () => {
      await productHelpers.selectCategory('accessories');
      
      // Check for proper heading hierarchy
      const headings = page.getByRole('heading');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
      
      // Check for proper image alt text
      const images = page.getByRole('img');
      const imageCount = await images.count();
      for (let i = 0; i < Math.min(imageCount, 5); i++) {
        const image = images.nth(i);
        const altText = await image.getAttribute('alt');
        expect(altText).toBeTruthy();
      }
    });

    await test.step('Verify accessibility in customization', async () => {
      await productHelpers.selectProduct();
      await productHelpers.startCustomization();
      
      // Check for proper button labels
      const buttons = page.getByRole('button');
      const buttonCount = await buttons.count();
      for (let i = 0; i < Math.min(buttonCount, 10); i++) {
        const button = buttons.nth(i);
        const accessibleName = await button.getAttribute('aria-label') || await button.textContent();
        expect(accessibleName?.trim()).toBeTruthy();
      }
    });

    await test.step('Verify accessibility in try-on', async () => {
      await productHelpers.addTextToDesign('Accessible Design');
      await productHelpers.saveDesign('Accessible');
      await productHelpers.proceedToTryOn();
      
      await tryOnHelpers.testTryOnAccessibility();
    });

    await test.step('Verify accessibility in checkout', async () => {
      await page.goto('/checkout');
      await checkoutHelpers.testCheckoutAccessibility();
    });
  });
});
