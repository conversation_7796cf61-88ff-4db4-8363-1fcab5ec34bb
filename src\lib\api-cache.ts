/**
 * API Caching Middleware for Ottiq
 * 
 * Provides intelligent caching for heavy API endpoints to ensure instant
 * visual feedback and optimal performance for emotional user experience.
 * Focuses on pricing calculations, template loading, and AI try-on operations.
 */

import { NextRequest, NextResponse } from 'next/server';
import { CacheManager, generate<PERSON><PERSON><PERSON><PERSON>, CACHE_CONFIG } from '@/lib/cache';
import { CacheMetrics } from '@/lib/cache';

/**
 * Cache middleware for API routes
 */
export function withApiCache(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: {
    keyGenerator: (request: NextRequest, ...args: any[]) => string;
    ttl: number;
    skipCache?: (request: NextRequest, ...args: any[]) => boolean;
    invalidateOn?: string[]; // HTTP methods that should invalidate cache
    tags?: string[];
  }
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    const startTime = Date.now();
    const method = request.method;
    
    // Skip cache for non-GET requests unless explicitly configured
    if (method !== 'GET' && !options.skipCache) {
      return await handler(request, ...args);
    }
    
    // Check if we should skip cache
    if (options.skipCache && options.skipCache(request, ...args)) {
      return await handler(request, ...args);
    }
    
    const cacheKey = options.keyGenerator(request, ...args);
    
    // Try to get from cache
    try {
      const cached = await CacheManager.get<any>(cacheKey);
      if (cached) {
        const responseTime = Date.now() - startTime;
        CacheMetrics.recordHit(responseTime);
        
        // Add cache headers
        const response = NextResponse.json(cached.data);
        response.headers.set('X-Cache', 'HIT');
        response.headers.set('X-Cache-Age', cached.age.toString());
        response.headers.set('X-Response-Time', `${responseTime}ms`);
        
        return response;
      }
    } catch (error) {
      console.warn('Cache retrieval failed:', error);
      CacheMetrics.recordError();
    }
    
    // Execute handler and cache result
    const response = await handler(request, ...args);
    const responseTime = Date.now() - startTime;
    CacheMetrics.recordMiss(responseTime);
    
    // Cache successful responses
    if (response.ok) {
      try {
        const responseData = await response.clone().json();
        await CacheManager.set(cacheKey, responseData, options.ttl, {
          tags: options.tags,
        });
        
        // Add cache headers
        response.headers.set('X-Cache', 'MISS');
        response.headers.set('X-Response-Time', `${responseTime}ms`);
      } catch (error) {
        console.warn('Cache storage failed:', error);
        CacheMetrics.recordError();
      }
    }
    
    return response;
  };
}

/**
 * Cached pricing calculation wrapper
 */
export const cachedPricingHandler = withApiCache(
  async (request: NextRequest) => {
    // This would be the actual pricing handler
    // For now, we'll import and call the existing handler
    const { POST } = await import('@/app/api/pricing/compute/route');
    return await POST(request);
  },
  {
    keyGenerator: (request: NextRequest) => {
      const url = new URL(request.url);
      const body = request.body; // Note: In real implementation, you'd need to read body carefully
      return generateCacheKey('PRICING', 'compute', {
        query: Object.fromEntries(url.searchParams),
        // body: body, // Be careful with body caching
      });
    },
    ttl: CACHE_CONFIG.TTL.PRICING_CALCULATIONS,
    tags: ['pricing'],
  }
);

/**
 * Cached template loading wrapper
 */
export const cachedTemplateHandler = withApiCache(
  async (request: NextRequest) => {
    const { GET } = await import('@/app/api/templates/route');
    return await GET(request);
  },
  {
    keyGenerator: (request: NextRequest) => {
      const url = new URL(request.url);
      return generateCacheKey('TEMPLATE', 'list', {
        query: Object.fromEntries(url.searchParams),
      });
    },
    ttl: CACHE_CONFIG.TTL.TEMPLATES,
    tags: ['templates'],
  }
);

/**
 * Cached AI try-on stats wrapper
 */
export const cachedAiTryOnStatsHandler = withApiCache(
  async (request: NextRequest) => {
    const { GET } = await import('@/app/api/admin/ai-tryon/stats/route');
    return await GET(request);
  },
  {
    keyGenerator: (request: NextRequest) => {
      const url = new URL(request.url);
      return generateCacheKey('STATS', 'ai-tryon', {
        query: Object.fromEntries(url.searchParams),
      });
    },
    ttl: CACHE_CONFIG.TTL.AI_TRYON_STATS,
    tags: ['ai-tryon', 'stats'],
  }
);

/**
 * Image processing cache wrapper
 */
export async function cachedImageProcessing<T>(
  operation: string,
  params: Record<string, any>,
  processor: () => Promise<T>,
  ttl: number = CACHE_CONFIG.TTL.PREVIEW_IMAGES
): Promise<T> {
  const cacheKey = generateCacheKey('IMAGE', operation, params);
  
  // Try cache first
  const cached = await CacheManager.get<T>(cacheKey);
  if (cached) {
    console.log(`Image cache hit for ${operation}`);
    return cached.data;
  }
  
  // Process and cache
  const startTime = Date.now();
  const result = await processor();
  const processingTime = Date.now() - startTime;
  
  console.log(`Image processing ${operation} completed in ${processingTime}ms`);
  
  // Cache the result
  await CacheManager.set(cacheKey, result, ttl, {
    tags: ['images'],
  });
  
  return result;
}

/**
 * Batch cache operations for multiple API calls
 */
export class BatchCacheManager {
  private operations: Array<{
    key: string;
    operation: () => Promise<any>;
    ttl: number;
    tags?: string[];
  }> = [];
  
  /**
   * Add operation to batch
   */
  add<T>(
    key: string,
    operation: () => Promise<T>,
    ttl: number,
    tags?: string[]
  ): this {
    this.operations.push({ key, operation, ttl, tags });
    return this;
  }
  
  /**
   * Execute all operations with intelligent caching
   */
  async execute(): Promise<any[]> {
    const keys = this.operations.map(op => op.key);
    
    // Try to get all from cache first
    const cachedResults = await CacheManager.getMultiple(keys);
    
    // Identify which operations need to be executed
    const toExecute: Array<{
      index: number;
      operation: () => Promise<any>;
      key: string;
      ttl: number;
      tags?: string[];
    }> = [];
    
    cachedResults.forEach((cached, index) => {
      if (cached === null) {
        toExecute.push({
          index,
          ...this.operations[index],
        });
      }
    });
    
    // Execute missing operations in parallel
    const executionPromises = toExecute.map(async (op) => {
      const result = await op.operation();
      return { index: op.index, result, key: op.key, ttl: op.ttl, tags: op.tags };
    });
    
    const executionResults = await Promise.all(executionPromises);
    
    // Cache new results
    const cacheEntries = executionResults.map(result => ({
      key: result.key,
      data: result.result,
      ttl: result.ttl,
    }));
    
    if (cacheEntries.length > 0) {
      await CacheManager.setMultiple(cacheEntries);
    }
    
    // Merge cached and new results
    const finalResults = [...cachedResults];
    executionResults.forEach(result => {
      finalResults[result.index] = result.result;
    });
    
    // Clear operations for next batch
    this.operations = [];
    
    return finalResults;
  }
}

/**
 * Smart cache warming for critical paths
 */
export class CacheWarmer {
  private static warmingInProgress = new Set<string>();
  
  /**
   * Warm cache for pricing calculations
   */
  static async warmPricingCache(commonRequests: any[]) {
    const warmKey = 'pricing-warm';
    if (this.warmingInProgress.has(warmKey)) return;
    
    this.warmingInProgress.add(warmKey);
    
    try {
      console.log('Warming pricing cache...');
      
      const batch = new BatchCacheManager();
      
      commonRequests.forEach((request, index) => {
        const cacheKey = generateCacheKey('PRICING', 'compute', request);
        batch.add(
          cacheKey,
          async () => {
            // Simulate pricing calculation
            return { price: 100 + index, request };
          },
          CACHE_CONFIG.TTL.PRICING_CALCULATIONS,
          ['pricing', 'warm']
        );
      });
      
      await batch.execute();
      console.log('Pricing cache warmed successfully');
    } catch (error) {
      console.error('Failed to warm pricing cache:', error);
    } finally {
      this.warmingInProgress.delete(warmKey);
    }
  }
  
  /**
   * Warm cache for popular templates
   */
  static async warmTemplateCache() {
    const warmKey = 'template-warm';
    if (this.warmingInProgress.has(warmKey)) return;
    
    this.warmingInProgress.add(warmKey);
    
    try {
      console.log('Warming template cache...');
      
      // Common template queries
      const commonQueries = [
        { featured: 'true', limit: '12' },
        { category: 'streetwear', limit: '8' },
        { moodTag: 'bold', limit: '8' },
        { sortBy: 'popular', limit: '16' },
      ];
      
      const batch = new BatchCacheManager();
      
      commonQueries.forEach(query => {
        const cacheKey = generateCacheKey('TEMPLATE', 'list', { query });
        batch.add(
          cacheKey,
          async () => {
            // In real implementation, this would call the actual template API
            return { templates: [], pagination: {}, filters: {} };
          },
          CACHE_CONFIG.TTL.TEMPLATES,
          ['templates', 'warm']
        );
      });
      
      await batch.execute();
      console.log('Template cache warmed successfully');
    } catch (error) {
      console.error('Failed to warm template cache:', error);
    } finally {
      this.warmingInProgress.delete(warmKey);
    }
  }
  
  /**
   * Warm all critical caches
   */
  static async warmAllCaches() {
    console.log('Starting cache warming process...');
    
    await Promise.all([
      this.warmPricingCache([
        { productId: 'common-1', quantity: 1 },
        { productId: 'common-2', quantity: 1 },
        { productId: 'common-3', quantity: 1 },
      ]),
      this.warmTemplateCache(),
    ]);
    
    console.log('Cache warming completed');
  }
}
