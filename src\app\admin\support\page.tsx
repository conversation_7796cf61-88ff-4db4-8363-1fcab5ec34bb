'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { AdminSupportTicketList } from '@/components/admin/AdminSupportTicketList';
import { AdminSupportStats } from '@/components/admin/AdminSupportStats';

type TabType = 'overview' | 'tickets' | 'analytics';

interface SupportStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  closedTickets: number;
  averageResponseTime: string;
  recentActivity: number;
  byStatus: Record<string, number>;
  byPriority: Record<string, number>;
  byCategory: Record<string, number>;
}

export default function AdminSupportPage() {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<SupportStats | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    fetchStats();
  }, [refreshTrigger]);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/support/stats');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching support stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'tickets', label: 'All Tickets', icon: '🎫' },
    { id: 'analytics', label: 'Analytics', icon: '📈' },
  ];

  if (loading && !stats) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-warm-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading support dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          {/* Header */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8">
            <div>
              <motion.h1 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-3xl font-bold text-gray-900 mb-2"
              >
                💬 Support Dashboard
              </motion.h1>
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-gray-600"
              >
                Manage customer support tickets and maintain high satisfaction
              </motion.p>
            </div>
            
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={loading}
              >
                {loading ? '🔄' : '↻'} Refresh
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          {stats && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8"
            >
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalTickets}</div>
                  <div className="text-sm text-gray-600">Total Tickets</div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-orange-600">{stats.openTickets}</div>
                  <div className="text-sm text-gray-600">Open</div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{stats.resolvedTickets}</div>
                  <div className="text-sm text-gray-600">Resolved</div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-gray-600">{stats.closedTickets}</div>
                  <div className="text-sm text-gray-600">Closed</div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{stats.averageResponseTime}</div>
                  <div className="text-sm text-gray-600">Avg Response</div>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-warm-600">{stats.recentActivity}</div>
                  <div className="text-sm text-gray-600">Last 30 Days</div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Tab Navigation */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex flex-wrap gap-2 mb-8 bg-white rounded-lg p-2 shadow-sm"
          >
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-warm-500 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </motion.div>

          {/* Tab Content */}
          <div className="space-y-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {activeTab === 'overview' && (
                  <div className="space-y-6">
                    {/* Priority Tickets */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          🚨 High Priority Tickets
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <AdminSupportTicketList
                          filters={{ priority: 'HIGH,URGENT', status: 'OPEN,IN_PROGRESS,WAITING_FOR_ADMIN' }}
                          limit={5}
                          showFilters={false}
                          refreshTrigger={refreshTrigger}
                          onRefresh={handleRefresh}
                        />
                      </CardContent>
                    </Card>

                    {/* Recent Activity */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          ⏰ Recent Activity
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <AdminSupportTicketList
                          filters={{}}
                          limit={10}
                          showFilters={false}
                          refreshTrigger={refreshTrigger}
                          onRefresh={handleRefresh}
                        />
                      </CardContent>
                    </Card>
                  </div>
                )}
                
                {activeTab === 'tickets' && (
                  <AdminSupportTicketList
                    filters={{}}
                    limit={20}
                    showFilters={true}
                    refreshTrigger={refreshTrigger}
                    onRefresh={handleRefresh}
                  />
                )}
                
                {activeTab === 'analytics' && stats && (
                  <AdminSupportStats stats={stats} />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </Container>
      </Section>
    </div>
  );
}
