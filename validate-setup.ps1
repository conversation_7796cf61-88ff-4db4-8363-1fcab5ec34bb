# Simple validation script for Docker deployment setup

Write-Host "Ottiq Docker Deployment Validation" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

$files = @(
    "Dockerfile",
    "docker-compose.prod.yml",
    ".dockerignore",
    "healthcheck.js",
    ".env.docker",
    "nginx/nginx.conf",
    "nginx/conf.d/default.conf",
    "deploy.ps1",
    "deploy.sh",
    "DEPLOYMENT.md"
)

$allGood = $true

Write-Host "Checking required files:" -ForegroundColor Blue
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  [OK] $file" -ForegroundColor Green
    } else {
        Write-Host "  [MISSING] $file" -ForegroundColor Red
        $allGood = $false
    }
}

Write-Host ""
Write-Host "Checking project structure:" -ForegroundColor Blue

$projectFiles = @(
    "package.json",
    "next.config.js",
    "prisma/schema.prisma",
    "src/app/api/health/route.ts"
)

foreach ($file in $projectFiles) {
    if (Test-Path $file) {
        Write-Host "  [OK] $file" -ForegroundColor Green
    } else {
        Write-Host "  [MISSING] $file" -ForegroundColor Red
        $allGood = $false
    }
}

Write-Host ""

if ($allGood) {
    Write-Host "[SUCCESS] All files are present!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Start Docker Desktop"
    Write-Host "2. Copy .env.docker to .env.production and configure"
    Write-Host "3. Run: .\deploy.ps1"
} else {
    Write-Host "[ERROR] Some files are missing!" -ForegroundColor Red
}

Write-Host ""
