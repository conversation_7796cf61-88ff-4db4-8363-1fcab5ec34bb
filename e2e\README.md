# Ottiq E2E Testing Suite

This directory contains end-to-end tests for the Ottiq platform, focusing on emotional user flows and mobile-first experiences.

## Test Structure

### Emotional Flows (`/emotional-flows/`)
Tests that validate the complete emotional journey users experience:

- **`complete-journey.spec.ts`** - Full user journey from selection to checkout
- **`product-selection.spec.ts`** - Emotional product discovery and selection
- **`customization.spec.ts`** - Creative design and customization experience  
- **`tryon-experience.spec.ts`** - AI try-on with emotional engagement
- **`checkout-experience.spec.ts`** - Purchase completion with celebration

### Utilities (`/utils/`)
Reusable helper classes for common test actions:

- **`auth-helpers.ts`** - Authentication and user management
- **`product-helpers.ts`** - Product selection and customization
- **`tryon-helpers.ts`** - AI try-on experience testing
- **`checkout-helpers.ts`** - Purchase flow testing

### Core Tests
- **`auth.spec.ts`** - Authentication flow testing
- **`example.spec.ts`** - Basic example tests

## Key Testing Principles

### 1. Mobile-First Approach
All tests prioritize mobile experience:
- Touch-friendly element sizing (44px minimum)
- Responsive design validation
- Mobile-specific gestures and interactions

### 2. Emotional Validation
Tests verify emotional engagement elements:
- Inspirational copy and messaging
- Visual excitement and anticipation
- Celebration and pride-building moments
- Supportive error handling

### 3. Accessibility Focus
Comprehensive accessibility testing:
- Screen reader compatibility
- Keyboard navigation
- Proper ARIA labels and roles
- Color contrast and visual indicators

### 4. Real-World Scenarios
Tests simulate actual user behavior:
- Network failures and recovery
- AI service interruptions
- Payment processing errors
- Mobile device limitations

## Running Tests

### Local Development
```bash
# Run all e2e tests
npm run test:e2e

# Run with UI mode for debugging
npm run test:e2e:ui

# Run specific test file
npx playwright test emotional-flows/complete-journey.spec.ts

# Run tests for specific browser
npx playwright test --project=chromium-mobile
```

### CI/CD Environment
Tests run automatically in GitHub Actions with:
- Multiple browser configurations
- Mobile and desktop viewports
- Parallel execution for speed
- Artifact collection on failures

## Test Data and Mocking

### Authentication Mocking
Tests use `AuthHelpers.mockAuthentication()` to simulate logged-in users without OAuth flows:

```typescript
await authHelpers.mockAuthentication('regular'); // or 'premium', 'admin'
```

### API Mocking
Network requests are mocked for consistent testing:

```typescript
// Mock AI service failure
await page.route('**/api/ai/tryon', route => {
  route.fulfill({ status: 500, body: JSON.stringify({ error: 'Service unavailable' }) });
});
```

### Test Assets
Place test images and files in `/test-assets/` directory:
- `user-photo.jpg` - Sample user photo for try-on
- `sample-design.png` - Sample design upload
- `mock-avatar.jpg` - User avatar placeholder

## Browser Configuration

### Mobile-First Projects
- **chromium-mobile** - Primary mobile testing (Pixel 5)
- **webkit-mobile** - iOS mobile testing (iPhone 12)
- **tablet** - Tablet experience (iPad Pro)

### Desktop Projects  
- **chromium-desktop** - Primary desktop testing
- **firefox-desktop** - Firefox compatibility
- **webkit-desktop** - Safari compatibility

### Specialized Projects
- **high-dpi** - High-resolution display testing
- Visual regression testing for design elements

## Debugging Tests

### Visual Debugging
```bash
# Run with headed browser
npx playwright test --headed

# Run with slow motion
npx playwright test --headed --slowMo=1000

# Debug specific test
npx playwright test --debug emotional-flows/customization.spec.ts
```

### Screenshots and Videos
- Screenshots taken on failure automatically
- Videos recorded for failed tests in CI
- Trace files generated for debugging

### Common Issues

1. **Timing Issues**
   - Use `page.waitForLoadState('networkidle')` for dynamic content
   - Add explicit waits for animations: `await page.waitForTimeout(500)`

2. **Element Not Found**
   - Verify test-id attributes exist in components
   - Check for dynamic content loading
   - Use `.or()` for multiple possible selectors

3. **Mobile Touch Issues**
   - Ensure elements meet minimum touch target size (44px)
   - Use `page.touchscreen.tap()` for mobile-specific interactions

## Best Practices

### Test Organization
- Group related tests in `test.describe()` blocks
- Use `test.step()` for clear test progression
- Keep individual tests focused and atomic

### Assertions
- Use emotional language in test descriptions
- Verify both functionality and emotional elements
- Test error states with supportive messaging

### Performance
- Run critical path tests first
- Use parallel execution where possible
- Mock external services for speed and reliability

### Maintenance
- Update test-ids when components change
- Review emotional copy assertions regularly
- Keep helper functions DRY and reusable

## Contributing

When adding new tests:

1. Follow the emotional flow testing pattern
2. Include mobile-first considerations
3. Add accessibility validations
4. Test both success and error scenarios
5. Update this README with new patterns or utilities

## Reporting

Test results are available in:
- HTML report: `playwright-report/index.html`
- JUnit XML: `test-results/junit.xml` (CI)
- GitHub Actions summary with artifacts
