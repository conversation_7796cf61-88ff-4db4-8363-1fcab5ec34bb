/**
 * Payment Confirmation Email Template
 * Confirms successful payment and builds excitement for order processing
 */

import { Section, Text, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { PaymentEmailData } from '../../lib/services/email';

interface PaymentConfirmationEmailProps extends PaymentEmailData {}

export function PaymentConfirmationEmail({
  customerName,
  orderNumber,
  amount,
  currency,
  paymentMethod,
  transactionId,
  orderUrl,
}: PaymentConfirmationEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`Payment confirmed for order #${orderNumber}! Your custom creation is now being processed.`}>
      {/* Header */}
      <Section className="text-center mb-8">
        <EmailHeading level={1}>
          💳 Payment Confirmed, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Great news! Your payment has been successfully processed. Your custom creation is now moving into production.
        </EmailText>
      </Section>

      {/* Payment Success */}
      <Section className="bg-green-50 rounded-lg p-6 mb-8 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Text className="text-green-600 text-2xl">✓</Text>
        </div>
        
        <EmailHeading level={2}>Payment Successful</EmailHeading>
        <EmailText>
          Your payment has been securely processed and your order is confirmed. We're excited to start crafting your unique piece!
        </EmailText>
      </Section>

      {/* Payment Details */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={2}>Payment Details</EmailHeading>
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <Text className="text-gray-700">Order Number:</Text>
            <Text className="font-semibold text-gray-900">#{orderNumber}</Text>
          </div>
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Amount Paid:</Text>
            <Text className="font-semibold text-primary-600 text-lg">{currency} {amount}</Text>
          </div>
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Payment Method:</Text>
            <Text className="font-semibold text-gray-900 capitalize">{paymentMethod}</Text>
          </div>
          
          {transactionId && (
            <div className="flex justify-between">
              <Text className="text-gray-700">Transaction ID:</Text>
              <Text className="font-mono text-sm text-gray-900">{transactionId}</Text>
            </div>
          )}
          
          <div className="flex justify-between">
            <Text className="text-gray-700">Status:</Text>
            <Text className="font-semibold text-green-600">Confirmed</Text>
          </div>
        </div>
      </Section>

      {/* What Happens Next */}
      <Section className="mb-8">
        <EmailHeading level={2}>What Happens Next?</EmailHeading>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Text className="text-primary-600 font-bold text-sm">1</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Order Processing (Now)
              </Text>
              <Text className="text-gray-600 text-sm">
                Your order is being prepared for production. Our team will review your design and prepare the materials.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Text className="text-primary-600 font-bold text-sm">2</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Production (1-3 days)
              </Text>
              <Text className="text-gray-600 text-sm">
                Our skilled artisans will carefully craft your custom piece using premium materials and attention to detail.
              </Text>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
              <Text className="text-primary-600 font-bold text-sm">3</Text>
            </div>
            <div>
              <Text className="font-semibold text-gray-900 mb-1">
                Quality Check & Shipping
              </Text>
              <Text className="text-gray-600 text-sm">
                After quality assurance, your order will be carefully packaged and shipped. You'll receive tracking details.
              </Text>
            </div>
          </div>
        </div>
      </Section>

      {/* Security Note */}
      <Section className="bg-blue-50 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>🔒 Your Payment is Secure</EmailHeading>
        <EmailText>
          Your payment information is protected by industry-standard encryption. We never store your complete payment details on our servers.
        </EmailText>
        
        <div className="flex items-center justify-center space-x-4 mt-4">
          <Text className="text-xs text-gray-600">Secured by SSL</Text>
          <Text className="text-xs text-gray-400">•</Text>
          <Text className="text-xs text-gray-600">PCI Compliant</Text>
          <Text className="text-xs text-gray-400">•</Text>
          <Text className="text-xs text-gray-600">Bank-level Security</Text>
        </div>
      </Section>

      {/* Call to Action */}
      <Section className="text-center mb-8">
        <EmailHeading level={2}>Track Your Order</EmailHeading>
        <EmailText>
          Stay updated on your order's progress and estimated delivery time.
        </EmailText>
        
        <EmailButton href={orderUrl} variant="primary">
          View Order Status
        </EmailButton>
      </Section>

      {/* Receipt Information */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>📄 Receipt Information</EmailHeading>
        <EmailText>
          This email serves as your payment receipt. Please keep it for your records. If you need a formal invoice, you can download it from your order page.
        </EmailText>
        
        <div className="text-center mt-4">
          <EmailButton href={`${orderUrl}?download=invoice`} variant="outline">
            Download Invoice
          </EmailButton>
        </div>
      </Section>

      {/* Support */}
      <Section className="text-center mb-8">
        <EmailHeading level={3}>Questions About Your Payment?</EmailHeading>
        <EmailText>
          If you have any questions about this payment or your order, our support team is here to help.
        </EmailText>
        
        <EmailButton href={`${baseUrl}/support`} variant="outline">
          Contact Support
        </EmailButton>
      </Section>

      {/* Footer */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          Thank you for choosing Ottiq to express your unique style!
        </EmailText>
        
        <Text className="text-xs text-gray-500">
          Payment processed on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
        </Text>
      </Section>
    </EmailLayout>
  );
}
