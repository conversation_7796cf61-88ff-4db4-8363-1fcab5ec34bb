/**
 * Enhanced Server-side Image Processing Utilities for Ottiq
 *
 * Handles canvas to image conversion, optimization, and preview generation
 * using Sharp for high-performance image processing with advanced caching
 * and performance optimizations for instant visual feedback
 */

import sharp from 'sharp';
import { DesignCanvas, DesignElement } from '@/types';
import { RedisUtils } from '@/lib/redis';
import crypto from 'crypto';

// Enhanced image processing configuration
export const IMAGE_CONFIG = {
  // Preview image settings
  PREVIEW_WIDTH: 800,
  PREVIEW_HEIGHT: 600,
  PREVIEW_QUALITY: 85,
  PREVIEW_FORMAT: 'webp' as const, // Changed to WebP for better compression

  // High-resolution export settings
  EXPORT_DPI: 300,
  EXPORT_QUALITY: 95,
  EXPORT_FORMAT: 'png' as const,

  // Thumbnail settings
  THUMBNAIL_SIZE: 200,
  THUMBNAIL_QUALITY: 80,

  // Progressive loading sizes
  PROGRESSIVE_SIZES: [200, 400, 800] as const,

  // Maximum file sizes (in bytes)
  MAX_PREVIEW_SIZE: 2 * 1024 * 1024, // 2MB
  MAX_EXPORT_SIZE: 10 * 1024 * 1024, // 10MB

  // Cache settings
  CACHE_TTL: {
    PREVIEW: 3600, // 1 hour
    THUMBNAIL: 86400, // 24 hours
    OPTIMIZED: 7200, // 2 hours
  },

  // Performance settings
  SHARP_CONCURRENCY: 4, // Limit concurrent Sharp operations
  MEMORY_LIMIT: 512, // MB
} as const;

// Configure Sharp for optimal performance
sharp.concurrency(IMAGE_CONFIG.SHARP_CONCURRENCY);
sharp.cache({ memory: IMAGE_CONFIG.MEMORY_LIMIT });

/**
 * Generate cache key for image operations
 */
function generateCacheKey(operation: string, data: any): string {
  const hash = crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  return `img:${operation}:${hash}`;
}

/**
 * Convert canvas data to SVG string
 * This is a simplified version - in production you'd want more robust SVG generation
 */
export function canvasToSVG(canvas: DesignCanvas): string {
  const { width, height, backgroundColor, elements } = canvas;
  
  let svgContent = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Add background
  svgContent += `<rect width="100%" height="100%" fill="${backgroundColor}"/>`;
  
  // Add elements (simplified - you'd need more complex rendering for real implementation)
  elements.forEach(element => {
    if (!element.visible || element.opacity === 0) return;
    
    const transform = element.rotation 
      ? `transform="rotate(${element.rotation} ${element.x + element.width/2} ${element.y + element.height/2})"` 
      : '';
    
    if (element.type === 'text' && element.data) {
      const textData = element.data as any;
      svgContent += `<text x="${element.x}" y="${element.y + element.height/2}" 
        font-family="${textData.fontFamily || 'Arial'}" 
        font-size="${textData.fontSize || 16}" 
        font-weight="${textData.fontWeight || 'normal'}"
        fill="${textData.color || '#000000'}" 
        opacity="${element.opacity}"
        ${transform}>
        ${textData.text || ''}
      </text>`;
    } else if (element.type === 'image' && element.data) {
      const imageData = element.data as any;
      svgContent += `<image x="${element.x}" y="${element.y}" 
        width="${element.width}" height="${element.height}" 
        href="${imageData.src}" 
        opacity="${element.opacity}"
        ${transform}/>`;
    } else {
      // Generic rectangle for other elements
      svgContent += `<rect x="${element.x}" y="${element.y}" 
        width="${element.width}" height="${element.height}" 
        fill="#cccccc" 
        opacity="${element.opacity}"
        ${transform}/>`;
    }
  });
  
  svgContent += '</svg>';
  return svgContent;
}

/**
 * Generate preview image from canvas data with caching
 */
export async function generatePreviewImage(
  canvas: DesignCanvas,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'png' | 'jpeg' | 'webp';
    useCache?: boolean;
  } = {}
): Promise<{
  buffer: Buffer;
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
  fromCache?: boolean;
}> {
  const {
    width = IMAGE_CONFIG.PREVIEW_WIDTH,
    height = IMAGE_CONFIG.PREVIEW_HEIGHT,
    quality = IMAGE_CONFIG.PREVIEW_QUALITY,
    format = IMAGE_CONFIG.PREVIEW_FORMAT,
    useCache = true,
  } = options;

  // Generate cache key
  const cacheKey = generateCacheKey('preview', { canvas, width, height, quality, format });

  // Try to get from cache first
  if (useCache) {
    try {
      const cached = await RedisUtils.get(cacheKey);
      if (cached) {
        const cachedData = JSON.parse(cached);
        return {
          buffer: Buffer.from(cachedData.buffer, 'base64'),
          metadata: cachedData.metadata,
          fromCache: true,
        };
      }
    } catch (error) {
      console.warn('Cache retrieval failed:', error);
    }
  }

  try {
    const startTime = Date.now();

    // Convert canvas to SVG
    const svgString = canvasToSVG(canvas);
    const svgBuffer = Buffer.from(svgString);

    // Process with Sharp
    let sharpInstance = sharp(svgBuffer)
      .resize(width, height, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      });

    // Apply format-specific settings with optimizations
    if (format === 'png') {
      sharpInstance = sharpInstance.png({
        quality: Math.round(quality / 10),
        compressionLevel: 6,
        progressive: false // Faster for small images
      });
    } else if (format === 'jpeg') {
      sharpInstance = sharpInstance.jpeg({
        quality,
        progressive: true,
        mozjpeg: true // Better compression
      });
    } else if (format === 'webp') {
      sharpInstance = sharpInstance.webp({
        quality,
        effort: 4, // Balance between compression and speed
        smartSubsample: true
      });
    }

    const buffer = await sharpInstance.toBuffer();
    const metadata = await sharp(buffer).metadata();

    const processingTime = Date.now() - startTime;
    console.log(`Image processing completed in ${processingTime}ms`);

    const result = {
      buffer,
      metadata: {
        width: metadata.width || width,
        height: metadata.height || height,
        format: metadata.format || format,
        size: buffer.length,
      },
      fromCache: false,
    };

    // Cache the result
    if (useCache && buffer.length < IMAGE_CONFIG.MAX_PREVIEW_SIZE) {
      try {
        const cacheData = {
          buffer: buffer.toString('base64'),
          metadata: result.metadata,
        };
        await RedisUtils.setex(cacheKey, IMAGE_CONFIG.CACHE_TTL.PREVIEW, JSON.stringify(cacheData));
      } catch (error) {
        console.warn('Cache storage failed:', error);
      }
    }

    return result;
  } catch (error) {
    console.error('Error generating preview image:', error);
    throw new Error('Failed to generate preview image');
  }
}

/**
 * Generate thumbnail from preview image
 */
export async function generateThumbnail(
  imageBuffer: Buffer,
  size: number = IMAGE_CONFIG.THUMBNAIL_SIZE
): Promise<Buffer> {
  try {
    return await sharp(imageBuffer)
      .resize(size, size, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: IMAGE_CONFIG.THUMBNAIL_QUALITY })
      .toBuffer();
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    throw new Error('Failed to generate thumbnail');
  }
}

/**
 * Optimize image for web delivery
 */
export async function optimizeImage(
  imageBuffer: Buffer,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'png' | 'jpeg' | 'webp';
  } = {}
): Promise<Buffer> {
  const {
    maxWidth = 1200,
    maxHeight = 1200,
    quality = 85,
    format = 'webp',
  } = options;

  try {
    let sharpInstance = sharp(imageBuffer);
    
    // Get original metadata
    const metadata = await sharpInstance.metadata();
    
    // Resize if necessary
    if (metadata.width && metadata.height) {
      if (metadata.width > maxWidth || metadata.height > maxHeight) {
        sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }
    }

    // Apply format and quality
    if (format === 'webp') {
      sharpInstance = sharpInstance.webp({ quality });
    } else if (format === 'jpeg') {
      sharpInstance = sharpInstance.jpeg({ quality, progressive: true });
    } else {
      sharpInstance = sharpInstance.png({ quality: Math.round(quality / 10) });
    }

    return await sharpInstance.toBuffer();
  } catch (error) {
    console.error('Error optimizing image:', error);
    throw new Error('Failed to optimize image');
  }
}

/**
 * Validate image buffer and metadata
 */
export async function validateImage(imageBuffer: Buffer): Promise<{
  isValid: boolean;
  metadata?: sharp.Metadata;
  error?: string;
}> {
  try {
    const metadata = await sharp(imageBuffer).metadata();
    
    // Check if it's a valid image
    if (!metadata.width || !metadata.height || !metadata.format) {
      return {
        isValid: false,
        error: 'Invalid image format or corrupted file'
      };
    }

    // Check file size limits
    if (imageBuffer.length > IMAGE_CONFIG.MAX_EXPORT_SIZE) {
      return {
        isValid: false,
        error: 'Image file size exceeds maximum limit'
      };
    }

    return {
      isValid: true,
      metadata
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Failed to process image file'
    };
  }
}

/**
 * Generate progressive image sizes for instant loading
 */
export async function generateProgressiveImages(
  canvas: DesignCanvas,
  options: {
    sizes?: readonly number[];
    format?: 'webp' | 'jpeg';
    quality?: number;
    useCache?: boolean;
  } = {}
): Promise<{
  sizes: Array<{
    width: number;
    buffer: Buffer;
    metadata: any;
  }>;
  fromCache?: boolean;
}> {
  const {
    sizes = IMAGE_CONFIG.PROGRESSIVE_SIZES,
    format = 'webp',
    quality = 85,
    useCache = true,
  } = options;

  const cacheKey = generateCacheKey('progressive', { canvas, sizes, format, quality });

  // Try cache first
  if (useCache) {
    try {
      const cached = await RedisUtils.get(cacheKey);
      if (cached) {
        const cachedData = JSON.parse(cached);
        return {
          sizes: cachedData.sizes.map((size: any) => ({
            ...size,
            buffer: Buffer.from(size.buffer, 'base64'),
          })),
          fromCache: true,
        };
      }
    } catch (error) {
      console.warn('Progressive cache retrieval failed:', error);
    }
  }

  try {
    const svgString = canvasToSVG(canvas);
    const svgBuffer = Buffer.from(svgString);

    // Generate all sizes in parallel for better performance
    const sizePromises = sizes.map(async (width) => {
      const height = Math.round((width / canvas.width) * canvas.height);

      let sharpInstance = sharp(svgBuffer)
        .resize(width, height, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        });

      if (format === 'webp') {
        sharpInstance = sharpInstance.webp({
          quality,
          effort: width <= 200 ? 2 : 4, // Less effort for small images
        });
      } else {
        sharpInstance = sharpInstance.jpeg({
          quality,
          progressive: true,
          mozjpeg: true,
        });
      }

      const buffer = await sharpInstance.toBuffer();
      const metadata = await sharp(buffer).metadata();

      return {
        width,
        buffer,
        metadata: {
          width: metadata.width || width,
          height: metadata.height || height,
          format: metadata.format || format,
          size: buffer.length,
        },
      };
    });

    const results = await Promise.all(sizePromises);

    // Cache the results
    if (useCache) {
      try {
        const cacheData = {
          sizes: results.map(result => ({
            ...result,
            buffer: result.buffer.toString('base64'),
          })),
        };
        await RedisUtils.setex(cacheKey, IMAGE_CONFIG.CACHE_TTL.PREVIEW, JSON.stringify(cacheData));
      } catch (error) {
        console.warn('Progressive cache storage failed:', error);
      }
    }

    return {
      sizes: results,
      fromCache: false,
    };
  } catch (error) {
    console.error('Error generating progressive images:', error);
    throw new Error('Failed to generate progressive images');
  }
}

/**
 * Generate multiple image variants with enhanced caching
 */
export async function generateImageVariants(
  canvas: DesignCanvas,
  options: {
    includeProgressive?: boolean;
    useCache?: boolean;
  } = {}
): Promise<{
  preview: Buffer;
  thumbnail: Buffer;
  optimized: Buffer;
  progressive?: Array<{ width: number; buffer: Buffer; metadata: any }>;
  metadata: {
    preview: any;
    thumbnail: any;
    optimized: any;
  };
  performance: {
    totalTime: number;
    cacheHits: number;
    cacheSize: number;
  };
}> {
  const { includeProgressive = false, useCache = true } = options;
  const startTime = Date.now();
  let cacheHits = 0;
  let totalCacheSize = 0;

  try {
    // Generate all variants in parallel
    const [previewResult, progressiveResult] = await Promise.all([
      generatePreviewImage(canvas, { useCache }),
      includeProgressive ? generateProgressiveImages(canvas, { useCache }) : null,
    ]);

    if (previewResult.fromCache) cacheHits++;
    if (progressiveResult?.fromCache) cacheHits++;

    // Generate thumbnail and optimized from preview
    const [thumbnail, optimized] = await Promise.all([
      generateThumbnail(previewResult.buffer),
      optimizeImage(previewResult.buffer),
    ]);

    // Get metadata for all variants
    const [thumbnailMetadata, optimizedMetadata] = await Promise.all([
      sharp(thumbnail).metadata(),
      sharp(optimized).metadata(),
    ]);

    totalCacheSize = previewResult.buffer.length + thumbnail.length + optimized.length;

    const result = {
      preview: previewResult.buffer,
      thumbnail,
      optimized,
      ...(progressiveResult && { progressive: progressiveResult.sizes }),
      metadata: {
        preview: previewResult.metadata,
        thumbnail: {
          width: thumbnailMetadata.width,
          height: thumbnailMetadata.height,
          format: thumbnailMetadata.format,
          size: thumbnail.length,
        },
        optimized: {
          width: optimizedMetadata.width,
          height: optimizedMetadata.height,
          format: optimizedMetadata.format,
          size: optimized.length,
        },
      },
      performance: {
        totalTime: Date.now() - startTime,
        cacheHits,
        cacheSize: totalCacheSize,
      },
    };

    return result;
  } catch (error) {
    console.error('Error generating image variants:', error);
    throw new Error('Failed to generate image variants');
  }
}
