/**
 * Individual Support Ticket API Routes
 * Handles specific ticket operations and updates
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { emailService } from '@/lib/services/email';

// Validation schemas
const UpdateTicketSchema = z.object({
  status: z.enum(['OPEN', 'RESOLVED', 'CLOSED']).optional(),
  satisfactionRating: z.number().min(1).max(5).optional(),
  satisfactionFeedback: z.string().max(1000).optional(),
});

/**
 * GET /api/support/tickets/[id] - Get specific ticket details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    // Build where clause - users can only see their own tickets, admins can see all
    const where: any = { id: params.id };
    if (!isAdmin) {
      where.userId = session.user.id;
    }

    const ticket = await prisma.supportTicket.findFirst({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Mark messages as read by the current user
    if (!isAdmin) {
      // Customer reading - mark admin messages as read by customer
      await prisma.supportMessage.updateMany({
        where: {
          ticketId: params.id,
          senderRole: 'ADMIN',
          readByCustomer: false,
        },
        data: {
          readByCustomer: true,
          readAt: new Date(),
        },
      });
    } else {
      // Admin reading - mark customer messages as read by admin
      await prisma.supportMessage.updateMany({
        where: {
          ticketId: params.id,
          senderRole: 'CUSTOMER',
          readByAdmin: false,
        },
        data: {
          readByAdmin: true,
          readAt: new Date(),
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: ticket,
    });
  } catch (error) {
    console.error('Error fetching support ticket:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch support ticket',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/support/tickets/[id] - Update ticket (customer feedback or admin updates)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = UpdateTicketSchema.parse(body);

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    // Build where clause - users can only update their own tickets, admins can update all
    const where: any = { id: params.id };
    if (!isAdmin) {
      where.userId = session.user.id;
    }

    // Check if ticket exists and user has permission
    const existingTicket = await prisma.supportTicket.findFirst({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingTicket) {
      return NextResponse.json(
        { success: false, error: 'Ticket not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (validatedData.status) {
      updateData.status = validatedData.status;
      
      if (validatedData.status === 'RESOLVED') {
        updateData.resolvedAt = new Date();
        updateData.resolvedById = session.user.id;
      }
    }

    if (validatedData.satisfactionRating !== undefined) {
      updateData.satisfactionRating = validatedData.satisfactionRating;
    }

    if (validatedData.satisfactionFeedback !== undefined) {
      updateData.satisfactionFeedback = validatedData.satisfactionFeedback;
    }

    // Update the ticket
    const updatedTicket = await prisma.supportTicket.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    // Send notification emails based on the update
    try {
      if (validatedData.status === 'RESOLVED' && isAdmin) {
        // Admin resolved the ticket - notify customer
        await emailService.sendEmail('support-ticket-resolved', {
          to: existingTicket.user.email || '',
          customerName: existingTicket.user.name || 'Customer',
          ticketId: params.id,
          subject: existingTicket.subject,
          resolution: updateData.resolution || 'Your issue has been resolved.',
          ticketUrl: `${process.env.APP_URL}/support/tickets/${params.id}`,
        });
      }
    } catch (emailError) {
      console.error('Failed to send notification email:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: updatedTicket,
    });
  } catch (error) {
    console.error('Error updating support ticket:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update support ticket',
      },
      { status: 500 }
    );
  }
}
