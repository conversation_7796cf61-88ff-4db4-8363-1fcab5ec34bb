# Ottiq Complete Backup Script (PowerShell)
# Orchestrates backup of both PostgreSQL database and MinIO object storage
# Usage: .\backup-all.ps1 [environment]
# Environment: dev, prod (default: prod)

param(
    [string]$Environment = "prod"
)

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$BackupRoot = Join-Path $ProjectRoot "backups"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$LogFile = Join-Path $BackupRoot "backup_all_$Timestamp.log"

# Create backup directories
$null = New-Item -ItemType Directory -Force -Path (Join-Path $BackupRoot "database")
$null = New-Item -ItemType Directory -Force -Path (Join-Path $BackupRoot "minio")
$null = New-Item -ItemType Directory -Force -Path (Join-Path $BackupRoot "logs")

# Logging function
function Write-Log {
    param([string]$Message)
    $LogEntry = "[$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')] $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
}

# Error handling
$ErrorActionPreference = "Stop"
trap {
    Write-Log "ERROR: Complete backup failed with error: $_"
    Send-Alert "Complete backup failed for $Environment environment: $_"
    exit 1
}

# Function to send alerts
function Send-Alert {
    param([string]$Message)
    Write-Log "ALERT: $Message"
    $AlertsFile = Join-Path $BackupRoot "alerts.log"
    Add-Content -Path $AlertsFile -Value "BACKUP ALERT: $Message"
    
    # Add webhook notification if configured
    $WebhookUrl = $env:BACKUP_WEBHOOK_URL
    if ($WebhookUrl) {
        try {
            $Body = @{
                text = $Message
            } | ConvertTo-Json
            
            Invoke-RestMethod -Uri $WebhookUrl -Method Post -Body $Body -ContentType "application/json"
        }
        catch {
            Write-Log "Failed to send webhook alert: $_"
        }
    }
}

# Function to check system resources
function Test-SystemResources {
    Write-Log "Checking system resources..."
    
    # Check disk space
    $BackupDrive = (Get-Item $BackupRoot).PSDrive
    $DiskInfo = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "$($BackupDrive.Name):" }
    $UsagePercent = [math]::Round((($DiskInfo.Size - $DiskInfo.FreeSpace) / $DiskInfo.Size) * 100, 1)
    
    if ($UsagePercent -gt 85) {
        Write-Log "WARNING: Backup disk usage is $UsagePercent%"
        Send-Alert "High disk usage detected: $UsagePercent%"
    }
    
    # Check available memory
    $Memory = Get-WmiObject -Class Win32_OperatingSystem
    $AvailableMemoryGB = [math]::Round($Memory.FreePhysicalMemory / 1MB, 1)
    Write-Log "Available memory: ${AvailableMemoryGB}GB"
    
    # Check Docker daemon
    try {
        docker info | Out-Null
        Write-Log "Docker daemon is running"
    }
    catch {
        Write-Log "ERROR: Docker daemon is not running"
        throw "Docker daemon is not running"
    }
    
    Write-Log "System resource check completed"
}

# Function to backup database
function Backup-Database {
    Write-Log "Starting database backup..."
    
    $DbBackupScript = Join-Path $ScriptDir "backup-database.ps1"
    if (Test-Path $DbBackupScript) {
        try {
            & $DbBackupScript $Environment
            Write-Log "Database backup completed successfully"
            return $true
        }
        catch {
            Write-Log "ERROR: Database backup failed: $_"
            return $false
        }
    }
    else {
        Write-Log "ERROR: Database backup script not found: $DbBackupScript"
        return $false
    }
}

# Function to backup MinIO
function Backup-MinIO {
    Write-Log "Starting MinIO backup..."
    
    $MinIOBackupScript = Join-Path $ScriptDir "backup-minio.ps1"
    if (Test-Path $MinIOBackupScript) {
        try {
            & $MinIOBackupScript $Environment
            Write-Log "MinIO backup completed successfully"
            return $true
        }
        catch {
            Write-Log "ERROR: MinIO backup failed: $_"
            return $false
        }
    }
    else {
        Write-Log "ERROR: MinIO backup script not found: $MinIOBackupScript"
        return $false
    }
}

# Function to create backup summary
function New-BackupSummary {
    Write-Log "Creating backup summary..."
    
    $SummaryFile = Join-Path $BackupRoot "backup_summary_$Timestamp.json"
    $DbBackups = (Get-ChildItem -Path (Join-Path $BackupRoot "database") -Filter "ottiq_db_${Environment}_$($Timestamp.Substring(0,8))*.sql.gz" -ErrorAction SilentlyContinue).Count
    $MinIOBackups = (Get-ChildItem -Path (Join-Path $BackupRoot "minio") -Filter "ottiq_minio_${Environment}_$($Timestamp.Substring(0,8))*.tar.gz" -ErrorAction SilentlyContinue).Count
    
    # Calculate total backup size
    $TotalSize = (Get-ChildItem -Path $BackupRoot -Recurse -File | Measure-Object -Property Length -Sum).Sum
    $TotalSizeFormatted = if ($TotalSize -gt 1GB) { "{0:N2} GB" -f ($TotalSize / 1GB) } elseif ($TotalSize -gt 1MB) { "{0:N2} MB" -f ($TotalSize / 1MB) } else { "{0:N2} KB" -f ($TotalSize / 1KB) }
    
    # Get latest backup files
    $LatestDbBackup = Get-ChildItem -Path (Join-Path $BackupRoot "database") -Filter "ottiq_db_${Environment}_*.sql.gz" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    $LatestMinIOBackup = Get-ChildItem -Path (Join-Path $BackupRoot "minio") -Filter "ottiq_minio_${Environment}_*.tar.gz" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    $Summary = @{
        backup_session = @{
            timestamp = $Timestamp
            environment = $Environment
            date = (Get-Date).ToString("o")
            status = "completed"
        }
        database_backup = @{
            count = $DbBackups
            latest_file = if ($LatestDbBackup) { $LatestDbBackup.Name } else { "none" }
            size = if ($LatestDbBackup) { "{0:N2} MB" -f ($LatestDbBackup.Length / 1MB) } else { "unknown" }
        }
        minio_backup = @{
            count = $MinIOBackups
            latest_file = if ($LatestMinIOBackup) { $LatestMinIOBackup.Name } else { "none" }
            size = if ($LatestMinIOBackup) { "{0:N2} MB" -f ($LatestMinIOBackup.Length / 1MB) } else { "unknown" }
        }
        total_backup_size = $TotalSizeFormatted
        backup_location = $BackupRoot
    }
    
    $Summary | ConvertTo-Json -Depth 3 | Set-Content -Path $SummaryFile
    Write-Log "Backup summary created: $SummaryFile"
}

# Function to cleanup old logs
function Remove-OldLogs {
    Write-Log "Cleaning up old backup logs..."
    
    # Remove log files older than 30 days
    $CutoffDate = (Get-Date).AddDays(-30)
    Get-ChildItem -Path $BackupRoot -Filter "backup_all_*.log" | Where-Object { $_.LastWriteTime -lt $CutoffDate } | Remove-Item -Force
    Get-ChildItem -Path $BackupRoot -Filter "backup_summary_*.json" | Where-Object { $_.LastWriteTime -lt $CutoffDate } | Remove-Item -Force
    
    # Keep only the last 100 alert entries
    $AlertsFile = Join-Path $BackupRoot "alerts.log"
    if (Test-Path $AlertsFile) {
        $AlertLines = Get-Content $AlertsFile
        if ($AlertLines.Count -gt 100) {
            $AlertLines[-100..-1] | Set-Content $AlertsFile
        }
    }
    
    Write-Log "Old logs cleanup completed"
}

# Function to send success notification
function Send-SuccessNotification {
    $SummaryFile = Join-Path $BackupRoot "backup_summary_$Timestamp.json"
    
    if (Test-Path $SummaryFile) {
        $Summary = Get-Content $SummaryFile | ConvertFrom-Json
        $DbSize = $Summary.database_backup.size
        $MinIOSize = $Summary.minio_backup.size
        $TotalSize = $Summary.total_backup_size
        
        $Message = "Backup completed successfully for $Environment environment. DB: $DbSize, MinIO: $MinIOSize, Total: $TotalSize"
        Write-Log "SUCCESS: $Message"
        
        # Send success notification if webhook is configured
        $SuccessWebhookUrl = $env:BACKUP_SUCCESS_WEBHOOK_URL
        if ($SuccessWebhookUrl) {
            try {
                $Body = @{
                    text = "✅ $Message"
                } | ConvertTo-Json
                
                Invoke-RestMethod -Uri $SuccessWebhookUrl -Method Post -Body $Body -ContentType "application/json"
            }
            catch {
                Write-Log "Failed to send success webhook: $_"
            }
        }
    }
}

# Function to validate backups
function Test-BackupIntegrity {
    Write-Log "Validating backup integrity..."
    
    $ValidationErrors = 0
    
    # Validate database backup
    $LatestDbBackup = Get-ChildItem -Path (Join-Path $BackupRoot "database") -Filter "ottiq_db_${Environment}_*.sql.gz" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($LatestDbBackup) {
        # Test gzip integrity (simplified check)
        try {
            $TestResult = & gzip -t $LatestDbBackup.FullName 2>&1
            Write-Log "Database backup validation: PASSED"
        }
        catch {
            Write-Log "ERROR: Database backup validation: FAILED"
            $ValidationErrors++
        }
        
        # Check checksum if available
        $ChecksumFile = "$($LatestDbBackup.FullName).sha256"
        if (Test-Path $ChecksumFile) {
            # PowerShell checksum validation would go here
            Write-Log "Database backup checksum: SKIPPED (PowerShell implementation)"
        }
    }
    else {
        Write-Log "ERROR: No database backup found for validation"
        $ValidationErrors++
    }
    
    # Validate MinIO backup
    $LatestMinIOBackup = Get-ChildItem -Path (Join-Path $BackupRoot "minio") -Filter "ottiq_minio_${Environment}_*.tar.gz" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($LatestMinIOBackup) {
        # Test tar.gz integrity (simplified check)
        try {
            # PowerShell doesn't have built-in tar validation, so we'll skip this
            Write-Log "MinIO backup validation: SKIPPED (PowerShell implementation)"
        }
        catch {
            Write-Log "ERROR: MinIO backup validation: FAILED"
            $ValidationErrors++
        }
    }
    else {
        Write-Log "ERROR: No MinIO backup found for validation"
        $ValidationErrors++
    }
    
    if ($ValidationErrors -eq 0) {
        Write-Log "All backup validations passed"
        return $true
    }
    else {
        Write-Log "ERROR: $ValidationErrors backup validation(s) failed"
        return $false
    }
}

# Main execution
function Main {
    Write-Log "=== Ottiq Complete Backup Started ==="
    Write-Log "Environment: $Environment"
    Write-Log "Timestamp: $Timestamp"
    Write-Log "Backup location: $BackupRoot"
    
    # System checks
    Test-SystemResources
    
    # Perform backups
    $BackupSuccess = $true
    
    # Database backup
    if (-not (Backup-Database)) {
        $BackupSuccess = $false
    }
    
    # MinIO backup
    if (-not (Backup-MinIO)) {
        $BackupSuccess = $false
    }
    
    # Check if any backup failed
    if (-not $BackupSuccess) {
        Write-Log "ERROR: One or more backups failed"
        exit 1
    }
    
    # Validate backups
    if (-not (Test-BackupIntegrity)) {
        exit 1
    }
    
    # Create summary
    New-BackupSummary
    
    # Cleanup old logs
    Remove-OldLogs
    
    # Send success notification
    Send-SuccessNotification
    
    Write-Log "=== Ottiq Complete Backup Completed Successfully ==="
}

# Run main function
Main
