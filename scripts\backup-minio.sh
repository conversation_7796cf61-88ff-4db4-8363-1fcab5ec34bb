#!/bin/bash

# Ottiq MinIO Backup Script
# Performs automated MinIO object storage backups with compression and rotation
# Usage: ./backup-minio.sh [environment]
# Environment: dev, prod (default: prod)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-prod}"

# Load environment variables
if [[ "$ENVIRONMENT" == "prod" ]]; then
    ENV_FILE="$PROJECT_ROOT/.env.docker"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"
else
    ENV_FILE="$PROJECT_ROOT/.env"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"
fi

# Source environment variables
if [[ -f "$ENV_FILE" ]]; then
    set -a
    source "$ENV_FILE"
    set +a
else
    echo "Error: Environment file $ENV_FILE not found"
    exit 1
fi

# Backup configuration
BACKUP_DIR="$PROJECT_ROOT/backups/minio"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="ottiq_minio_${ENVIRONMENT}_${TIMESTAMP}.tar.gz"
LOG_FILE="$BACKUP_DIR/backup_${TIMESTAMP}.log"

# Retention settings (days)
RETENTION_DAYS=30
RETENTION_WEEKLY=12  # Keep weekly backups for 12 weeks
RETENTION_MONTHLY=12 # Keep monthly backups for 12 months

# MinIO settings
MINIO_CONTAINER="ottiq-minio"
MINIO_DATA_PATH="/data"
MINIO_VOLUME="ottiq_minio_data"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        log "ERROR: MinIO backup failed with exit code $exit_code"
        send_alert "MinIO backup failed for $ENVIRONMENT environment"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Function to send alerts
send_alert() {
    local message="$1"
    log "ALERT: $message"
    echo "BACKUP ALERT: $message" >> "$BACKUP_DIR/alerts.log"
}

# Function to check if MinIO container is running
check_container() {
    if ! docker ps --format "table {{.Names}}" | grep -q "^${MINIO_CONTAINER}$"; then
        log "ERROR: MinIO container '$MINIO_CONTAINER' is not running"
        return 1
    fi
    return 0
}

# Function to test MinIO connection
test_connection() {
    log "Testing MinIO connection..."
    
    # Try to access MinIO health endpoint
    if docker exec "$MINIO_CONTAINER" curl -f http://localhost:9000/minio/health/live > /dev/null 2>&1; then
        log "MinIO connection successful"
        return 0
    else
        log "ERROR: Cannot connect to MinIO"
        return 1
    fi
}

# Function to get MinIO storage usage
get_storage_usage() {
    local usage=$(docker exec "$MINIO_CONTAINER" du -sh "$MINIO_DATA_PATH" 2>/dev/null | cut -f1 || echo "Unknown")
    echo "$usage"
}

# Function to list MinIO buckets and objects
list_buckets_and_objects() {
    log "MinIO storage contents:"
    
    # List buckets and their contents
    docker exec "$MINIO_CONTAINER" find "$MINIO_DATA_PATH" -type d -name "*" 2>/dev/null | while read -r bucket_path; do
        if [[ "$bucket_path" != "$MINIO_DATA_PATH" ]]; then
            local bucket_name=$(basename "$bucket_path")
            local object_count=$(docker exec "$MINIO_CONTAINER" find "$bucket_path" -type f 2>/dev/null | wc -l)
            local bucket_size=$(docker exec "$MINIO_CONTAINER" du -sh "$bucket_path" 2>/dev/null | cut -f1 || echo "0")
            log "  Bucket: $bucket_name - Objects: $object_count - Size: $bucket_size"
        fi
    done
}

# Function to perform MinIO backup
backup_minio() {
    log "Starting MinIO backup for $ENVIRONMENT environment..."
    log "Container: $MINIO_CONTAINER"
    log "Backup file: $BACKUP_FILE"
    
    # Get storage usage before backup
    local storage_usage=$(get_storage_usage)
    log "Storage usage: $storage_usage"
    
    # List buckets and objects
    list_buckets_and_objects
    
    # Create backup using docker volume backup method
    log "Creating MinIO data backup..."
    
    # Method 1: Direct volume backup (recommended for Docker volumes)
    if docker volume inspect "$MINIO_VOLUME" > /dev/null 2>&1; then
        log "Using Docker volume backup method..."
        
        # Create temporary container to access volume
        if docker run --rm \
            -v "$MINIO_VOLUME":/source:ro \
            -v "$BACKUP_DIR":/backup \
            alpine:latest \
            tar czf "/backup/$BACKUP_FILE" -C /source . 2>> "$LOG_FILE"; then
            
            log "MinIO volume backup created successfully"
        else
            log "ERROR: Failed to create MinIO volume backup"
            return 1
        fi
    else
        log "ERROR: MinIO volume '$MINIO_VOLUME' not found"
        return 1
    fi
    
    # Verify backup file
    if [[ -f "$BACKUP_DIR/$BACKUP_FILE" ]]; then
        local backup_size=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
        log "Backup completed successfully"
        log "Backup file: $BACKUP_FILE"
        log "Backup size: $backup_size"
        
        # Create checksum
        local checksum=$(sha256sum "$BACKUP_DIR/$BACKUP_FILE" | cut -d' ' -f1)
        echo "$checksum  $BACKUP_FILE" > "$BACKUP_DIR/${BACKUP_FILE}.sha256"
        log "Checksum created: $checksum"
        
        # Create backup manifest
        create_backup_manifest
        
        return 0
    else
        log "ERROR: Backup file not found after creation"
        return 1
    fi
}

# Function to create backup manifest
create_backup_manifest() {
    local manifest_file="$BACKUP_DIR/${BACKUP_FILE}.manifest"
    
    log "Creating backup manifest..."
    
    cat > "$manifest_file" << EOF
{
  "backup_type": "minio",
  "environment": "$ENVIRONMENT",
  "timestamp": "$TIMESTAMP",
  "backup_file": "$BACKUP_FILE",
  "container": "$MINIO_CONTAINER",
  "volume": "$MINIO_VOLUME",
  "created_at": "$(date -Iseconds)",
  "storage_usage": "$(get_storage_usage)",
  "backup_size": "$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)",
  "checksum": "$(cat "$BACKUP_DIR/${BACKUP_FILE}.sha256" | cut -d' ' -f1)"
}
EOF
    
    log "Backup manifest created: ${BACKUP_FILE}.manifest"
}

# Function to clean up old backups
cleanup_old_backups() {
    log "Cleaning up old MinIO backups..."
    
    # Remove backups older than retention period
    find "$BACKUP_DIR" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz.sha256" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz.manifest" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    local cleaned_count=$(find "$BACKUP_DIR" -name "ottiq_minio_${ENVIRONMENT}_*.tar.gz" -type f -mtime +$RETENTION_DAYS 2>/dev/null | wc -l)
    log "Cleaned up $cleaned_count old backup files"
}

# Function to validate backup integrity
validate_backup() {
    log "Validating backup integrity..."
    
    # Check if backup file exists and is not empty
    if [[ ! -f "$BACKUP_DIR/$BACKUP_FILE" ]] || [[ ! -s "$BACKUP_DIR/$BACKUP_FILE" ]]; then
        log "ERROR: Backup file is missing or empty"
        return 1
    fi
    
    # Verify checksum
    if [[ -f "$BACKUP_DIR/${BACKUP_FILE}.sha256" ]]; then
        if cd "$BACKUP_DIR" && sha256sum -c "${BACKUP_FILE}.sha256" > /dev/null 2>&1; then
            log "Backup checksum verification passed"
        else
            log "ERROR: Backup checksum verification failed"
            return 1
        fi
    fi
    
    # Test archive integrity
    if tar -tzf "$BACKUP_DIR/$BACKUP_FILE" > /dev/null 2>&1; then
        log "Backup archive integrity check passed"
    else
        log "ERROR: Backup archive is corrupted"
        return 1
    fi
    
    return 0
}

# Main execution
main() {
    log "=== Ottiq MinIO Backup Started ==="
    log "Environment: $ENVIRONMENT"
    log "Timestamp: $TIMESTAMP"
    
    # Pre-flight checks
    check_container || exit 1
    test_connection || exit 1
    
    # Perform backup
    backup_minio || exit 1
    
    # Validate backup
    validate_backup || exit 1
    
    # Cleanup old backups
    cleanup_old_backups
    
    log "=== Ottiq MinIO Backup Completed Successfully ==="
}

# Run main function
main "$@"
