version: '3.8'

services:
  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ottiq-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************************/ottiq_prod
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=ottiq_minio
      - MINIO_SECRET_KEY=ottiq_minio_password
      - MINIO_BUCKET_NAME=ottiq-uploads
      - MINIO_USE_SSL=false
      - NEXTAUTH_URL=http://localhost
      - NEXTAUTH_SECRET=your-production-secret-key-change-this
      - APP_URL=http://localhost
      - SMTP_HOST=mailhog
      - SMTP_PORT=1025
      - SMTP_FROM=<EMAIL>
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - ottiq-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ottiq-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ottiq_prod
      POSTGRES_USER: ottiq_user
      POSTGRES_PASSWORD: ottiq_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ottiq-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ottiq_user -d ottiq_prod"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: ottiq-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ottiq_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ottiq-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # MinIO Object Storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: ottiq-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ottiq_minio
      MINIO_ROOT_PASSWORD: ottiq_minio_password
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    volumes:
      - minio_data:/data
    networks:
      - ottiq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ottiq-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      app:
        condition: service_healthy
    networks:
      - ottiq-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Mailhog for email testing (remove in production)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ottiq-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - ottiq-network

volumes:
  postgres_data:
    driver: local
  minio_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  ottiq-network:
    driver: bridge
