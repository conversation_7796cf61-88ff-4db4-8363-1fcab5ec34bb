/**
 * Support Statistics API Route
 * Provides user-specific support statistics
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

/**
 * GET /api/support/stats - Get user's support statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's ticket statistics
    const [
      totalTickets,
      openTickets,
      resolvedTickets,
      closedTickets,
      recentTickets
    ] = await Promise.all([
      // Total tickets
      prisma.supportTicket.count({
        where: { userId: session.user.id }
      }),
      
      // Open tickets (including in progress, waiting states)
      prisma.supportTicket.count({
        where: {
          userId: session.user.id,
          status: {
            in: ['OPEN', 'IN_PROGRESS', 'WAITING_FOR_CUSTOMER', 'WAITING_FOR_ADMIN']
          }
        }
      }),
      
      // Resolved tickets
      prisma.supportTicket.count({
        where: {
          userId: session.user.id,
          status: 'RESOLVED'
        }
      }),
      
      // Closed tickets
      prisma.supportTicket.count({
        where: {
          userId: session.user.id,
          status: 'CLOSED'
        }
      }),
      
      // Recent tickets for response time calculation
      prisma.supportTicket.findMany({
        where: {
          userId: session.user.id,
          status: {
            in: ['RESOLVED', 'CLOSED']
          },
          resolvedAt: {
            not: null
          }
        },
        select: {
          createdAt: true,
          resolvedAt: true,
          messages: {
            where: {
              senderRole: 'ADMIN'
            },
            select: {
              createdAt: true
            },
            orderBy: {
              createdAt: 'asc'
            },
            take: 1
          }
        },
        orderBy: {
          resolvedAt: 'desc'
        },
        take: 10
      })
    ]);

    // Calculate average response time
    let averageResponseTime = 'N/A';
    
    if (recentTickets.length > 0) {
      const responseTimes = recentTickets
        .filter(ticket => ticket.messages.length > 0)
        .map(ticket => {
          const createdAt = new Date(ticket.createdAt);
          const firstResponseAt = new Date(ticket.messages[0].createdAt);
          return firstResponseAt.getTime() - createdAt.getTime();
        });

      if (responseTimes.length > 0) {
        const avgMs = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
        const avgHours = Math.round(avgMs / (1000 * 60 * 60));
        
        if (avgHours < 1) {
          const avgMinutes = Math.round(avgMs / (1000 * 60));
          averageResponseTime = `${avgMinutes}m`;
        } else if (avgHours < 24) {
          averageResponseTime = `${avgHours}h`;
        } else {
          const avgDays = Math.round(avgHours / 24);
          averageResponseTime = `${avgDays}d`;
        }
      }
    }

    // Get category breakdown
    const categoryStats = await prisma.supportTicket.groupBy({
      by: ['category'],
      where: { userId: session.user.id },
      _count: {
        category: true
      }
    });

    // Get priority breakdown
    const priorityStats = await prisma.supportTicket.groupBy({
      by: ['priority'],
      where: { userId: session.user.id },
      _count: {
        priority: true
      }
    });

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentActivity = await prisma.supportTicket.count({
      where: {
        userId: session.user.id,
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        totalTickets,
        openTickets,
        resolvedTickets,
        closedTickets,
        averageResponseTime,
        recentActivity,
        categoryBreakdown: categoryStats.reduce((acc, stat) => {
          acc[stat.category || 'general'] = stat._count.category;
          return acc;
        }, {} as Record<string, number>),
        priorityBreakdown: priorityStats.reduce((acc, stat) => {
          acc[stat.priority] = stat._count.priority;
          return acc;
        }, {} as Record<string, number>),
      }
    });
  } catch (error) {
    console.error('Error fetching support stats:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch support statistics',
      },
      { status: 500 }
    );
  }
}
