#!/bin/bash

# Ottiq Database Backup Script
# Performs automated PostgreSQL database backups with compression and rotation
# Usage: ./backup-database.sh [environment]
# Environment: dev, prod (default: prod)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-prod}"

# Load environment variables
if [[ "$ENVIRONMENT" == "prod" ]]; then
    ENV_FILE="$PROJECT_ROOT/.env.docker"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"
    DB_NAME="ottiq_prod"
else
    ENV_FILE="$PROJECT_ROOT/.env"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"
    DB_NAME="ottiq_dev"
fi

# Source environment variables
if [[ -f "$ENV_FILE" ]]; then
    set -a
    source "$ENV_FILE"
    set +a
else
    echo "Error: Environment file $ENV_FILE not found"
    exit 1
fi

# Backup configuration
BACKUP_DIR="$PROJECT_ROOT/backups/database"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="ottiq_db_${ENVIRONMENT}_${TIMESTAMP}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"
LOG_FILE="$BACKUP_DIR/backup_${TIMESTAMP}.log"

# Retention settings (days)
RETENTION_DAYS=30
RETENTION_WEEKLY=12  # Keep weekly backups for 12 weeks
RETENTION_MONTHLY=12 # Keep monthly backups for 12 months

# Database connection settings
DB_USER="ottiq_user"
DB_PASSWORD="ottiq_password"
DB_HOST="localhost"
DB_PORT="5432"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        log "ERROR: Backup failed with exit code $exit_code"
        # Send alert (implement notification system)
        send_alert "Database backup failed for $ENVIRONMENT environment"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Function to send alerts (placeholder - implement with your notification system)
send_alert() {
    local message="$1"
    log "ALERT: $message"
    
    # Example implementations:
    # - Send email via SMTP
    # - Send Slack notification
    # - Send Discord webhook
    # - Log to monitoring system
    
    # For now, just log the alert
    echo "BACKUP ALERT: $message" >> "$BACKUP_DIR/alerts.log"
}

# Function to check if Docker container is running
check_container() {
    local container_name="ottiq-postgres"
    if ! docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        log "ERROR: PostgreSQL container '$container_name' is not running"
        return 1
    fi
    return 0
}

# Function to test database connection
test_connection() {
    log "Testing database connection..."
    
    if docker exec ottiq-postgres pg_isready -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        log "Database connection successful"
        return 0
    else
        log "ERROR: Cannot connect to database"
        return 1
    fi
}

# Function to get database size
get_db_size() {
    local size=$(docker exec ottiq-postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" 2>/dev/null | xargs)
    echo "$size"
}

# Function to perform database backup
backup_database() {
    log "Starting database backup for $ENVIRONMENT environment..."
    log "Database: $DB_NAME"
    log "Backup file: $COMPRESSED_FILE"
    
    # Get database size before backup
    local db_size=$(get_db_size)
    log "Database size: $db_size"
    
    # Create backup with pg_dump
    log "Creating database dump..."
    if docker exec ottiq-postgres pg_dump \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --file="/tmp/$BACKUP_FILE" 2>> "$LOG_FILE"; then
        
        log "Database dump created successfully"
    else
        log "ERROR: Failed to create database dump"
        return 1
    fi
    
    # Copy backup file from container
    log "Copying backup file from container..."
    if docker cp "ottiq-postgres:/tmp/$BACKUP_FILE" "$BACKUP_DIR/$BACKUP_FILE"; then
        log "Backup file copied successfully"
    else
        log "ERROR: Failed to copy backup file from container"
        return 1
    fi
    
    # Clean up temporary file in container
    docker exec ottiq-postgres rm -f "/tmp/$BACKUP_FILE" 2>/dev/null || true
    
    # Compress backup file
    log "Compressing backup file..."
    if gzip "$BACKUP_DIR/$BACKUP_FILE"; then
        log "Backup file compressed successfully"
    else
        log "ERROR: Failed to compress backup file"
        return 1
    fi
    
    # Verify backup file
    if [[ -f "$BACKUP_DIR/$COMPRESSED_FILE" ]]; then
        local backup_size=$(du -h "$BACKUP_DIR/$COMPRESSED_FILE" | cut -f1)
        log "Backup completed successfully"
        log "Backup file: $COMPRESSED_FILE"
        log "Backup size: $backup_size"
        
        # Create checksum
        local checksum=$(sha256sum "$BACKUP_DIR/$COMPRESSED_FILE" | cut -d' ' -f1)
        echo "$checksum  $COMPRESSED_FILE" > "$BACKUP_DIR/${COMPRESSED_FILE}.sha256"
        log "Checksum created: $checksum"
        
        return 0
    else
        log "ERROR: Backup file not found after compression"
        return 1
    fi
}

# Function to clean up old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Remove daily backups older than retention period
    find "$BACKUP_DIR" -name "ottiq_db_${ENVIRONMENT}_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    # Keep weekly backups (every Sunday)
    # Keep monthly backups (first of each month)
    # This is a simplified cleanup - in production, implement more sophisticated retention
    
    local cleaned_count=$(find "$BACKUP_DIR" -name "ottiq_db_${ENVIRONMENT}_*.sql.gz" -type f -mtime +$RETENTION_DAYS 2>/dev/null | wc -l)
    log "Cleaned up $cleaned_count old backup files"
}

# Main execution
main() {
    log "=== Ottiq Database Backup Started ==="
    log "Environment: $ENVIRONMENT"
    log "Timestamp: $TIMESTAMP"
    
    # Pre-flight checks
    check_container || exit 1
    test_connection || exit 1
    
    # Perform backup
    backup_database || exit 1
    
    # Cleanup old backups
    cleanup_old_backups
    
    log "=== Ottiq Database Backup Completed Successfully ==="
}

# Run main function
main "$@"
