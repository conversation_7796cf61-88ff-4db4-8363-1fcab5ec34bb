'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useParams } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle, Textarea } from '@/components/ui';
import { formatDistanceToNow, format } from 'date-fns';

interface SupportMessage {
  id: string;
  content: string;
  messageType: string;
  senderRole: string;
  isInternal: boolean;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    email: string;
  };
}

interface SupportTicket {
  id: string;
  subject: string;
  description: string;
  status: string;
  priority: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  messages: SupportMessage[];
}

export default function SupportTicketPage() {
  const { data: session, status } = useSession();
  const params = useParams();
  const ticketId = params.id as string;
  
  const [ticket, setTicket] = useState<SupportTicket | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (session?.user && ticketId) {
      fetchTicket();
    }
  }, [session, ticketId]);

  useEffect(() => {
    scrollToBottom();
  }, [ticket?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchTicket = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/support/tickets/${ticketId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch ticket');
      }

      const data = await response.json();
      
      if (data.success) {
        setTicket(data.data);
      } else {
        throw new Error(data.error || 'Failed to fetch ticket');
      }
    } catch (error) {
      console.error('Error fetching ticket:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch ticket');
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || sendingMessage) return;

    setSendingMessage(true);
    try {
      const response = await fetch(`/api/support/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newMessage.trim(),
          messageType: 'TEXT',
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setNewMessage('');
        await fetchTicket(); // Refresh to get the new message
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    } finally {
      setSendingMessage(false);
    }
  };

  const submitFeedback = async () => {
    if (rating === 0) return;

    try {
      const response = await fetch(`/api/support/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          satisfactionRating: rating,
          satisfactionFeedback: feedback.trim() || undefined,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setShowFeedback(false);
        setRating(0);
        setFeedback('');
        await fetchTicket();
        alert('Thank you for your feedback!');
      } else {
        throw new Error(data.error || 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('Failed to submit feedback. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'WAITING_FOR_CUSTOMER':
        return 'bg-orange-100 text-orange-800';
      case 'WAITING_FOR_ADMIN':
        return 'bg-purple-100 text-purple-800';
      case 'RESOLVED':
        return 'bg-green-100 text-green-800';
      case 'CLOSED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-green-100 text-green-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800';
      case 'URGENT':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-warm-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading ticket...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
        <Section variant="primary" padding="lg">
          <Container>
            <div className="text-center py-16">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Access Denied
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Please sign in to view your support tickets
              </p>
              <Button onClick={() => window.location.href = '/auth/signin'}>
                Sign In
              </Button>
            </div>
          </Container>
        </Section>
      </div>
    );
  }

  if (error || !ticket) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
        <Section variant="primary" padding="lg">
          <Container>
            <div className="text-center py-16">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Ticket Not Found
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                {error || 'The requested support ticket could not be found.'}
              </p>
              <Button onClick={() => window.location.href = '/support'}>
                Back to Support
              </Button>
            </div>
          </Container>
        </Section>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button
                variant="outline"
                onClick={() => window.location.href = '/support'}
              >
                ← Back to Support
              </Button>
              
              {ticket.status === 'RESOLVED' && !showFeedback && (
                <Button
                  onClick={() => setShowFeedback(true)}
                  className="bg-green-500 hover:bg-green-600"
                >
                  ⭐ Rate Experience
                </Button>
              )}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {ticket.subject}
              </h1>
              
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <span className="font-mono">#{ticket.id.slice(-8)}</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                  {ticket.status.replace('_', ' ')}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                  {ticket.priority}
                </span>
                <span className="capitalize">{ticket.category}</span>
                <span>Created {format(new Date(ticket.createdAt), 'MMM d, yyyy')}</span>
              </div>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Messages */}
            <div className="lg:col-span-2">
              <Card className="h-[600px] flex flex-col">
                <CardHeader>
                  <CardTitle>Conversation</CardTitle>
                </CardHeader>
                
                <CardContent className="flex-1 flex flex-col">
                  {/* Messages List */}
                  <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                    {ticket.messages.map((message, index) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={`flex ${message.senderRole === 'CUSTOMER' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[80%] rounded-lg p-4 ${
                          message.senderRole === 'CUSTOMER'
                            ? 'bg-warm-500 text-white'
                            : message.messageType === 'SYSTEM'
                            ? 'bg-gray-100 text-gray-700 text-center'
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          {message.messageType !== 'SYSTEM' && (
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">
                                {message.senderRole === 'CUSTOMER' ? 'You' : message.sender.name || 'Support Team'}
                              </span>
                              <span className={`text-xs ${
                                message.senderRole === 'CUSTOMER' ? 'text-white/70' : 'text-gray-500'
                              }`}>
                                {format(new Date(message.createdAt), 'MMM d, h:mm a')}
                              </span>
                            </div>
                          )}
                          
                          <div className="whitespace-pre-wrap">
                            {message.content}
                          </div>
                          
                          {message.messageType === 'SYSTEM' && (
                            <div className="text-xs text-gray-500 mt-1">
                              {format(new Date(message.createdAt), 'MMM d, h:mm a')}
                            </div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                    <div ref={messagesEndRef} />
                  </div>

                  {/* Message Input */}
                  {ticket.status !== 'CLOSED' && (
                    <form onSubmit={sendMessage} className="border-t pt-4">
                      <div className="flex gap-2">
                        <Textarea
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          placeholder="Type your message..."
                          rows={2}
                          className="flex-1"
                          disabled={sendingMessage}
                        />
                        <Button
                          type="submit"
                          disabled={!newMessage.trim() || sendingMessage}
                          className="self-end"
                        >
                          {sendingMessage ? '...' : 'Send'}
                        </Button>
                      </div>
                    </form>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Ticket Info */}
              <Card>
                <CardHeader>
                  <CardTitle>Ticket Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <div className={`mt-1 px-2 py-1 rounded-full text-xs font-medium inline-block ${getStatusColor(ticket.status)}`}>
                      {ticket.status.replace('_', ' ')}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Priority</label>
                    <div className={`mt-1 px-2 py-1 rounded-full text-xs font-medium inline-block ${getPriorityColor(ticket.priority)}`}>
                      {ticket.priority}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Category</label>
                    <div className="mt-1 text-sm text-gray-900 capitalize">{ticket.category}</div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Created</label>
                    <div className="mt-1 text-sm text-gray-900">
                      {format(new Date(ticket.createdAt), 'MMM d, yyyy h:mm a')}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Last Updated</label>
                    <div className="mt-1 text-sm text-gray-900">
                      {formatDistanceToNow(new Date(ticket.updatedAt), { addSuffix: true })}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Feedback Modal */}
              <AnimatePresence>
                {showFeedback && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                  >
                    <Card>
                      <CardHeader>
                        <CardTitle>Rate Your Experience</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-gray-600 mb-2 block">
                            How satisfied are you with our support?
                          </label>
                          <div className="flex gap-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <button
                                key={star}
                                type="button"
                                onClick={() => setRating(star)}
                                className={`text-2xl ${
                                  star <= rating ? 'text-yellow-400' : 'text-gray-300'
                                } hover:text-yellow-400 transition-colors`}
                              >
                                ⭐
                              </button>
                            ))}
                          </div>
                        </div>
                        
                        <Textarea
                          value={feedback}
                          onChange={(e) => setFeedback(e.target.value)}
                          placeholder="Tell us about your experience (optional)..."
                          rows={3}
                        />
                        
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            onClick={() => setShowFeedback(false)}
                            className="flex-1"
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={submitFeedback}
                            disabled={rating === 0}
                            className="flex-1"
                          >
                            Submit
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </Container>
      </Section>
    </div>
  );
}
