# Ottiq Docker Deployment Script for Windows
# Run this script with PowerShell as Administrator

param(
    [string]$Action = "start",
    [switch]$Build = $false,
    [switch]$Fresh = $false,
    [switch]$Logs = $false,
    [switch]$Help = $false
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput($ForegroundColor, $Message) {
    Write-Host $Message -ForegroundColor $ForegroundColor
}

function Show-Help {
    Write-ColorOutput $Blue "Ottiq Docker Deployment Script"
    Write-Host ""
    Write-Host "Usage: .\deploy.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Action <action>    Action to perform: start, stop, restart, status, clean (default: start)"
    Write-Host "  -Build             Force rebuild of Docker images"
    Write-Host "  -Fresh             Fresh deployment (removes all data)"
    Write-Host "  -Logs              Show logs after starting"
    Write-Host "  -Help              Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\deploy.ps1                          # Start all services"
    Write-Host "  .\deploy.ps1 -Action stop             # Stop all services"
    Write-Host "  .\deploy.ps1 -Build -Logs             # Rebuild and start with logs"
    Write-Host "  .\deploy.ps1 -Fresh                   # Fresh deployment (WARNING: removes all data)"
}

function Test-DockerInstalled {
    try {
        $null = docker --version
        return $true
    }
    catch {
        return $false
    }
}

function Test-DockerRunning {
    try {
        $null = docker ps
        return $true
    }
    catch {
        return $false
    }
}

function Start-Services {
    Write-ColorOutput $Blue "Starting Ottiq services..."
    
    # Copy environment file
    if (Test-Path ".env.docker") {
        Copy-Item ".env.docker" ".env.production"
        Write-ColorOutput $Green "Environment configuration copied"
    }
    else {
        Write-ColorOutput $Yellow "Warning: .env.docker not found, using existing .env"
    }
    
    # Build arguments
    $buildArgs = @()
    if ($Build) {
        $buildArgs += "--build"
        Write-ColorOutput $Yellow "Force rebuilding images..."
    }
    
    # Start services
    $cmd = "docker-compose -f docker-compose.prod.yml up -d"
    if ($buildArgs.Count -gt 0) {
        $cmd += " " + ($buildArgs -join " ")
    }
    
    Invoke-Expression $cmd
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput $Green "Services started successfully!"
        Write-Host ""
        Write-ColorOutput $Blue "Service URLs:"
        Write-Host "  Application:    http://localhost"
        Write-Host "  MinIO Console:  http://localhost:9001"
        Write-Host "  Mailhog UI:     http://localhost:8025"
        Write-Host "  PostgreSQL:     localhost:5432"
        Write-Host "  Redis:          localhost:6379"
        
        # Wait for services to be healthy
        Write-ColorOutput $Yellow "Waiting for services to be healthy..."
        Start-Sleep -Seconds 10
        
        # Run database migrations
        Write-ColorOutput $Blue "Running database migrations..."
        docker-compose -f docker-compose.prod.yml exec -T app npx prisma migrate deploy
        
        if ($Logs) {
            Write-ColorOutput $Blue "Showing logs (Ctrl+C to exit)..."
            docker-compose -f docker-compose.prod.yml logs -f
        }
    }
    else {
        Write-ColorOutput $Red "Failed to start services!"
        exit 1
    }
}

function Stop-Services {
    Write-ColorOutput $Blue "Stopping Ottiq services..."
    docker-compose -f docker-compose.prod.yml down
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput $Green "Services stopped successfully!"
    }
    else {
        Write-ColorOutput $Red "Failed to stop services!"
    }
}

function Restart-Services {
    Write-ColorOutput $Blue "Restarting Ottiq services..."
    Stop-Services
    Start-Sleep -Seconds 5
    Start-Services
}

function Show-Status {
    Write-ColorOutput $Blue "Service Status:"
    docker-compose -f docker-compose.prod.yml ps
}

function Clean-Deployment {
    Write-ColorOutput $Red "WARNING: This will remove all data including database, uploads, and cache!"
    $confirmation = Read-Host "Are you sure? Type 'yes' to continue"
    
    if ($confirmation -eq "yes") {
        Write-ColorOutput $Blue "Cleaning up deployment..."
        docker-compose -f docker-compose.prod.yml down -v --remove-orphans
        docker system prune -f
        Write-ColorOutput $Green "Cleanup completed!"
    }
    else {
        Write-ColorOutput $Yellow "Cleanup cancelled."
    }
}

# Main script execution
if ($Help) {
    Show-Help
    exit 0
}

# Check if Docker is installed and running
if (-not (Test-DockerInstalled)) {
    Write-ColorOutput $Red "Error: Docker is not installed or not in PATH"
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop"
    exit 1
}

if (-not (Test-DockerRunning)) {
    Write-ColorOutput $Red "Error: Docker is not running"
    Write-Host "Please start Docker Desktop and try again"
    exit 1
}

# Handle fresh deployment
if ($Fresh) {
    Clean-Deployment
    $Action = "start"
}

# Execute action
switch ($Action.ToLower()) {
    "start" { Start-Services }
    "stop" { Stop-Services }
    "restart" { Restart-Services }
    "status" { Show-Status }
    "clean" { Clean-Deployment }
    default {
        Write-ColorOutput $Red "Unknown action: $Action"
        Show-Help
        exit 1
    }
}
