#!/bin/bash

# Ottiq Docker Deployment Script
# Cross-platform deployment script for Ottiq

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ACTION="start"
BUILD=false
FRESH=false
LOGS=false

# Functions
print_color() {
    printf "${1}${2}${NC}\n"
}

show_help() {
    print_color $BLUE "Ottiq Docker Deployment Script"
    echo ""
    echo "Usage: ./deploy.sh [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -a, --action <action>    Action to perform: start, stop, restart, status, clean (default: start)"
    echo "  -b, --build             Force rebuild of Docker images"
    echo "  -f, --fresh             Fresh deployment (removes all data)"
    echo "  -l, --logs              Show logs after starting"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./deploy.sh                          # Start all services"
    echo "  ./deploy.sh -a stop                  # Stop all services"
    echo "  ./deploy.sh -b -l                    # Rebuild and start with logs"
    echo "  ./deploy.sh -f                       # Fresh deployment (WARNING: removes all data)"
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        print_color $RED "Error: Docker is not installed or not in PATH"
        echo "Please install Docker from: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! docker ps &> /dev/null; then
        print_color $RED "Error: Docker is not running or you don't have permission"
        echo "Please start Docker and ensure you have proper permissions"
        exit 1
    fi
}

start_services() {
    print_color $BLUE "Starting Ottiq services..."
    
    # Copy environment file
    if [ -f ".env.docker" ]; then
        cp .env.docker .env.production
        print_color $GREEN "Environment configuration copied"
    else
        print_color $YELLOW "Warning: .env.docker not found, using existing .env"
    fi
    
    # Build arguments
    BUILD_ARGS=""
    if [ "$BUILD" = true ]; then
        BUILD_ARGS="--build"
        print_color $YELLOW "Force rebuilding images..."
    fi
    
    # Start services
    docker-compose -f docker-compose.prod.yml up -d $BUILD_ARGS
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "Services started successfully!"
        echo ""
        print_color $BLUE "Service URLs:"
        echo "  Application:    http://localhost"
        echo "  MinIO Console:  http://localhost:9001"
        echo "  Mailhog UI:     http://localhost:8025"
        echo "  PostgreSQL:     localhost:5432"
        echo "  Redis:          localhost:6379"
        
        # Wait for services to be healthy
        print_color $YELLOW "Waiting for services to be healthy..."
        sleep 10
        
        # Run database migrations
        print_color $BLUE "Running database migrations..."
        docker-compose -f docker-compose.prod.yml exec -T app npx prisma migrate deploy
        
        if [ "$LOGS" = true ]; then
            print_color $BLUE "Showing logs (Ctrl+C to exit)..."
            docker-compose -f docker-compose.prod.yml logs -f
        fi
    else
        print_color $RED "Failed to start services!"
        exit 1
    fi
}

stop_services() {
    print_color $BLUE "Stopping Ottiq services..."
    docker-compose -f docker-compose.prod.yml down
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "Services stopped successfully!"
    else
        print_color $RED "Failed to stop services!"
    fi
}

restart_services() {
    print_color $BLUE "Restarting Ottiq services..."
    stop_services
    sleep 5
    start_services
}

show_status() {
    print_color $BLUE "Service Status:"
    docker-compose -f docker-compose.prod.yml ps
}

clean_deployment() {
    print_color $RED "WARNING: This will remove all data including database, uploads, and cache!"
    read -p "Are you sure? Type 'yes' to continue: " confirmation
    
    if [ "$confirmation" = "yes" ]; then
        print_color $BLUE "Cleaning up deployment..."
        docker-compose -f docker-compose.prod.yml down -v --remove-orphans
        docker system prune -f
        print_color $GREEN "Cleanup completed!"
    else
        print_color $YELLOW "Cleanup cancelled."
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--action)
            ACTION="$2"
            shift 2
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -f|--fresh)
            FRESH=true
            shift
            ;;
        -l|--logs)
            LOGS=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main script execution
check_docker

# Handle fresh deployment
if [ "$FRESH" = true ]; then
    clean_deployment
    ACTION="start"
fi

# Execute action
case $ACTION in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    clean)
        clean_deployment
        ;;
    *)
        print_color $RED "Unknown action: $ACTION"
        show_help
        exit 1
        ;;
esac
