import { Page, expect } from '@playwright/test';

export class TryOnHelpers {
  constructor(private page: Page) {}

  /**
   * Navigate to try-on experience
   */
  async goToTryOn() {
    await this.page.goto('/try-on');
    await this.page.waitForLoadState('networkidle');
    return this;
  }

  /**
   * Upload user photo for try-on
   */
  async uploadUserPhoto(imagePath: string = 'test-assets/user-photo.jpg') {
    // Look for upload area with emotional messaging
    const uploadArea = this.page.getByTestId('photo-upload-area');
    await expect(uploadArea).toBeVisible();
    
    // Verify emotional copy is present
    const emotionalText = this.page.getByText(/see yourself|imagine wearing|bring your vision to life/i);
    await expect(emotionalText).toBeVisible();
    
    // Set up file chooser
    const fileChooserPromise = this.page.waitForEvent('filechooser');
    await uploadArea.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(imagePath);
    
    // Wait for photo processing
    await this.page.waitForSelector('[data-testid="photo-processing"]', { timeout: 30000 });
    await expect(this.page.getByText(/processing|analyzing|preparing/i)).toBeVisible();
    
    return this;
  }

  /**
   * Wait for AI processing to complete
   */
  async waitForAIProcessing() {
    // Show loading state with encouraging messages
    const loadingMessages = [
      /creating your look/i,
      /applying your design/i,
      /almost ready/i,
      /finalizing/i
    ];
    
    // Wait for processing to start
    await expect(this.page.getByTestId('ai-processing')).toBeVisible();
    
    // Check for encouraging loading messages
    let messageFound = false;
    for (const messagePattern of loadingMessages) {
      const message = this.page.getByText(messagePattern);
      if (await message.isVisible()) {
        messageFound = true;
        break;
      }
    }
    expect(messageFound).toBeTruthy();
    
    // Wait for processing to complete (up to 60 seconds)
    await this.page.waitForSelector('[data-testid="tryon-result"]', { timeout: 60000 });
    
    return this;
  }

  /**
   * Verify try-on result quality
   */
  async verifyTryOnResult() {
    const result = this.page.getByTestId('tryon-result');
    await expect(result).toBeVisible();
    
    // Check for result image
    const resultImage = result.getByRole('img');
    await expect(resultImage).toBeVisible();
    
    // Verify image has loaded properly
    const imageSrc = await resultImage.getAttribute('src');
    expect(imageSrc).toBeTruthy();
    expect(imageSrc).not.toContain('placeholder');
    
    // Check for quality indicators
    const qualityIndicator = this.page.getByTestId('result-quality');
    if (await qualityIndicator.isVisible()) {
      const qualityText = await qualityIndicator.textContent();
      expect(qualityText).toMatch(/high|good|excellent/i);
    }
    
    return this;
  }

  /**
   * Test different angles/poses
   */
  async testDifferentAngles() {
    const angleButtons = this.page.getByTestId(/angle-/);
    const angleCount = await angleButtons.count();
    
    if (angleCount > 0) {
      // Test front view
      const frontView = this.page.getByTestId('angle-front');
      if (await frontView.isVisible()) {
        await frontView.click();
        await this.page.waitForTimeout(2000);
        await this.verifyTryOnResult();
      }
      
      // Test side view if available
      const sideView = this.page.getByTestId('angle-side');
      if (await sideView.isVisible()) {
        await sideView.click();
        await this.page.waitForTimeout(2000);
        await this.verifyTryOnResult();
      }
    }
    
    return this;
  }

  /**
   * Share try-on result
   */
  async shareResult(platform: 'social' | 'email' | 'link' = 'social') {
    const shareButton = this.page.getByRole('button', { name: /share|show off|tell friends/i });
    await expect(shareButton).toBeVisible();
    await shareButton.click();
    
    // Select sharing platform
    const platformButton = this.page.getByTestId(`share-${platform}`);
    if (await platformButton.isVisible()) {
      await platformButton.click();
      
      // Verify share modal or redirect
      if (platform === 'link') {
        const shareLink = this.page.getByTestId('share-link');
        await expect(shareLink).toBeVisible();
        const linkValue = await shareLink.inputValue();
        expect(linkValue).toContain('ottiq.com');
      }
    }
    
    return this;
  }

  /**
   * Save try-on result to profile
   */
  async saveToProfile() {
    const saveButton = this.page.getByRole('button', { name: /save|keep|add to wardrobe/i });
    await expect(saveButton).toBeVisible();
    await saveButton.click();
    
    // Wait for save confirmation
    await expect(this.page.getByText(/saved|added|kept/i)).toBeVisible();
    
    return this;
  }

  /**
   * Proceed to checkout from try-on
   */
  async proceedToCheckout() {
    const checkoutButton = this.page.getByRole('button', { name: /buy now|get this look|make it mine/i });
    await expect(checkoutButton).toBeVisible();
    await checkoutButton.click();
    
    // Wait for checkout page
    await this.page.waitForLoadState('networkidle');
    
    return this;
  }

  /**
   * Test fallback experience when AI fails
   */
  async testFallbackExperience() {
    // Simulate AI failure by intercepting API calls
    await this.page.route('**/api/ai/tryon', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'AI service unavailable' })
      });
    });
    
    await this.uploadUserPhoto();
    
    // Should show fallback experience
    const fallbackMessage = this.page.getByText(/temporarily unavailable|try again|alternative/i);
    await expect(fallbackMessage).toBeVisible();
    
    // Should offer alternative options
    const alternativeOptions = this.page.getByTestId('fallback-options');
    await expect(alternativeOptions).toBeVisible();
    
    return this;
  }

  /**
   * Verify emotional engagement elements
   */
  async verifyEmotionalEngagement() {
    // Check for excitement-building copy
    const excitementText = this.page.getByText(/amazing|stunning|perfect|incredible|love it/i);
    await expect(excitementText).toBeVisible();
    
    // Check for personalization elements
    const personalizationText = this.page.getByText(/your style|made for you|uniquely yours/i);
    await expect(personalizationText).toBeVisible();
    
    // Check for social validation
    const socialElements = this.page.getByText(/others love|popular|trending/i);
    // Social elements are optional
    
    return this;
  }

  /**
   * Test mobile try-on experience
   */
  async testMobileTryOnExperience() {
    await this.page.setViewportSize({ width: 375, height: 667 });
    
    // Verify mobile-optimized upload area
    const uploadArea = this.page.getByTestId('photo-upload-area');
    await expect(uploadArea).toBeVisible();
    
    const uploadBox = await uploadArea.boundingBox();
    if (uploadBox) {
      expect(uploadBox.height).toBeGreaterThanOrEqual(120); // Large enough for mobile
    }
    
    // Test touch interactions
    await uploadArea.tap();
    
    // Verify mobile-friendly result display
    const result = this.page.getByTestId('tryon-result');
    if (await result.isVisible()) {
      const resultBox = await result.boundingBox();
      if (resultBox) {
        expect(resultBox.width).toBeLessThanOrEqual(375); // Fits mobile screen
      }
    }
    
    return this;
  }

  /**
   * Test accessibility of try-on interface
   */
  async testTryOnAccessibility() {
    // Check for proper alt text on images
    const images = this.page.getByRole('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const image = images.nth(i);
      const altText = await image.getAttribute('alt');
      expect(altText).toBeTruthy();
      expect(altText?.length).toBeGreaterThan(0);
    }
    
    // Check for proper button labels
    const buttons = this.page.getByRole('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < buttonCount; i++) {
      const button = buttons.nth(i);
      const accessibleName = await button.getAttribute('aria-label') || await button.textContent();
      expect(accessibleName?.trim()).toBeTruthy();
    }
    
    // Check for loading state announcements
    const loadingAnnouncement = this.page.getByRole('status');
    if (await loadingAnnouncement.isVisible()) {
      const announcementText = await loadingAnnouncement.textContent();
      expect(announcementText).toBeTruthy();
    }
    
    return this;
  }
}
