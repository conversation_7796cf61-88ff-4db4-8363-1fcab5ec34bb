/**
 * Support Ticket Resolved Email Template (Customer Notification)
 * Notifies customers when their support ticket has been resolved
 */

import { Section, Text, Img, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { SupportTicketResolvedEmailData } from '../../lib/services/email';

interface SupportTicketResolvedEmailProps extends SupportTicketResolvedEmailData {}

export function SupportTicketResolvedEmail({
  customerName,
  ticketId,
  subject,
  resolution,
  ticketUrl,
}: SupportTicketResolvedEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText={`Great news! Your support ticket has been resolved: ${subject}`}>
      {/* Hero Section */}
      <Section className="text-center mb-8">
        <Img
          src={`${baseUrl}/images/email-hero-support-resolved.jpg`}
          alt="Support ticket resolved - Issue solved!"
          width="600"
          height="200"
          className="w-full rounded-lg mb-6"
        />
        
        <EmailHeading level={1}>
          ✅ Great News, {customerName}!
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Your support ticket has been resolved! We hope our solution helps you get back to creating amazing designs.
        </EmailText>
      </Section>

      {/* Ticket Info */}
      <Section className="bg-green-50 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-center mb-4">
          <div className="bg-green-500 text-white rounded-full w-12 h-12 flex items-center justify-center text-2xl">
            ✓
          </div>
        </div>

        <div className="text-center mb-4">
          <Text className="text-sm font-medium text-gray-600">Ticket</Text>
          <Text className="text-lg font-mono text-gray-900">#{ticketId.slice(-8)}</Text>
        </div>

        <div className="text-center">
          <Text className="text-sm font-medium text-gray-600 mb-1">Subject</Text>
          <Text className="text-lg font-medium text-gray-900">{subject}</Text>
        </div>
      </Section>

      {/* Resolution */}
      <Section className="bg-white border-l-4 border-green-500 pl-6 py-4 mb-6">
        <EmailHeading level={3} className="text-gray-900 mb-3">
          🔧 How We Solved It
        </EmailHeading>
        
        <EmailText className="text-gray-700 leading-relaxed whitespace-pre-wrap">
          {resolution}
        </EmailText>
      </Section>

      {/* Action Buttons */}
      <Section className="text-center mb-8">
        <EmailButton href={ticketUrl} className="mb-4">
          View Full Resolution
        </EmailButton>
        
        <EmailText className="text-gray-600 text-sm">
          Click above to see the complete conversation and solution details
        </EmailText>
      </Section>

      {/* Feedback Request */}
      <Section className="bg-gradient-to-r from-warm-500 to-amber-500 text-white rounded-lg p-6 mb-6">
        <EmailHeading level={3} className="text-white mb-3">
          💭 How Did We Do?
        </EmailHeading>
        
        <EmailText className="text-white/90 mb-4">
          Your feedback helps us improve our support experience. Please take a moment to rate your experience with our support team.
        </EmailText>

        <div className="text-center">
          <EmailButton 
            href={`${ticketUrl}?feedback=true`}
            className="bg-white text-warm-600 hover:bg-gray-100"
          >
            Rate Your Experience
          </EmailButton>
        </div>
      </Section>

      {/* Still Need Help */}
      <Section className="bg-gray-50 rounded-lg p-4 mb-6">
        <EmailHeading level={4} className="text-gray-900 mb-3">
          Still Need Help?
        </EmailHeading>
        
        <EmailText className="text-gray-700 mb-3">
          If this solution doesn't fully resolve your issue or if you have any follow-up questions, don't hesitate to reach out!
        </EmailText>

        <div className="space-y-2">
          <EmailText className="text-gray-700 text-sm">
            💬 <strong>Reply to this ticket:</strong> Simply respond to continue the conversation
          </EmailText>
          <EmailText className="text-gray-700 text-sm">
            🎫 <strong>New issue?</strong> <a href={`${baseUrl}/support`} className="text-warm-600 underline">Create a new ticket</a>
          </EmailText>
          <EmailText className="text-gray-700 text-sm">
            📚 <strong>Browse solutions:</strong> Check our <a href={`${baseUrl}/help`} className="text-warm-600 underline">Help Center</a>
          </EmailText>
        </div>
      </Section>

      {/* Thank You */}
      <Section className="text-center mb-6">
        <EmailHeading level={3} className="text-gray-900 mb-3">
          🙏 Thank You for Your Patience
        </EmailHeading>
        
        <EmailText className="text-gray-700">
          We appreciate you giving us the opportunity to help. Your trust in Ottiq means everything to us, and we're always here when you need support.
        </EmailText>
      </Section>

      <Hr className="my-6" />

      {/* Footer */}
      <Section className="text-center">
        <EmailText className="text-gray-500 text-sm">
          This email was sent regarding your support ticket #{ticketId.slice(-8)}.
          <br />
          If you have any concerns about this resolution, please don't hesitate to contact us.
        </EmailText>
      </Section>
    </EmailLayout>
  );
}
