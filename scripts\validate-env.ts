#!/usr/bin/env tsx

/**
 * Environment Validation Script for Ottiq
 * 
 * Validates all required environment variables before deployment
 * and provides detailed feedback on configuration issues.
 */

import { validateEnvironment, getSanitizedConfig, isProduction } from '../src/lib/security/env';

function main() {
  console.log('🔍 Validating Ottiq environment configuration...\n');
  
  const validation = validateEnvironment();
  
  if (validation.success) {
    console.log('✅ Environment validation successful!');
    
    if (validation.config) {
      console.log('\n📋 Configuration Summary:');
      console.log(`   Environment: ${validation.config.NODE_ENV}`);
      console.log(`   App URL: ${validation.config.APP_URL}`);
      console.log(`   Database: ${validation.config.DATABASE_URL ? '✓ Configured' : '✗ Missing'}`);
      console.log(`   Redis: ${validation.config.REDIS_URL ? '✓ Configured' : '✗ Missing'}`);
      console.log(`   NextAuth: ${validation.config.NEXTAUTH_SECRET ? '✓ Configured' : '✗ Missing'}`);
      console.log(`   MinIO: ${validation.config.MINIO_ENDPOINT ? '✓ Configured' : '✗ Missing'}`);
      console.log(`   bKash: ${validation.config.BKASH_APP_KEY ? '✓ Configured' : '✗ Missing'}`);
      console.log(`   AI Services: ${validation.config.HUGGING_FACE_API_KEY ? '✓ Configured' : '✗ Missing'}`);
      
      // Production-specific checks
      if (isProduction()) {
        console.log('\n🔒 Production Security Checks:');
        console.log(`   HTTPS Enforced: ${validation.config.APP_URL.startsWith('https://') ? '✓' : '✗'}`);
        console.log(`   Sandbox Mode: ${validation.config.BKASH_SANDBOX ? '✗ Enabled (should be disabled)' : '✓ Disabled'}`);
        console.log(`   Strong Secrets: ✓ Validated`);
      }
    }
    
    console.log('\n🚀 Ready for deployment!');
    process.exit(0);
  } else {
    console.log('❌ Environment validation failed!\n');
    
    if (validation.errors) {
      console.log('🔧 Issues found:');
      validation.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
    
    console.log('\n💡 Tips:');
    console.log('   - Copy .env.example to .env and fill in the values');
    console.log('   - Check the documentation for required environment variables');
    console.log('   - Ensure all secrets are properly configured for production');
    
    if (isProduction()) {
      console.log('\n🚨 Production deployment blocked due to configuration errors!');
      process.exit(1);
    } else {
      console.log('\n⚠️  Development mode - continuing with warnings');
      process.exit(0);
    }
  }
}

// Run the validation
main();
