/**
 * Email Testing API Route
 * Allows testing email templates and delivery (admin only)
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { emailService, EmailType } from '../../../../lib/services/email';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// Test email request schema
const TestEmailSchema = z.object({
  type: z.enum([
    'order-confirmation',
    'order-shipped',
    'order-delivered',
    'order-cancelled',
    'payment-confirmation',
    'payment-failed',
    'ai-tryon-ready',
    'welcome',
    'password-reset',
  ]),
  recipient: z.string().email(),
  useTestData: z.boolean().default(true),
  customData: z.object({}).passthrough().optional(),
});

/**
 * POST /api/emails/test - Send test email (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        {
          success: false,
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'ADMIN_ACCESS_REQUIRED',
          message: 'Admin access required',
        },
        { status: 403 }
      );
    }

    // Check if email service is available
    if (!emailService.isAvailable()) {
      return NextResponse.json(
        {
          success: false,
          error: 'EMAIL_SERVICE_UNAVAILABLE',
          message: 'Email service is not configured or unavailable',
        },
        { status: 503 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = TestEmailSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'INVALID_REQUEST',
          message: 'Invalid request data',
          details: validationResult.error.issues.map((issue) => ({
            field: issue.path.join('.'),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    const { type, recipient, useTestData, customData } = validationResult.data;

    // Generate test data or use custom data
    const emailData = useTestData 
      ? generateTestEmailData(type as EmailType, recipient)
      : { to: recipient, ...customData };

    // Send test email
    const result = await emailService.sendEmail(
      type as EmailType,
      emailData,
      { priority: 'normal' }
    );

    if (!result.success) {
      console.error('Test email sending failed:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: 'EMAIL_SEND_FAILED',
          message: result.error || 'Failed to send test email',
        },
        { status: 500 }
      );
    }

    // Log successful test email send
    console.log('Test email sent successfully:', {
      type,
      recipient,
      messageId: result.messageId,
      sentBy: session.user.email,
    });

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully',
      messageId: result.messageId,
      type,
      recipient,
    });

  } catch (error) {
    console.error('Test email API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/emails/test - Get available test email types
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        {
          success: false,
          error: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email)) {
      return NextResponse.json(
        {
          success: false,
          error: 'ADMIN_ACCESS_REQUIRED',
          message: 'Admin access required',
        },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      availableTypes: [
        {
          type: 'order-confirmation',
          description: 'Order confirmation email with order details',
          requiresData: ['orderNumber', 'items', 'shippingAddress'],
        },
        {
          type: 'order-shipped',
          description: 'Order shipped notification with tracking',
          requiresData: ['orderNumber', 'trackingNumber'],
        },
        {
          type: 'order-delivered',
          description: 'Order delivered confirmation',
          requiresData: ['orderNumber', 'items'],
        },
        {
          type: 'order-cancelled',
          description: 'Order cancellation notification',
          requiresData: ['orderNumber', 'cancellationReason'],
        },
        {
          type: 'payment-confirmation',
          description: 'Payment confirmation email',
          requiresData: ['orderNumber', 'amount', 'paymentMethod'],
        },
        {
          type: 'payment-failed',
          description: 'Payment failure notification',
          requiresData: ['orderNumber', 'amount', 'failureReason'],
        },
        {
          type: 'ai-tryon-ready',
          description: 'AI try-on result notification',
          requiresData: ['productName', 'tryOnImageUrl', 'customizationName'],
        },
        {
          type: 'welcome',
          description: 'Welcome email for new users',
          requiresData: [],
        },
        {
          type: 'password-reset',
          description: 'Password reset email',
          requiresData: ['resetUrl', 'expiresIn'],
        },
      ],
      emailServiceStatus: {
        available: emailService.isAvailable(),
        connectionValid: emailService.isAvailable() ? await emailService.verifyConnection() : false,
      },
    });

  } catch (error) {
    console.error('Test email info API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * Generate test data for different email types
 */
function generateTestEmailData(type: EmailType, recipient: string): any {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';
  
  const baseData = {
    to: recipient,
    customerName: 'Test Customer',
  };

  switch (type) {
    case 'order-confirmation':
    case 'order-shipped':
    case 'order-delivered':
    case 'order-cancelled':
      return {
        ...baseData,
        orderNumber: 'TEST-' + Date.now(),
        orderDate: new Date().toLocaleDateString(),
        totalAmount: '2500.00',
        currency: 'BDT',
        items: [
          {
            name: 'Custom T-Shirt',
            quantity: 1,
            price: '1500.00',
            customization: {
              name: 'Bold Streetwear Design',
              previewImage: `${baseUrl}/images/test-design.jpg`,
            },
          },
          {
            name: 'Custom Hoodie',
            quantity: 1,
            price: '1000.00',
            customization: {
              name: 'Minimalist Logo',
              previewImage: `${baseUrl}/images/test-design-2.jpg`,
            },
          },
        ],
        shippingAddress: {
          name: 'Test Customer',
          address: '123 Test Street, Apartment 4B',
          city: 'Dhaka',
          postalCode: '1000',
          country: 'Bangladesh',
        },
        trackingNumber: type === 'order-shipped' ? 'TRK123456789' : undefined,
        estimatedDelivery: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString(),
        orderUrl: `${baseUrl}/orders/test-order-id`,
        cancellationReason: type === 'order-cancelled' ? 'Customer requested cancellation' : undefined,
      };

    case 'payment-confirmation':
    case 'payment-failed':
      return {
        ...baseData,
        orderNumber: 'TEST-' + Date.now(),
        amount: '2500.00',
        currency: 'BDT',
        paymentMethod: 'bkash',
        transactionId: 'TXN' + Date.now(),
        orderUrl: `${baseUrl}/orders/test-order-id`,
        failureReason: type === 'payment-failed' ? 'Insufficient funds' : undefined,
      };

    case 'ai-tryon-ready':
      return {
        ...baseData,
        productName: 'Custom T-Shirt',
        tryOnImageUrl: `${baseUrl}/images/test-tryon-result.jpg`,
        customizationName: 'Bold Streetwear Design',
        orderUrl: `${baseUrl}/create?design=test-design`,
      };

    case 'welcome':
      return baseData;

    case 'password-reset':
      return {
        ...baseData,
        resetUrl: `${baseUrl}/reset-password?token=test-token-123`,
        expiresIn: '1 hour',
      };

    default:
      return baseData;
  }
}
