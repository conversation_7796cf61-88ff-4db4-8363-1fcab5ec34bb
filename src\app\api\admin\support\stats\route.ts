/**
 * Admin Support Statistics API Route
 * Provides comprehensive support statistics for admin dashboard
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

/**
 * GET /api/admin/support/stats - Get comprehensive support statistics (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get comprehensive ticket statistics
    const [
      totalTickets,
      openTickets,
      inProgressTickets,
      waitingForCustomerTickets,
      waitingForAdminTickets,
      resolvedTickets,
      closedTickets,
      cancelledTickets
    ] = await Promise.all([
      // Total tickets
      prisma.supportTicket.count(),
      
      // Open tickets
      prisma.supportTicket.count({
        where: { status: 'OPEN' }
      }),
      
      // In progress tickets
      prisma.supportTicket.count({
        where: { status: 'IN_PROGRESS' }
      }),
      
      // Waiting for customer
      prisma.supportTicket.count({
        where: { status: 'WAITING_FOR_CUSTOMER' }
      }),
      
      // Waiting for admin
      prisma.supportTicket.count({
        where: { status: 'WAITING_FOR_ADMIN' }
      }),
      
      // Resolved tickets
      prisma.supportTicket.count({
        where: { status: 'RESOLVED' }
      }),
      
      // Closed tickets
      prisma.supportTicket.count({
        where: { status: 'CLOSED' }
      }),
      
      // Cancelled tickets
      prisma.supportTicket.count({
        where: { status: 'CANCELLED' }
      })
    ]);

    // Calculate response time statistics
    const recentResolvedTickets = await prisma.supportTicket.findMany({
      where: {
        status: {
          in: ['RESOLVED', 'CLOSED']
        },
        resolvedAt: {
          not: null
        }
      },
      select: {
        createdAt: true,
        resolvedAt: true,
        messages: {
          where: {
            senderRole: 'ADMIN'
          },
          select: {
            createdAt: true
          },
          orderBy: {
            createdAt: 'asc'
          },
          take: 1
        }
      },
      orderBy: {
        resolvedAt: 'desc'
      },
      take: 100 // Last 100 resolved tickets
    });

    // Calculate average response time
    let averageResponseTime = 'N/A';
    
    if (recentResolvedTickets.length > 0) {
      const responseTimes = recentResolvedTickets
        .filter(ticket => ticket.messages.length > 0)
        .map(ticket => {
          const createdAt = new Date(ticket.createdAt);
          const firstResponseAt = new Date(ticket.messages[0].createdAt);
          return firstResponseAt.getTime() - createdAt.getTime();
        });

      if (responseTimes.length > 0) {
        const avgMs = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
        const avgHours = Math.round(avgMs / (1000 * 60 * 60));
        
        if (avgHours < 1) {
          const avgMinutes = Math.round(avgMs / (1000 * 60));
          averageResponseTime = `${avgMinutes}m`;
        } else if (avgHours < 24) {
          averageResponseTime = `${avgHours}h`;
        } else {
          const avgDays = Math.round(avgHours / 24);
          averageResponseTime = `${avgDays}d`;
        }
      }
    }

    // Get category breakdown
    const categoryStats = await prisma.supportTicket.groupBy({
      by: ['category'],
      _count: {
        category: true
      }
    });

    // Get priority breakdown
    const priorityStats = await prisma.supportTicket.groupBy({
      by: ['priority'],
      _count: {
        priority: true
      }
    });

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentActivity = await prisma.supportTicket.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    // Get daily ticket creation for the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const dailyTickets = await prisma.supportTicket.findMany({
      where: {
        createdAt: {
          gte: sevenDaysAgo
        }
      },
      select: {
        createdAt: true
      }
    });

    // Group by day
    const dailyStats = dailyTickets.reduce((acc, ticket) => {
      const date = new Date(ticket.createdAt).toDateString();
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get satisfaction ratings
    const satisfactionStats = await prisma.supportTicket.aggregate({
      _avg: {
        satisfactionRating: true
      },
      _count: {
        satisfactionRating: true
      },
      where: {
        satisfactionRating: {
          not: null
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        totalTickets,
        openTickets: openTickets + inProgressTickets + waitingForCustomerTickets + waitingForAdminTickets,
        resolvedTickets,
        closedTickets,
        averageResponseTime,
        recentActivity,
        byStatus: {
          OPEN: openTickets,
          IN_PROGRESS: inProgressTickets,
          WAITING_FOR_CUSTOMER: waitingForCustomerTickets,
          WAITING_FOR_ADMIN: waitingForAdminTickets,
          RESOLVED: resolvedTickets,
          CLOSED: closedTickets,
          CANCELLED: cancelledTickets,
        },
        byPriority: priorityStats.reduce((acc, stat) => {
          acc[stat.priority] = stat._count.priority;
          return acc;
        }, {} as Record<string, number>),
        byCategory: categoryStats.reduce((acc, stat) => {
          acc[stat.category || 'general'] = stat._count.category;
          return acc;
        }, {} as Record<string, number>),
        dailyStats,
        satisfaction: {
          averageRating: satisfactionStats._avg.satisfactionRating || 0,
          totalRatings: satisfactionStats._count.satisfactionRating || 0,
        },
      }
    });
  } catch (error) {
    console.error('Error fetching admin support stats:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch support statistics',
      },
      { status: 500 }
    );
  }
}
