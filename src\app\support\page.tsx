'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { SupportTicketList } from '@/components/support/SupportTicketList';
import { CreateTicketModal } from '@/components/support/CreateTicketModal';
import { SupportChatBot } from '@/components/support/SupportChatBot';

interface SupportStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  averageResponseTime: string;
}

export default function SupportPage() {
  const { data: session, status } = useSession();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showChatBot, setShowChatBot] = useState(false);
  const [stats, setStats] = useState<SupportStats | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch user's support stats
  useEffect(() => {
    if (session?.user) {
      fetchSupportStats();
    }
  }, [session, refreshTrigger]);

  const fetchSupportStats = async () => {
    try {
      const response = await fetch('/api/support/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching support stats:', error);
    }
  };

  const handleTicketCreated = () => {
    setShowCreateModal(false);
    setRefreshTrigger(prev => prev + 1);
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-warm-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading support center...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
        <Section variant="primary" padding="lg">
          <Container>
            <div className="text-center py-16">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Support Center
              </h1>
              <p className="text-xl text-gray-600 mb-8">
                Please sign in to access support and view your tickets
              </p>
              <Button onClick={() => window.location.href = '/auth/signin'}>
                Sign In to Continue
              </Button>
            </div>
          </Container>
        </Section>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50">
      <Section variant="primary" padding="lg">
        <Container>
          {/* Header */}
          <div className="text-center mb-12">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl font-bold text-gray-900 mb-4"
            >
              💬 Support Center
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-600"
            >
              We're here to help you create amazing designs! Get support, track tickets, or chat with our AI assistant.
            </motion.p>
          </div>

          {/* Quick Actions */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
          >
            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setShowCreateModal(true)}>
              <CardHeader className="text-center">
                <div className="text-4xl mb-2">🎫</div>
                <CardTitle>Create Ticket</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-center">
                  Need help with an order, product, or technical issue? Create a support ticket.
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setShowChatBot(true)}>
              <CardHeader className="text-center">
                <div className="text-4xl mb-2">🤖</div>
                <CardTitle>AI Assistant</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-center">
                  Get instant answers to common questions with our AI-powered assistant.
                </p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => window.location.href = '/help'}>
              <CardHeader className="text-center">
                <div className="text-4xl mb-2">📚</div>
                <CardTitle>Help Center</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-center">
                  Browse our comprehensive help articles and tutorials.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Support Stats */}
          {stats && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"
            >
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-warm-600">{stats.totalTickets}</div>
                <div className="text-sm text-gray-600">Total Tickets</div>
              </div>
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.openTickets}</div>
                <div className="text-sm text-gray-600">Open Tickets</div>
              </div>
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.resolvedTickets}</div>
                <div className="text-sm text-gray-600">Resolved</div>
              </div>
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.averageResponseTime}</div>
                <div className="text-sm text-gray-600">Avg Response</div>
              </div>
            </motion.div>
          )}

          {/* Support Tickets List */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Your Support Tickets</h2>
              <Button onClick={() => setShowCreateModal(true)}>
                ✨ New Ticket
              </Button>
            </div>

            <SupportTicketList 
              refreshTrigger={refreshTrigger}
              onRefresh={handleRefresh}
            />
          </motion.div>
        </Container>
      </Section>

      {/* Create Ticket Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <CreateTicketModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            onTicketCreated={handleTicketCreated}
          />
        )}
      </AnimatePresence>

      {/* AI ChatBot Modal */}
      <AnimatePresence>
        {showChatBot && (
          <SupportChatBot
            isOpen={showChatBot}
            onClose={() => setShowChatBot(false)}
            onCreateTicket={() => {
              setShowChatBot(false);
              setShowCreateModal(true);
            }}
          />
        )}
      </AnimatePresence>

      {/* Floating Chat Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1 }}
        onClick={() => setShowChatBot(true)}
        className="fixed bottom-6 right-6 bg-warm-500 hover:bg-warm-600 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg transition-colors z-40"
      >
        💬
      </motion.button>
    </div>
  );
}
