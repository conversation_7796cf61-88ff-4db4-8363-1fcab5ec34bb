/**
 * Lazy Loading Utilities for Ottiq
 * 
 * Provides intelligent lazy loading for heavy components like the customization
 * editor and AI try-on functionality to ensure instant visual feedback and
 * optimal performance for emotional user experience.
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react';
import { <PERSON><PERSON><PERSON>anager, generate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/cache';

// Lazy loading configuration
export const LAZY_CONFIG = {
  // Preload timing (in milliseconds)
  PRELOAD_DELAY: {
    EDITOR: 2000, // Preload editor after 2 seconds
    AI_TRYON: 3000, // Preload AI try-on after 3 seconds
    TEMPLATES: 1000, // Preload templates after 1 second
    ADMIN_TOOLS: 5000, // Preload admin tools after 5 seconds
  },
  
  // Intersection observer settings
  INTERSECTION_OPTIONS: {
    rootMargin: '50px', // Start loading 50px before element is visible
    threshold: 0.1, // Trigger when 10% of element is visible
  },
  
  // Performance settings
  MAX_CONCURRENT_LOADS: 3, // Limit concurrent component loads
  RETRY_ATTEMPTS: 3, // Retry failed loads
  RETRY_DELAY: 1000, // Delay between retries
} as const;

/**
 * Enhanced lazy loading with preloading and error handling
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: {
    name: string;
    preload?: boolean;
    preloadDelay?: number;
    fallback?: ComponentType;
    onError?: (error: Error) => void;
  }
): LazyExoticComponent<T> {
  const { name, preload = false, preloadDelay = 0, onError } = options;
  
  // Create lazy component with error handling
  const LazyComponent = lazy(async () => {
    try {
      console.log(`Loading component: ${name}`);
      const startTime = Date.now();
      
      const module = await importFn();
      
      const loadTime = Date.now() - startTime;
      console.log(`Component ${name} loaded in ${loadTime}ms`);
      
      // Cache successful load for analytics
      const cacheKey = generateCacheKey('COMPUTATION', 'component-load', { name });
      await CacheManager.set(cacheKey, { loadTime, timestamp: Date.now() }, 3600);
      
      return module;
    } catch (error) {
      console.error(`Failed to load component ${name}:`, error);
      if (onError) {
        onError(error as Error);
      }
      throw error;
    }
  });
  
  // Set up preloading if requested
  if (preload && typeof window !== 'undefined') {
    setTimeout(() => {
      console.log(`Preloading component: ${name}`);
      importFn().catch(error => {
        console.warn(`Preload failed for ${name}:`, error);
      });
    }, preloadDelay);
  }
  
  return LazyComponent;
}

/**
 * Lazy-loaded components for Ottiq
 */
export const LazyComponents = {
  // Editor components
  CustomizationEditor: createLazyComponent(
    () => import('@/components/editor/CustomizationEditor'),
    {
      name: 'CustomizationEditor',
      preload: true,
      preloadDelay: LAZY_CONFIG.PRELOAD_DELAY.EDITOR,
    }
  ),
  
  TemplateGallery: createLazyComponent(
    () => import('@/components/editor/TemplateGallery'),
    {
      name: 'TemplateGallery',
      preload: true,
      preloadDelay: LAZY_CONFIG.PRELOAD_DELAY.TEMPLATES,
    }
  ),
  
  DesignCanvas: createLazyComponent(
    () => import('@/components/editor/DesignCanvas'),
    {
      name: 'DesignCanvas',
      preload: true,
      preloadDelay: LAZY_CONFIG.PRELOAD_DELAY.EDITOR,
    }
  ),
  
  // AI Try-On components
  AiTryOnDemo: createLazyComponent(
    () => import('@/components/AiTryOnDemo'),
    {
      name: 'AiTryOnDemo',
      preload: false, // Only load when needed
      preloadDelay: LAZY_CONFIG.PRELOAD_DELAY.AI_TRYON,
    }
  ),
  
  AiTryOnProductsTab: createLazyComponent(
    () => import('@/components/admin/AiTryOnProductsTab'),
    {
      name: 'AiTryOnProductsTab',
      preload: false,
    }
  ),
  
  AiTryOnAnalyticsTab: createLazyComponent(
    () => import('@/components/admin/AiTryOnAnalyticsTab'),
    {
      name: 'AiTryOnAnalyticsTab',
      preload: false,
    }
  ),
  
  // Admin components
  PricingEngineAdmin: createLazyComponent(
    () => import('@/components/admin/PricingEngineAdmin'),
    {
      name: 'PricingEngineAdmin',
      preload: false,
      preloadDelay: LAZY_CONFIG.PRELOAD_DELAY.ADMIN_TOOLS,
    }
  ),
} as const;

/**
 * Intersection Observer based lazy loading
 */
export class IntersectionLazyLoader {
  private observer: IntersectionObserver | null = null;
  private loadingQueue: Set<string> = new Set();
  private loadedComponents: Set<string> = new Set();
  
  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        LAZY_CONFIG.INTERSECTION_OPTIONS
      );
    }
  }
  
  private async handleIntersection(entries: IntersectionObserverEntry[]) {
    for (const entry of entries) {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        const componentName = element.dataset.lazyComponent;
        
        if (componentName && !this.loadedComponents.has(componentName)) {
          await this.loadComponent(componentName, element);
          this.observer?.unobserve(element);
        }
      }
    }
  }
  
  private async loadComponent(componentName: string, element: HTMLElement) {
    if (this.loadingQueue.has(componentName)) {
      return; // Already loading
    }
    
    // Limit concurrent loads
    if (this.loadingQueue.size >= LAZY_CONFIG.MAX_CONCURRENT_LOADS) {
      setTimeout(() => this.loadComponent(componentName, element), 100);
      return;
    }
    
    this.loadingQueue.add(componentName);
    
    try {
      // Add loading indicator
      element.classList.add('lazy-loading');
      
      // Simulate component loading (in real implementation, this would trigger React lazy loading)
      console.log(`Lazy loading component: ${componentName}`);
      
      // Remove loading indicator
      element.classList.remove('lazy-loading');
      element.classList.add('lazy-loaded');
      
      this.loadedComponents.add(componentName);
    } catch (error) {
      console.error(`Failed to lazy load ${componentName}:`, error);
      element.classList.add('lazy-error');
    } finally {
      this.loadingQueue.delete(componentName);
    }
  }
  
  /**
   * Observe element for lazy loading
   */
  observe(element: HTMLElement, componentName: string) {
    if (this.observer) {
      element.dataset.lazyComponent = componentName;
      this.observer.observe(element);
    }
  }
  
  /**
   * Stop observing element
   */
  unobserve(element: HTMLElement) {
    if (this.observer) {
      this.observer.unobserve(element);
    }
  }
  
  /**
   * Preload component
   */
  async preload(componentName: string) {
    if (this.loadedComponents.has(componentName)) {
      return;
    }
    
    console.log(`Preloading component: ${componentName}`);
    // In real implementation, this would trigger the lazy component import
    this.loadedComponents.add(componentName);
  }
  
  /**
   * Get loading statistics
   */
  getStats() {
    return {
      loaded: this.loadedComponents.size,
      loading: this.loadingQueue.size,
      components: Array.from(this.loadedComponents),
    };
  }
  
  /**
   * Cleanup
   */
  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.loadingQueue.clear();
    this.loadedComponents.clear();
  }
}

/**
 * Global lazy loader instance
 */
export const lazyLoader = new IntersectionLazyLoader();

/**
 * Hook for using lazy loading in components
 */
export function useLazyLoading() {
  return {
    observe: lazyLoader.observe.bind(lazyLoader),
    unobserve: lazyLoader.unobserve.bind(lazyLoader),
    preload: lazyLoader.preload.bind(lazyLoader),
    getStats: lazyLoader.getStats.bind(lazyLoader),
  };
}

/**
 * Preload critical components based on user behavior
 */
export class SmartPreloader {
  private static preloadedRoutes: Set<string> = new Set();
  
  /**
   * Preload components based on current route
   */
  static async preloadForRoute(route: string) {
    if (this.preloadedRoutes.has(route)) {
      return;
    }
    
    console.log(`Smart preloading for route: ${route}`);
    
    switch (route) {
      case '/create':
      case '/create/customize':
        // Preload editor components
        await Promise.all([
          lazyLoader.preload('CustomizationEditor'),
          lazyLoader.preload('TemplateGallery'),
          lazyLoader.preload('DesignCanvas'),
        ]);
        break;
        
      case '/templates':
        await lazyLoader.preload('TemplateGallery');
        break;
        
      case '/admin':
        // Preload admin components after delay
        setTimeout(() => {
          lazyLoader.preload('PricingEngineAdmin');
          lazyLoader.preload('AiTryOnProductsTab');
        }, LAZY_CONFIG.PRELOAD_DELAY.ADMIN_TOOLS);
        break;
    }
    
    this.preloadedRoutes.add(route);
  }
  
  /**
   * Preload based on user interaction patterns
   */
  static async preloadOnHover(componentName: string) {
    // Preload component when user hovers over trigger element
    setTimeout(() => {
      lazyLoader.preload(componentName);
    }, 100); // Small delay to avoid unnecessary loads
  }
  
  /**
   * Preload based on viewport and connection
   */
  static async smartPreload() {
    // Check connection quality
    const connection = (navigator as any).connection;
    if (connection && connection.effectiveType === 'slow-2g') {
      console.log('Slow connection detected, skipping preload');
      return;
    }
    
    // Preload based on viewport size
    if (window.innerWidth >= 1024) {
      // Desktop - preload more aggressively
      await lazyLoader.preload('CustomizationEditor');
    }
  }
}
