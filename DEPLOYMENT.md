# Ottiq Docker Deployment Guide

This guide covers deploying <PERSON><PERSON><PERSON> using Docker for testing and production environments.

## Prerequisites

- Docker Desktop (Windows/Mac) or Docker Engine (Linux)
- Docker Compose v2.0+
- At least 4GB RAM available for containers
- 10GB free disk space

## Quick Start (Windows)

1. **<PERSON><PERSON> and navigate to the project:**
   ```powershell
   git clone <repository-url>
   cd ottiq
   ```

2. **Configure environment:**
   ```powershell
   # Copy the Docker environment template
   Copy-Item .env.docker .env.production
   
   # Edit .env.production with your actual values
   notepad .env.production
   ```

3. **Deploy with PowerShell:**
   ```powershell
   # Start all services
   .\deploy.ps1
   
   # Or with logs
   .\deploy.ps1 -Logs
   
   # Fresh deployment (removes all data)
   .\deploy.ps1 -Fresh
   ```

## Quick Start (Linux/Mac)

1. **<PERSON><PERSON> and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd ottiq
   ```

2. **Configure environment:**
   ```bash
   # Copy the Docker environment template
   cp .env.docker .env.production
   
   # Edit .env.production with your actual values
   nano .env.production
   ```

3. **Deploy with bash:**
   ```bash
   # Make script executable
   chmod +x deploy.sh
   
   # Start all services
   ./deploy.sh
   
   # Or with logs
   ./deploy.sh -l
   
   # Fresh deployment (removes all data)
   ./deploy.sh -f
   ```

## Manual Deployment

If you prefer manual control:

```bash
# Copy environment configuration
cp .env.docker .env.production

# Build and start all services
docker-compose -f docker-compose.prod.yml up -d --build

# Run database migrations
docker-compose -f docker-compose.prod.yml exec app npx prisma migrate deploy

# Seed the database (optional)
docker-compose -f docker-compose.prod.yml exec app npm run db:seed
```

## Service URLs

After deployment, access these services:

- **Main Application**: http://localhost
- **MinIO Console**: http://localhost:9001 (admin: ottiq_minio / ottiq_minio_password)
- **Mailhog UI**: http://localhost:8025 (email testing)
- **PostgreSQL**: localhost:5432 (ottiq_user / ottiq_password)
- **Redis**: localhost:6379

## Environment Configuration

Key environment variables to configure in `.env.production`:

### Required for Production
```env
NEXTAUTH_SECRET=your-secure-random-secret-key
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
HUGGING_FACE_API_KEY=your-hugging-face-api-key
```

### Optional but Recommended
```env
BKASH_APP_KEY=your-bkash-app-key
BKASH_APP_SECRET=your-bkash-app-secret
SENTRY_DSN=your-sentry-dsn
OPENAI_API_KEY=your-openai-api-key
```

## Management Commands

### Using PowerShell (Windows)
```powershell
# View service status
.\deploy.ps1 -Action status

# Stop all services
.\deploy.ps1 -Action stop

# Restart all services
.\deploy.ps1 -Action restart

# Clean deployment (removes all data)
.\deploy.ps1 -Action clean
```

### Using Bash (Linux/Mac)
```bash
# View service status
./deploy.sh -a status

# Stop all services
./deploy.sh -a stop

# Restart all services
./deploy.sh -a restart

# Clean deployment (removes all data)
./deploy.sh -a clean
```

### Manual Docker Commands
```bash
# View logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f app

# Execute commands in containers
docker-compose -f docker-compose.prod.yml exec app npm run db:studio
docker-compose -f docker-compose.prod.yml exec postgres psql -U ottiq_user -d ottiq_prod

# Scale services (if needed)
docker-compose -f docker-compose.prod.yml up -d --scale app=2
```

## Troubleshooting

### Services won't start
1. Check Docker is running: `docker ps`
2. Check logs: `docker-compose -f docker-compose.prod.yml logs`
3. Verify ports aren't in use: `netstat -an | findstr :80`

### Database connection issues
1. Wait for PostgreSQL to be ready (can take 30-60 seconds)
2. Check database logs: `docker-compose -f docker-compose.prod.yml logs postgres`
3. Verify connection: `docker-compose -f docker-compose.prod.yml exec postgres pg_isready`

### Application errors
1. Check application logs: `docker-compose -f docker-compose.prod.yml logs app`
2. Verify environment variables: `docker-compose -f docker-compose.prod.yml exec app env`
3. Run health check: `curl http://localhost/api/health`

### Performance issues
1. Increase Docker memory allocation (Docker Desktop settings)
2. Monitor resource usage: `docker stats`
3. Check disk space: `docker system df`

## Security Notes

- Change all default passwords in production
- Use strong, unique secrets for NEXTAUTH_SECRET and other keys
- Configure proper OAuth redirect URLs
- Set up SSL/TLS certificates for production domains
- Regularly update Docker images: `docker-compose pull`

## Backup and Recovery

### Backup
```bash
# Backup database
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U ottiq_user ottiq_prod > backup.sql

# Backup MinIO data
docker run --rm -v ottiq_minio_data:/data -v $(pwd):/backup alpine tar czf /backup/minio-backup.tar.gz /data
```

### Restore
```bash
# Restore database
docker-compose -f docker-compose.prod.yml exec -T postgres psql -U ottiq_user ottiq_prod < backup.sql

# Restore MinIO data
docker run --rm -v ottiq_minio_data:/data -v $(pwd):/backup alpine tar xzf /backup/minio-backup.tar.gz -C /
```

## Production Considerations

1. **SSL/TLS**: Configure nginx with proper SSL certificates
2. **Domain**: Update NEXTAUTH_URL and APP_URL with your domain
3. **Email**: Replace Mailhog with a real SMTP service
4. **Monitoring**: Set up proper logging and monitoring
5. **Backups**: Implement automated backup strategies
6. **Updates**: Plan for regular security updates
