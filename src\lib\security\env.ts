/**
 * Environment Variable Validation and Security Utilities for Ottiq
 * 
 * Provides comprehensive validation, sanitization, and security checks
 * for environment variables with type safety and runtime validation.
 */

import { z } from 'zod';

// Environment variable schemas
const DatabaseSchema = z.object({
  DATABASE_URL: z.string().url('Invalid database URL'),
});

const AuthSchema = z.object({
  NEXTAUTH_URL: z.string().url('Invalid NextAuth URL'),
  NEXTAUTH_SECRET: z.string().min(32, 'NextAuth secret must be at least 32 characters'),
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  FACEBOOK_CLIENT_ID: z.string().optional(),
  FACEBOOK_CLIENT_SECRET: z.string().optional(),
});

const AdminSchema = z.object({
  ADMIN_EMAILS: z.string().transform((val) => val.split(',').map(email => email.trim())),
});

const StorageSchema = z.object({
  MINIO_ENDPOINT: z.string().min(1, 'MinIO endpoint required'),
  MINIO_ACCESS_KEY: z.string().min(1, 'MinIO access key required'),
  MINIO_SECRET_KEY: z.string().min(8, 'MinIO secret key must be at least 8 characters'),
  MINIO_BUCKET_NAME: z.string().min(1, 'MinIO bucket name required'),
  MINIO_USE_SSL: z.string().transform((val) => val === 'true').default('false'),
});

const RedisSchema = z.object({
  REDIS_URL: z.string().url('Invalid Redis URL').optional(),
});

const PaymentSchema = z.object({
  BKASH_SANDBOX: z.string().transform((val) => val === 'true').default('true'),
  BKASH_BASE_URL: z.string().url('Invalid bKash base URL'),
  BKASH_APP_KEY: z.string().min(1, 'bKash app key required'),
  BKASH_APP_SECRET: z.string().min(1, 'bKash app secret required'),
  BKASH_USERNAME: z.string().min(1, 'bKash username required'),
  BKASH_PASSWORD: z.string().min(1, 'bKash password required'),
});

const AIServicesSchema = z.object({
  HUGGING_FACE_API_KEY: z.string().optional(),
  OPENAI_API_KEY: z.string().optional(),
  HUGGINGFACE_OOTD_SPACE_URL: z.string().url('Invalid Hugging Face space URL').optional(),
});

const SecuritySchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  APP_URL: z.string().url('Invalid app URL'),
  MAX_FILE_SIZE: z.string().transform((val) => parseInt(val, 10)).default('10485760'),
  ALLOWED_FILE_TYPES: z.string().default('image/jpeg,image/png,image/webp'),
  RATE_LIMIT_REQUESTS: z.string().transform((val) => parseInt(val, 10)).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform((val) => parseInt(val, 10)).default('900000'),
});

// Combined schema for all environment variables
const EnvironmentSchema = z.object({
  ...DatabaseSchema.shape,
  ...AuthSchema.shape,
  ...AdminSchema.shape,
  ...StorageSchema.shape,
  ...RedisSchema.shape,
  ...PaymentSchema.shape,
  ...AIServicesSchema.shape,
  ...SecuritySchema.shape,
});

export type EnvironmentConfig = z.infer<typeof EnvironmentSchema>;

/**
 * Validate environment variables
 */
export function validateEnvironment(): {
  success: boolean;
  config?: EnvironmentConfig;
  errors?: string[];
} {
  try {
    const config = EnvironmentSchema.parse(process.env);
    
    // Additional security validations
    const securityErrors = performSecurityValidations(config);
    
    if (securityErrors.length > 0) {
      return {
        success: false,
        errors: securityErrors,
      };
    }
    
    return {
      success: true,
      config,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      );
      return {
        success: false,
        errors,
      };
    }
    
    return {
      success: false,
      errors: ['Unknown validation error'],
    };
  }
}

/**
 * Perform additional security validations
 */
function performSecurityValidations(config: EnvironmentConfig): string[] {
  const errors: string[] = [];
  
  // Check for weak secrets in production
  if (config.NODE_ENV === 'production') {
    if (config.NEXTAUTH_SECRET.includes('your-secret-key-here')) {
      errors.push('NEXTAUTH_SECRET: Default secret detected in production');
    }
    
    if (config.MINIO_SECRET_KEY.length < 16) {
      errors.push('MINIO_SECRET_KEY: Should be at least 16 characters in production');
    }
    
    if (config.BKASH_SANDBOX) {
      errors.push('BKASH_SANDBOX: Should be false in production');
    }
    
    if (config.APP_URL.includes('localhost')) {
      errors.push('APP_URL: Should not use localhost in production');
    }
  }
  
  // Validate admin emails format
  try {
    config.ADMIN_EMAILS.forEach(email => {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        errors.push(`ADMIN_EMAILS: Invalid email format: ${email}`);
      }
    });
  } catch {
    errors.push('ADMIN_EMAILS: Failed to parse admin emails');
  }
  
  // Validate file size limits
  if (config.MAX_FILE_SIZE > 50 * 1024 * 1024) { // 50MB
    errors.push('MAX_FILE_SIZE: File size limit too high (max 50MB recommended)');
  }
  
  // Validate rate limiting
  if (config.RATE_LIMIT_REQUESTS > 1000) {
    errors.push('RATE_LIMIT_REQUESTS: Rate limit too high (max 1000 recommended)');
  }
  
  return errors;
}

/**
 * Get sanitized environment config (safe for logging)
 */
export function getSanitizedConfig(): Record<string, any> {
  const config = process.env;
  const sanitized: Record<string, any> = {};
  
  // List of sensitive keys to mask
  const sensitiveKeys = [
    'NEXTAUTH_SECRET',
    'DATABASE_URL',
    'GOOGLE_CLIENT_SECRET',
    'FACEBOOK_CLIENT_SECRET',
    'MINIO_SECRET_KEY',
    'BKASH_APP_SECRET',
    'BKASH_PASSWORD',
    'HUGGING_FACE_API_KEY',
    'OPENAI_API_KEY',
    'REDIS_URL',
  ];
  
  for (const [key, value] of Object.entries(config)) {
    if (sensitiveKeys.some(sensitive => key.includes(sensitive))) {
      sanitized[key] = maskSensitiveValue(value || '');
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

/**
 * Mask sensitive values for logging
 */
function maskSensitiveValue(value: string): string {
  if (value.length <= 4) {
    return '***';
  }
  
  const start = value.substring(0, 2);
  const end = value.substring(value.length - 2);
  const middle = '*'.repeat(Math.max(3, value.length - 4));
  
  return `${start}${middle}${end}`;
}

/**
 * Check if running in production
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Check if running in development
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Get required environment variable with validation
 */
export function getRequiredEnv(key: string): string {
  const value = process.env[key];
  
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  
  return value;
}

/**
 * Get optional environment variable with default
 */
export function getOptionalEnv(key: string, defaultValue: string): string {
  return process.env[key] || defaultValue;
}

/**
 * Get boolean environment variable
 */
export function getBooleanEnv(key: string, defaultValue: boolean = false): boolean {
  const value = process.env[key];
  
  if (!value) {
    return defaultValue;
  }
  
  return value.toLowerCase() === 'true';
}

/**
 * Get numeric environment variable
 */
export function getNumericEnv(key: string, defaultValue: number): number {
  const value = process.env[key];
  
  if (!value) {
    return defaultValue;
  }
  
  const parsed = parseInt(value, 10);
  
  if (isNaN(parsed)) {
    console.warn(`Invalid numeric value for ${key}: ${value}, using default: ${defaultValue}`);
    return defaultValue;
  }
  
  return parsed;
}

/**
 * Validate environment on startup
 */
export function initializeEnvironment(): void {
  console.log('Validating environment configuration...');
  
  const validation = validateEnvironment();
  
  if (!validation.success) {
    console.error('Environment validation failed:');
    validation.errors?.forEach(error => console.error(`  - ${error}`));
    
    if (isProduction()) {
      throw new Error('Environment validation failed in production');
    } else {
      console.warn('Environment validation failed in development - continuing with warnings');
    }
  } else {
    console.log('Environment validation successful');
    
    // Log sanitized config in development
    if (isDevelopment()) {
      console.log('Environment config:', getSanitizedConfig());
    }
  }
}

// Auto-initialize on import in production
if (isProduction()) {
  initializeEnvironment();
}

/**
 * Security utilities for input validation and sanitization
 */
export const SecurityUtils = {
  /**
   * Sanitize string input to prevent XSS
   */
  sanitizeString(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  },

  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate URL format
   */
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Generate secure random string
   */
  generateSecureToken(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  },

  /**
   * Hash sensitive data (for logging/debugging)
   */
  hashSensitiveData(data: string): string {
    // Simple hash for non-cryptographic purposes
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  },

  /**
   * Validate file type
   */
  isValidFileType(filename: string, allowedTypes: string[]): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return extension ? allowedTypes.includes(`.${extension}`) : false;
  },

  /**
   * Validate file size
   */
  isValidFileSize(size: number, maxSize: number): boolean {
    return size > 0 && size <= maxSize;
  },
};
