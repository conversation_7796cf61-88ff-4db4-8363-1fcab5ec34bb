#!/usr/bin/env node

/**
 * Support System Test Runner
 * Comprehensive test script for the customer support system
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Support System Tests\n');

const testSuites = [
  {
    name: 'Support API Tests',
    pattern: 'src/__tests__/api/support/**/*.test.ts',
    description: 'Testing support ticket and message API endpoints',
  },
  {
    name: 'Support Component Tests',
    pattern: 'src/__tests__/components/support/**/*.test.tsx',
    description: 'Testing support UI components and interactions',
  },
];

function runTestSuite(suite) {
  console.log(`\n📋 ${suite.name}`);
  console.log(`   ${suite.description}`);
  console.log('   ' + '─'.repeat(50));

  try {
    const command = `npx jest "${suite.pattern}" --verbose --coverage --collectCoverageFrom="src/app/api/support/**/*.ts" --collectCoverageFrom="src/components/support/**/*.tsx"`;
    
    console.log(`   Running: ${command}\n`);
    
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    
    console.log(`\n   ✅ ${suite.name} completed successfully`);
    return true;
  } catch (error) {
    console.error(`\n   ❌ ${suite.name} failed`);
    console.error(`   Error: ${error.message}`);
    return false;
  }
}

function runAllTests() {
  console.log('🚀 Running all support system tests...\n');
  
  try {
    const command = 'npx jest src/__tests__/api/support src/__tests__/components/support --coverage --verbose';
    
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    
    console.log('\n✅ All support system tests completed successfully!');
    return true;
  } catch (error) {
    console.error('\n❌ Some tests failed');
    console.error(`Error: ${error.message}`);
    return false;
  }
}

function runLinting() {
  console.log('\n🔍 Running ESLint on support system files...');
  
  try {
    const command = 'npx eslint src/app/api/support src/components/support src/__tests__/api/support src/__tests__/components/support --ext .ts,.tsx';
    
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    
    console.log('✅ Linting completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Linting failed');
    console.error(`Error: ${error.message}`);
    return false;
  }
}

function runTypeCheck() {
  console.log('\n🔧 Running TypeScript type checking...');
  
  try {
    const command = 'npx tsc --noEmit --project tsconfig.json';
    
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    
    console.log('✅ Type checking completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Type checking failed');
    console.error(`Error: ${error.message}`);
    return false;
  }
}

function generateCoverageReport() {
  console.log('\n📊 Generating coverage report...');
  
  try {
    const command = 'npx jest src/__tests__/api/support src/__tests__/components/support --coverage --coverageReporters=html --coverageReporters=text-summary';
    
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });
    
    console.log('✅ Coverage report generated in coverage/ directory');
    return true;
  } catch (error) {
    console.error('❌ Coverage report generation failed');
    console.error(`Error: ${error.message}`);
    return false;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node scripts/test-support-system.js [options]

Options:
  --all, -a          Run all tests
  --api             Run API tests only
  --components      Run component tests only
  --lint            Run linting only
  --type-check      Run type checking only
  --coverage        Generate coverage report
  --help, -h        Show this help message

Examples:
  node scripts/test-support-system.js --all
  node scripts/test-support-system.js --api
  node scripts/test-support-system.js --components --coverage
`);
    return;
  }

  let success = true;

  if (args.includes('--type-check')) {
    success = runTypeCheck() && success;
  }

  if (args.includes('--lint')) {
    success = runLinting() && success;
  }

  if (args.includes('--api')) {
    success = runTestSuite(testSuites[0]) && success;
  }

  if (args.includes('--components')) {
    success = runTestSuite(testSuites[1]) && success;
  }

  if (args.includes('--coverage')) {
    success = generateCoverageReport() && success;
  }

  if (args.includes('--all') || args.length === 0) {
    // Run type checking first
    success = runTypeCheck() && success;
    
    // Run linting
    success = runLinting() && success;
    
    // Run all tests
    success = runAllTests() && success;
  }

  console.log('\n' + '='.repeat(60));
  
  if (success) {
    console.log('🎉 All support system tests and checks passed!');
    console.log('\nNext steps:');
    console.log('1. Review the test coverage report');
    console.log('2. Run the application and test the support features manually');
    console.log('3. Deploy the support system to staging for integration testing');
    process.exit(0);
  } else {
    console.log('💥 Some tests or checks failed!');
    console.log('\nPlease fix the issues and run the tests again.');
    process.exit(1);
  }
}

main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
