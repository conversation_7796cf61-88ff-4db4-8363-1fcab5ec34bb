
import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { PrismaClient } from '@prisma/client';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import { emailService } from './services/email';

const prisma = new PrismaClient();

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user && token) {
        // Add user ID and role to session from token
        session.user.id = token.uid as string;
        session.user.role = token.role as 'admin' | 'user';
      }
      return session;
    },
    jwt: async ({ user, token }) => {
      if (user) {
        token.uid = user.id;

        // Check if user is admin
        const dbUser = await prisma.user.findUnique({
          where: { id: user.id },
          select: { email: true }
        });

        const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || [];
        token.role = adminEmails.includes(dbUser?.email || '') ? 'admin' : 'user';
      }
      return token;
    },
  },
  events: {
    createUser: async ({ user }) => {
      // Send welcome email to new users
      if (user.email && user.name) {
        try {
          const emailData = {
            to: user.email,
            customerName: user.name,
          };

          const result = await emailService.sendEmail('welcome', emailData, {
            priority: 'normal',
            delay: 2000, // Small delay to ensure user creation is complete
          });

          if (result.success) {
            console.log('Welcome email sent successfully:', {
              userId: user.id,
              email: user.email,
              messageId: result.messageId,
            });
          } else {
            console.error('Failed to send welcome email:', result.error);
          }
        } catch (error) {
          console.error('Error sending welcome email:', error);
        }
      }
    },
  },
  session: {
    strategy: 'database',
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
};

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role: 'admin' | 'user';
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    uid: string;
    role: 'admin' | 'user';
  }
}

