/**
 * Email Sending API Route
 * Handles sending various types of emails with validation and error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { emailService, EmailType, EmailData } from '../../../../lib/services/email';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// Request validation schema
const SendEmailSchema = z.object({
  type: z.enum([
    'order-confirmation',
    'order-shipped',
    'order-delivered',
    'order-cancelled',
    'payment-confirmation',
    'payment-failed',
    'ai-tryon-ready',
    'welcome',
    'password-reset',
  ]),
  data: z.object({
    to: z.string().email(),
    customerName: z.string().min(1),
  }).passthrough(), // Allow additional properties based on email type
  options: z.object({
    priority: z.enum(['high', 'normal', 'low']).optional(),
    delay: z.number().min(0).max(300000).optional(), // Max 5 minutes delay
  }).optional(),
});

// Admin-only email types that require authentication
const ADMIN_ONLY_TYPES: EmailType[] = [
  'order-confirmation',
  'order-shipped',
  'order-delivered',
  'order-cancelled',
  'payment-confirmation',
  'payment-failed',
];

/**
 * POST /api/emails/send - Send email
 */
export async function POST(request: NextRequest) {
  try {
    // Check if email service is available
    if (!emailService.isAvailable()) {
      return NextResponse.json(
        {
          success: false,
          error: 'EMAIL_SERVICE_UNAVAILABLE',
          message: 'Email service is not configured or unavailable',
        },
        { status: 503 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = SendEmailSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'INVALID_REQUEST',
          message: 'Invalid request data',
          details: validationResult.error.issues.map((issue) => ({
            field: issue.path.join('.'),
            message: issue.message,
          })),
        },
        { status: 400 }
      );
    }

    const { type, data, options } = validationResult.data;

    // Check authentication for admin-only email types
    if (ADMIN_ONLY_TYPES.includes(type as EmailType)) {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.email) {
        return NextResponse.json(
          {
            success: false,
            error: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required for this email type',
          },
          { status: 401 }
        );
      }

      // Check if user is admin
      const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
      if (!adminEmails.includes(session.user.email)) {
        return NextResponse.json(
          {
            success: false,
            error: 'ADMIN_ACCESS_REQUIRED',
            message: 'Admin access required for this email type',
          },
          { status: 403 }
        );
      }
    }

    // Validate email-specific data based on type
    const validatedEmailData = validateEmailData(type as EmailType, data);
    if (!validatedEmailData.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'INVALID_EMAIL_DATA',
          message: validatedEmailData.error,
        },
        { status: 400 }
      );
    }

    // Send email
    const result = await emailService.sendEmail(
      type as EmailType,
      validatedEmailData.data,
      options
    );

    if (!result.success) {
      console.error('Email sending failed:', result.error);
      return NextResponse.json(
        {
          success: false,
          error: 'EMAIL_SEND_FAILED',
          message: result.error || 'Failed to send email',
        },
        { status: 500 }
      );
    }

    // Log successful email send
    console.log('Email sent successfully:', {
      type,
      to: data.to,
      messageId: result.messageId,
    });

    return NextResponse.json({
      success: true,
      message: 'Email sent successfully',
      messageId: result.messageId,
    });

  } catch (error) {
    console.error('Email API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/emails/send - Get email service status
 */
export async function GET() {
  try {
    const isAvailable = emailService.isAvailable();
    const connectionValid = isAvailable ? await emailService.verifyConnection() : false;

    return NextResponse.json({
      success: true,
      status: {
        available: isAvailable,
        connectionValid,
        supportedTypes: [
          'order-confirmation',
          'order-shipped',
          'order-delivered',
          'order-cancelled',
          'payment-confirmation',
          'payment-failed',
          'ai-tryon-ready',
          'welcome',
          'password-reset',
        ],
      },
    });
  } catch (error) {
    console.error('Email status check error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'STATUS_CHECK_FAILED',
        message: 'Failed to check email service status',
      },
      { status: 500 }
    );
  }
}

/**
 * Validate email data based on email type
 */
function validateEmailData(type: EmailType, data: any): { success: boolean; data?: EmailData; error?: string } {
  try {
    switch (type) {
      case 'order-confirmation':
      case 'order-shipped':
      case 'order-delivered':
      case 'order-cancelled':
        const orderSchema = z.object({
          to: z.string().email(),
          customerName: z.string().min(1),
          orderNumber: z.string().min(1),
          orderDate: z.string(),
          totalAmount: z.string(),
          currency: z.string(),
          items: z.array(z.object({
            name: z.string(),
            quantity: z.number(),
            price: z.string(),
            customization: z.object({
              previewImage: z.string().optional(),
              name: z.string(),
            }).optional(),
          })),
          shippingAddress: z.object({
            name: z.string(),
            address: z.string(),
            city: z.string(),
            postalCode: z.string(),
            country: z.string(),
          }),
          trackingNumber: z.string().optional(),
          estimatedDelivery: z.string().optional(),
          orderUrl: z.string().url(),
        });
        
        const orderResult = orderSchema.safeParse(data);
        if (!orderResult.success) {
          return { success: false, error: `Invalid order email data: ${orderResult.error.message}` };
        }
        return { success: true, data: orderResult.data };

      case 'payment-confirmation':
      case 'payment-failed':
        const paymentSchema = z.object({
          to: z.string().email(),
          customerName: z.string().min(1),
          orderNumber: z.string().min(1),
          amount: z.string(),
          currency: z.string(),
          paymentMethod: z.string(),
          transactionId: z.string().optional(),
          orderUrl: z.string().url(),
        });
        
        const paymentResult = paymentSchema.safeParse(data);
        if (!paymentResult.success) {
          return { success: false, error: `Invalid payment email data: ${paymentResult.error.message}` };
        }
        return { success: true, data: paymentResult.data };

      case 'ai-tryon-ready':
        const aiTryOnSchema = z.object({
          to: z.string().email(),
          customerName: z.string().min(1),
          productName: z.string(),
          tryOnImageUrl: z.string().url(),
          customizationName: z.string(),
          orderUrl: z.string().url(),
        });
        
        const aiTryOnResult = aiTryOnSchema.safeParse(data);
        if (!aiTryOnResult.success) {
          return { success: false, error: `Invalid AI try-on email data: ${aiTryOnResult.error.message}` };
        }
        return { success: true, data: aiTryOnResult.data };

      case 'welcome':
        const welcomeSchema = z.object({
          to: z.string().email(),
          customerName: z.string().min(1),
        });
        
        const welcomeResult = welcomeSchema.safeParse(data);
        if (!welcomeResult.success) {
          return { success: false, error: `Invalid welcome email data: ${welcomeResult.error.message}` };
        }
        return { success: true, data: welcomeResult.data };

      case 'password-reset':
        const passwordResetSchema = z.object({
          to: z.string().email(),
          customerName: z.string().min(1),
          resetUrl: z.string().url(),
          expiresIn: z.string(),
        });
        
        const passwordResetResult = passwordResetSchema.safeParse(data);
        if (!passwordResetResult.success) {
          return { success: false, error: `Invalid password reset email data: ${passwordResetResult.error.message}` };
        }
        return { success: true, data: passwordResetResult.data };

      default:
        return { success: false, error: `Unsupported email type: ${type}` };
    }
  } catch (error) {
    return { success: false, error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
}
