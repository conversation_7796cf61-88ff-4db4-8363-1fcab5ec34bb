/**
 * Enhanced Caching System for Ottiq
 * 
 * Provides intelligent caching for heavy endpoints with Redis backend,
 * cache invalidation strategies, and performance monitoring for
 * instant visual feedback and emotional high user experience.
 */

import { RedisUtils } from '@/lib/redis';
import crypto from 'crypto';

// Cache configuration
export const CACHE_CONFIG = {
  // TTL settings (in seconds)
  TTL: {
    // API endpoints
    PRICING: 1800, // 30 minutes
    TEMPLATES: 3600, // 1 hour
    PRODUCTS: 7200, // 2 hours
    AI_TRYON_STATS: 300, // 5 minutes
    USER_PREFERENCES: 86400, // 24 hours
    
    // Image processing
    PREVIEW_IMAGES: 3600, // 1 hour
    THUMBNAILS: 86400, // 24 hours
    OPTIMIZED_IMAGES: 7200, // 2 hours
    
    // Heavy computations
    AREA_CALCULATIONS: 1800, // 30 minutes
    PRICING_CALCULATIONS: 900, // 15 minutes
    
    // Short-lived data
    RATE_LIMITS: 3600, // 1 hour
    SESSION_DATA: 1800, // 30 minutes
  },
  
  // Cache key prefixes
  PREFIXES: {
    API: 'api',
    IMAGE: 'img',
    PRICING: 'price',
    TEMPLATE: 'tmpl',
    USER: 'user',
    STATS: 'stats',
    COMPUTATION: 'comp',
  },
  
  // Performance settings
  MAX_KEY_LENGTH: 250,
  COMPRESSION_THRESHOLD: 1024, // Compress data larger than 1KB
} as const;

/**
 * Generate standardized cache key
 */
export function generateCacheKey(
  prefix: keyof typeof CACHE_CONFIG.PREFIXES,
  operation: string,
  params: Record<string, any> = {},
  userId?: string
): string {
  // Create deterministic hash of parameters
  const paramString = JSON.stringify(params, Object.keys(params).sort());
  const hash = crypto.createHash('md5').update(paramString).digest('hex').substring(0, 8);
  
  const keyParts = [
    CACHE_CONFIG.PREFIXES[prefix],
    operation,
    hash,
    ...(userId ? [userId.substring(0, 8)] : [])
  ];
  
  const key = keyParts.join(':');
  
  // Ensure key length doesn't exceed Redis limits
  if (key.length > CACHE_CONFIG.MAX_KEY_LENGTH) {
    const fullHash = crypto.createHash('md5').update(key).digest('hex');
    return `${CACHE_CONFIG.PREFIXES[prefix]}:${fullHash}`;
  }
  
  return key;
}

/**
 * Compress data if it exceeds threshold
 */
function shouldCompress(data: string): boolean {
  return data.length > CACHE_CONFIG.COMPRESSION_THRESHOLD;
}

/**
 * Cache wrapper with automatic serialization and compression
 */
export class CacheManager {
  /**
   * Set cache with automatic serialization
   */
  static async set<T>(
    key: string,
    data: T,
    ttl: number,
    options: {
      compress?: boolean;
      tags?: string[];
    } = {}
  ): Promise<boolean> {
    try {
      const serialized = JSON.stringify({
        data,
        timestamp: Date.now(),
        tags: options.tags || [],
        compressed: false,
      });
      
      // TODO: Add compression if needed in future
      // For now, keep it simple for performance
      
      return await RedisUtils.setex(key, ttl, serialized);
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Get cache with automatic deserialization
   */
  static async get<T>(key: string): Promise<{
    data: T;
    timestamp: number;
    age: number;
  } | null> {
    try {
      const cached = await RedisUtils.get(key);
      if (!cached) return null;
      
      const parsed = JSON.parse(cached);
      const age = Date.now() - parsed.timestamp;
      
      return {
        data: parsed.data,
        timestamp: parsed.timestamp,
        age,
      };
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Delete cache entry
   */
  static async delete(key: string): Promise<boolean> {
    return await RedisUtils.del(key);
  }

  /**
   * Check if cache exists
   */
  static async exists(key: string): Promise<boolean> {
    return await RedisUtils.exists(key);
  }

  /**
   * Get multiple cache entries
   */
  static async getMultiple<T>(keys: string[]): Promise<Array<T | null>> {
    try {
      const results = await RedisUtils.mget(keys);
      return results.map(result => {
        if (!result) return null;
        try {
          const parsed = JSON.parse(result);
          return parsed.data;
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('Cache getMultiple error:', error);
      return keys.map(() => null);
    }
  }

  /**
   * Set multiple cache entries
   */
  static async setMultiple<T>(
    entries: Array<{ key: string; data: T; ttl: number }>,
    options: { tags?: string[] } = {}
  ): Promise<boolean> {
    try {
      const keyValues: Record<string, string> = {};
      
      for (const entry of entries) {
        const serialized = JSON.stringify({
          data: entry.data,
          timestamp: Date.now(),
          tags: options.tags || [],
        });
        keyValues[entry.key] = serialized;
      }
      
      const success = await RedisUtils.mset(keyValues);
      
      // Set TTL for each key (Redis MSET doesn't support TTL)
      if (success) {
        await Promise.all(
          entries.map(entry => RedisUtils.expire(entry.key, entry.ttl))
        );
      }
      
      return success;
    } catch (error) {
      console.error('Cache setMultiple error:', error);
      return false;
    }
  }
}

/**
 * Cache decorator for API functions
 */
export function withCache<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: {
    keyGenerator: (...args: Parameters<T>) => string;
    ttl: number;
    skipCache?: (...args: Parameters<T>) => boolean;
  }
): T {
  return (async (...args: Parameters<T>) => {
    // Check if we should skip cache
    if (options.skipCache && options.skipCache(...args)) {
      return await fn(...args);
    }
    
    const cacheKey = options.keyGenerator(...args);
    
    // Try to get from cache
    const cached = await CacheManager.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for key: ${cacheKey} (age: ${cached.age}ms)`);
      return cached.data;
    }
    
    // Execute function and cache result
    const startTime = Date.now();
    const result = await fn(...args);
    const executionTime = Date.now() - startTime;
    
    // Cache the result
    await CacheManager.set(cacheKey, result, options.ttl);
    
    console.log(`Cache miss for key: ${cacheKey} (execution: ${executionTime}ms)`);
    return result;
  }) as T;
}

/**
 * Cache invalidation utilities
 */
export class CacheInvalidator {
  /**
   * Invalidate cache by pattern (requires Redis SCAN)
   */
  static async invalidateByPattern(pattern: string): Promise<number> {
    try {
      // This is a simplified version - in production you'd want to use Redis SCAN
      // For now, we'll track keys to invalidate manually
      console.log(`Cache invalidation requested for pattern: ${pattern}`);
      return 0;
    } catch (error) {
      console.error('Cache invalidation error:', error);
      return 0;
    }
  }

  /**
   * Invalidate user-specific cache
   */
  static async invalidateUserCache(userId: string): Promise<void> {
    const patterns = [
      `${CACHE_CONFIG.PREFIXES.USER}:*:${userId.substring(0, 8)}`,
      `${CACHE_CONFIG.PREFIXES.PRICING}:*:${userId.substring(0, 8)}`,
    ];
    
    await Promise.all(
      patterns.map(pattern => this.invalidateByPattern(pattern))
    );
  }

  /**
   * Invalidate template cache
   */
  static async invalidateTemplateCache(): Promise<void> {
    await this.invalidateByPattern(`${CACHE_CONFIG.PREFIXES.TEMPLATE}:*`);
  }

  /**
   * Invalidate pricing cache
   */
  static async invalidatePricingCache(): Promise<void> {
    await this.invalidateByPattern(`${CACHE_CONFIG.PREFIXES.PRICING}:*`);
  }
}

/**
 * Performance monitoring for cache operations
 */
export class CacheMetrics {
  private static metrics = {
    hits: 0,
    misses: 0,
    errors: 0,
    totalTime: 0,
  };

  static recordHit(time: number): void {
    this.metrics.hits++;
    this.metrics.totalTime += time;
  }

  static recordMiss(time: number): void {
    this.metrics.misses++;
    this.metrics.totalTime += time;
  }

  static recordError(): void {
    this.metrics.errors++;
  }

  static getStats(): {
    hitRate: number;
    totalOperations: number;
    averageTime: number;
    errors: number;
  } {
    const total = this.metrics.hits + this.metrics.misses;
    return {
      hitRate: total > 0 ? this.metrics.hits / total : 0,
      totalOperations: total,
      averageTime: total > 0 ? this.metrics.totalTime / total : 0,
      errors: this.metrics.errors,
    };
  }

  static reset(): void {
    this.metrics = { hits: 0, misses: 0, errors: 0, totalTime: 0 };
  }
}
