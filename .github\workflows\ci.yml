name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Lint and Type Check
  lint:
    name: Lint & Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run TypeScript type check
        run: npm run type-check

      - name: Check code formatting
        run: npm run format:check

  # Security Checks
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm run security:check

      - name: Run security tests
        run: npm run security:test
        env:
          NODE_ENV: test

  # Unit Tests
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  # Build Application
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [lint, security, test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma client
        run: npm run db:generate

      - name: Build application
        run: npm run build:prod
        env:
          NODE_ENV: production
          SKIP_ENV_VALIDATION: true

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: |
            .next/
            public/
          retention-days: 1

  # E2E Tests
  e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [build]
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        device: [desktop, mobile]
        exclude:
          # Skip webkit on mobile for faster CI (Safari mobile covered by webkit desktop)
          - browser: webkit
            device: mobile
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files

      - name: Install Playwright browsers
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Run E2E tests
        run: npx playwright test --project="${{ matrix.browser }}-${{ matrix.device }}"
        env:
          CI: true

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: e2e-results-${{ matrix.browser }}-${{ matrix.device }}
          path: |
            test-results/
            playwright-report/
          retention-days: 7

  # Database Tests
  database:
    name: Database Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ottiq_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run database tests
        run: npm run db:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ottiq_test

      - name: Validate database schema
        run: npm run db:validate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/ottiq_test

  # Deploy to Production (main branch only)
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [lint, security, test, build, e2e, database]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment:
      name: production
      url: https://ottiq.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files

      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production..."
          # Add your deployment script here
          # ./deploy.sh or similar
        env:
          NODE_ENV: production

      - name: Run post-deployment health checks
        run: |
          echo "🏥 Running health checks..."
          # Add health check commands here
          # curl -f https://ottiq.com/api/health || exit 1

      - name: Notify deployment success
        if: success()
        run: |
          echo "✅ Deployment successful!"
          # Add notification logic (Slack, Discord, etc.)

      - name: Notify deployment failure
        if: failure()
        run: |
          echo "❌ Deployment failed!"
          # Add failure notification logic
