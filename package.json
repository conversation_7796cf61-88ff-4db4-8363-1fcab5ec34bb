{"name": "ottiq", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON> — wear your imagination. An emotion-driven custom fashion platform.", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:prod": "NODE_ENV=production next build", "build:analyze": "ANALYZE=true npm run build:prod", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:security": "npm audit && npm run lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:security": "playwright test tests/security", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:test": "tsx scripts/test-db.ts", "db:validate": "tsx scripts/validate-schema.ts", "pricing:test": "tsx scripts/test-pricing.ts", "security:check": "npm audit --audit-level moderate", "security:fix": "npm audit fix", "security:test": "tsx scripts/security-test.ts", "validate:env": "tsx scripts/validate-env.ts", "email:test": "tsx scripts/test-email-service.ts", "email:validate": "tsx scripts/validate-email-templates.ts", "email:preview": "tsx scripts/test-email-service.ts preview", "prestart:prod": "npm run validate:env", "prebuild:prod": "npm run validate:env && npm run lint:security && npm run type-check", "postbuild:prod": "npm run security:test"}, "keywords": ["fashion", "custom", "ai", "nextjs", "typescript"], "author": "Ottiq Team", "license": "MIT", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@gradio/client": "^1.17.0", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.13.0", "@react-email/components": "^0.5.0", "@react-email/render": "^1.2.0", "@react-email/tailwind": "^1.2.2", "@types/nodemailer": "^6.4.17", "@use-gesture/react": "^10.3.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "konva": "^9.3.22", "lucide-react": "^0.539.0", "next": "^15.4.6", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.13.0", "rate-limiter-flexible": "^7.2.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-email": "^4.2.8", "react-konva": "^19.0.7", "redis": "^5.8.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@playwright/test": "^1.54.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "husky": "^9.1.7", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "lint-staged": "^16.1.5", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5.9.2", "webpack-bundle-analyzer": "^4.10.2"}}