#!/bin/bash

# Ottiq Database Restore Script
# Restores PostgreSQL database from backup files
# Usage: ./restore-database.sh <backup_file> [environment]
# Environment: dev, prod (default: prod)

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_FILE="${1:-}"
ENVIRONMENT="${2:-prod}"

# Validate arguments
if [[ -z "$BACKUP_FILE" ]]; then
    echo "Usage: $0 <backup_file> [environment]"
    echo "Example: $0 ottiq_db_prod_20241211_120000.sql.gz prod"
    exit 1
fi

# Load environment variables
if [[ "$ENVIRONMENT" == "prod" ]]; then
    ENV_FILE="$PROJECT_ROOT/.env.docker"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"
    DB_NAME="ottiq_prod"
else
    ENV_FILE="$PROJECT_ROOT/.env"
    COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"
    DB_NAME="ottiq_dev"
fi

# Source environment variables
if [[ -f "$ENV_FILE" ]]; then
    set -a
    source "$ENV_FILE"
    set +a
else
    echo "Error: Environment file $ENV_FILE not found"
    exit 1
fi

# Restore configuration
BACKUP_DIR="$PROJECT_ROOT/backups/database"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$BACKUP_DIR/restore_${TIMESTAMP}.log"
TEMP_DIR="/tmp/ottiq_restore_$$"

# Database connection settings
DB_USER="ottiq_user"
DB_PASSWORD="ottiq_password"
DB_HOST="localhost"
DB_PORT="5432"

# Create temp directory
mkdir -p "$TEMP_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
cleanup() {
    local exit_code=$?
    
    # Clean up temporary files
    rm -rf "$TEMP_DIR" 2>/dev/null || true
    
    if [[ $exit_code -ne 0 ]]; then
        log "ERROR: Database restore failed with exit code $exit_code"
        send_alert "Database restore failed for $ENVIRONMENT environment"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Function to send alerts
send_alert() {
    local message="$1"
    log "ALERT: $message"
    echo "RESTORE ALERT: $message" >> "$BACKUP_DIR/alerts.log"
}

# Function to check if Docker container is running
check_container() {
    local container_name="ottiq-postgres"
    if ! docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        log "ERROR: PostgreSQL container '$container_name' is not running"
        return 1
    fi
    return 0
}

# Function to test database connection
test_connection() {
    log "Testing database connection..."
    
    if docker exec ottiq-postgres pg_isready -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        log "Database connection successful"
        return 0
    else
        log "ERROR: Cannot connect to database"
        return 1
    fi
}

# Function to validate backup file
validate_backup_file() {
    local backup_path="$1"
    
    log "Validating backup file: $backup_path"
    
    # Check if file exists
    if [[ ! -f "$backup_path" ]]; then
        log "ERROR: Backup file not found: $backup_path"
        return 1
    fi
    
    # Check if file is not empty
    if [[ ! -s "$backup_path" ]]; then
        log "ERROR: Backup file is empty: $backup_path"
        return 1
    fi
    
    # Verify checksum if available
    local checksum_file="${backup_path}.sha256"
    if [[ -f "$checksum_file" ]]; then
        log "Verifying backup file checksum..."
        if cd "$(dirname "$backup_path")" && sha256sum -c "$(basename "$checksum_file")" > /dev/null 2>&1; then
            log "Backup file checksum verification passed"
        else
            log "ERROR: Backup file checksum verification failed"
            return 1
        fi
    else
        log "WARNING: No checksum file found, skipping verification"
    fi
    
    # Test archive integrity if it's compressed
    if [[ "$backup_path" == *.gz ]]; then
        log "Testing compressed backup file integrity..."
        if gzip -t "$backup_path" 2>/dev/null; then
            log "Compressed backup file integrity check passed"
        else
            log "ERROR: Compressed backup file is corrupted"
            return 1
        fi
    fi
    
    return 0
}

# Function to create database backup before restore
create_pre_restore_backup() {
    log "Creating pre-restore backup..."
    
    local pre_restore_backup="ottiq_db_${ENVIRONMENT}_pre_restore_${TIMESTAMP}.sql.gz"
    local pre_restore_path="$BACKUP_DIR/$pre_restore_backup"
    
    if docker exec ottiq-postgres pg_dump \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --format=custom \
        --compress=9 \
        --file="/tmp/pre_restore_backup.sql" 2>> "$LOG_FILE"; then
        
        # Copy and compress the backup
        docker cp "ottiq-postgres:/tmp/pre_restore_backup.sql" "$TEMP_DIR/pre_restore_backup.sql"
        gzip "$TEMP_DIR/pre_restore_backup.sql"
        mv "$TEMP_DIR/pre_restore_backup.sql.gz" "$pre_restore_path"
        
        # Clean up container temp file
        docker exec ottiq-postgres rm -f "/tmp/pre_restore_backup.sql" 2>/dev/null || true
        
        log "Pre-restore backup created: $pre_restore_backup"
        return 0
    else
        log "ERROR: Failed to create pre-restore backup"
        return 1
    fi
}

# Function to prepare backup file for restore
prepare_backup_file() {
    local backup_path="$1"
    local prepared_file="$TEMP_DIR/restore_backup.sql"
    
    log "Preparing backup file for restore..."
    
    if [[ "$backup_path" == *.gz ]]; then
        # Decompress the backup file
        log "Decompressing backup file..."
        if gunzip -c "$backup_path" > "$prepared_file"; then
            log "Backup file decompressed successfully"
        else
            log "ERROR: Failed to decompress backup file"
            return 1
        fi
    else
        # Copy the backup file
        cp "$backup_path" "$prepared_file"
    fi
    
    echo "$prepared_file"
}

# Function to restore database
restore_database() {
    local backup_path="$1"
    
    log "Starting database restore from: $backup_path"
    
    # Prepare backup file
    local prepared_file
    prepared_file=$(prepare_backup_file "$backup_path") || return 1
    
    # Copy prepared file to container
    log "Copying backup file to container..."
    docker cp "$prepared_file" "ottiq-postgres:/tmp/restore_backup.sql"
    
    # Drop existing connections to the database
    log "Terminating existing database connections..."
    docker exec ottiq-postgres psql -U "$DB_USER" -d postgres -c "
        SELECT pg_terminate_backend(pid) 
        FROM pg_stat_activity 
        WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();
    " 2>/dev/null || true
    
    # Drop and recreate database
    log "Dropping and recreating database..."
    docker exec ottiq-postgres psql -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>> "$LOG_FILE"
    docker exec ottiq-postgres psql -U "$DB_USER" -d postgres -c "CREATE DATABASE $DB_NAME;" 2>> "$LOG_FILE"
    
    # Restore database from backup
    log "Restoring database from backup..."
    if docker exec ottiq-postgres pg_restore \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --clean \
        --if-exists \
        --no-owner \
        --no-privileges \
        "/tmp/restore_backup.sql" 2>> "$LOG_FILE"; then
        
        log "Database restored successfully"
    else
        log "ERROR: Failed to restore database"
        return 1
    fi
    
    # Clean up container temp file
    docker exec ottiq-postgres rm -f "/tmp/restore_backup.sql" 2>/dev/null || true
    
    return 0
}

# Function to verify restore
verify_restore() {
    log "Verifying database restore..."
    
    # Test database connection
    if ! test_connection; then
        log "ERROR: Cannot connect to restored database"
        return 1
    fi
    
    # Check if tables exist
    local table_count=$(docker exec ottiq-postgres psql -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    " 2>/dev/null | xargs)
    
    log "Restored database contains $table_count tables"
    
    if [[ "$table_count" -gt 0 ]]; then
        log "Database restore verification passed"
        return 0
    else
        log "ERROR: Restored database appears to be empty"
        return 1
    fi
}

# Main execution
main() {
    log "=== Ottiq Database Restore Started ==="
    log "Environment: $ENVIRONMENT"
    log "Backup file: $BACKUP_FILE"
    log "Timestamp: $TIMESTAMP"
    
    # Resolve backup file path
    local backup_path
    if [[ "$BACKUP_FILE" == /* ]]; then
        # Absolute path
        backup_path="$BACKUP_FILE"
    elif [[ -f "$BACKUP_DIR/$BACKUP_FILE" ]]; then
        # Relative to backup directory
        backup_path="$BACKUP_DIR/$BACKUP_FILE"
    elif [[ -f "$BACKUP_FILE" ]]; then
        # Relative to current directory
        backup_path="$BACKUP_FILE"
    else
        log "ERROR: Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    # Pre-flight checks
    check_container || exit 1
    test_connection || exit 1
    validate_backup_file "$backup_path" || exit 1
    
    # Confirmation prompt
    echo ""
    echo "WARNING: This will completely replace the current database!"
    echo "Environment: $ENVIRONMENT"
    echo "Database: $DB_NAME"
    echo "Backup file: $backup_path"
    echo ""
    read -p "Are you sure you want to continue? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log "Restore cancelled by user"
        exit 0
    fi
    
    # Create pre-restore backup
    create_pre_restore_backup || exit 1
    
    # Perform restore
    restore_database "$backup_path" || exit 1
    
    # Verify restore
    verify_restore || exit 1
    
    log "=== Ottiq Database Restore Completed Successfully ==="
}

# Run main function
main "$@"
