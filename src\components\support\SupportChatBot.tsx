'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Input } from '@/components/ui';
import { useResponsive } from '@/hooks/useResponsive';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  type?: 'text' | 'options' | 'action';
  options?: Array<{
    label: string;
    action: string;
    value?: string;
  }>;
}

interface SupportChatBotProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTicket: () => void;
}

export function SupportChatBot({ isOpen, onClose, onCreateTicket }: SupportChatBotProps) {
  const { isMobile } = useResponsive();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        content: "Hi! I'm Ottiq's AI assistant. I'm here to help you with common questions and guide you to the right support resources. How can I help you today?",
        sender: 'bot',
        timestamp: new Date(),
        type: 'options',
        options: [
          { label: '📦 Order Status', action: 'order_status' },
          { label: '👕 Product Questions', action: 'product_help' },
          { label: '🔧 Technical Issues', action: 'technical_help' },
          { label: '💳 Billing Questions', action: 'billing_help' },
          { label: '🎫 Create Support Ticket', action: 'create_ticket' },
        ],
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, messages.length]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addMessage = (content: string, sender: 'user' | 'bot', type: 'text' | 'options' | 'action' = 'text', options?: any[]) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content,
      sender,
      timestamp: new Date(),
      type,
      options,
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const simulateTyping = async (duration: number = 1000) => {
    setIsTyping(true);
    await new Promise(resolve => setTimeout(resolve, duration));
    setIsTyping(false);
  };

  const handleOptionClick = async (action: string, value?: string) => {
    // Add user's choice as a message
    const optionLabel = messages[messages.length - 1]?.options?.find(opt => opt.action === action)?.label || value || action;
    addMessage(optionLabel, 'user');

    await simulateTyping();

    switch (action) {
      case 'order_status':
        addMessage(
          "I can help you check your order status! Here are a few ways to track your order:",
          'bot',
          'options',
          [
            { label: '📱 Check Order Dashboard', action: 'view_orders' },
            { label: '🔍 Search by Order Number', action: 'search_order' },
            { label: '📧 Resend Order Email', action: 'resend_email' },
            { label: '🎫 Contact Support', action: 'create_ticket' },
          ]
        );
        break;

      case 'product_help':
        addMessage(
          "I'd be happy to help with product questions! What would you like to know?",
          'bot',
          'options',
          [
            { label: '📏 Size Guide', action: 'size_guide' },
            { label: '🎨 Design Options', action: 'design_help' },
            { label: '🧵 Material Information', action: 'material_info' },
            { label: '💰 Pricing Questions', action: 'pricing_help' },
            { label: '🎫 Other Product Question', action: 'create_ticket' },
          ]
        );
        break;

      case 'technical_help':
        addMessage(
          "Let me help you with technical issues. What problem are you experiencing?",
          'bot',
          'options',
          [
            { label: '🖥️ Website Not Loading', action: 'website_issue' },
            { label: '🎨 Design Editor Problems', action: 'editor_issue' },
            { label: '📱 Mobile App Issues', action: 'mobile_issue' },
            { label: '🔐 Login Problems', action: 'login_issue' },
            { label: '🎫 Other Technical Issue', action: 'create_ticket' },
          ]
        );
        break;

      case 'billing_help':
        addMessage(
          "I can help with billing questions. What do you need assistance with?",
          'bot',
          'options',
          [
            { label: '💳 Payment Methods', action: 'payment_methods' },
            { label: '🧾 Invoice Questions', action: 'invoice_help' },
            { label: '💰 Refund Request', action: 'refund_help' },
            { label: '🔄 Subscription Issues', action: 'subscription_help' },
            { label: '🎫 Other Billing Question', action: 'create_ticket' },
          ]
        );
        break;

      case 'view_orders':
        addMessage(
          "You can view all your orders in your dashboard. Click the button below to go there now!",
          'bot',
          'action'
        );
        setTimeout(() => {
          window.open('/dashboard/orders', '_blank');
        }, 500);
        break;

      case 'size_guide':
        addMessage(
          "Our size guide helps you find the perfect fit! You can find detailed measurements for all our products in our help center.",
          'bot',
          'action'
        );
        setTimeout(() => {
          window.open('/help/size-guide', '_blank');
        }, 500);
        break;

      case 'website_issue':
        addMessage(
          "For website loading issues, try these quick fixes:\n\n1. Clear your browser cache and cookies\n2. Try a different browser or incognito mode\n3. Check your internet connection\n4. Disable browser extensions temporarily\n\nIf the problem persists, I'd recommend creating a support ticket with details about your browser and device.",
          'bot',
          'options',
          [
            { label: '✅ This Helped', action: 'problem_solved' },
            { label: '🎫 Still Need Help', action: 'create_ticket' },
          ]
        );
        break;

      case 'login_issue':
        addMessage(
          "For login problems, try these steps:\n\n1. Make sure you're using the correct email address\n2. Check if Caps Lock is on\n3. Try resetting your password\n4. Clear browser cookies for our site\n\nIf you're still having trouble, our support team can help you directly.",
          'bot',
          'options',
          [
            { label: '🔐 Reset Password', action: 'reset_password' },
            { label: '✅ This Helped', action: 'problem_solved' },
            { label: '🎫 Contact Support', action: 'create_ticket' },
          ]
        );
        break;

      case 'reset_password':
        addMessage(
          "I'll redirect you to the password reset page where you can enter your email address to receive reset instructions.",
          'bot',
          'action'
        );
        setTimeout(() => {
          window.open('/auth/forgot-password', '_blank');
        }, 500);
        break;

      case 'problem_solved':
        addMessage(
          "Great! I'm glad I could help. Is there anything else you need assistance with?",
          'bot',
          'options',
          [
            { label: '❓ Ask Another Question', action: 'restart' },
            { label: '👋 End Chat', action: 'end_chat' },
          ]
        );
        break;

      case 'create_ticket':
        addMessage(
          "I'll help you create a support ticket so our team can assist you directly. This will close the chat and open the ticket creation form.",
          'bot',
          'options',
          [
            { label: '🎫 Create Ticket Now', action: 'confirm_ticket' },
            { label: '🔙 Go Back', action: 'restart' },
          ]
        );
        break;

      case 'confirm_ticket':
        addMessage("Perfect! I'm opening the support ticket form for you now. Our team will get back to you soon!", 'bot');
        setTimeout(() => {
          onCreateTicket();
        }, 1000);
        break;

      case 'restart':
        setMessages([]);
        break;

      case 'end_chat':
        addMessage("Thanks for chatting with me! Feel free to reach out anytime you need help. Have a great day! 👋", 'bot');
        setTimeout(() => {
          onClose();
        }, 2000);
        break;

      default:
        addMessage(
          "I'm not sure how to help with that specific request, but our support team definitely can! Would you like me to help you create a support ticket?",
          'bot',
          'options',
          [
            { label: '🎫 Yes, Create Ticket', action: 'create_ticket' },
            { label: '🔙 Go Back to Menu', action: 'restart' },
          ]
        );
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim()) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    addMessage(userMessage, 'user');

    await simulateTyping();

    // Simple keyword-based responses
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('order') || lowerMessage.includes('shipping')) {
      handleOptionClick('order_status');
    } else if (lowerMessage.includes('size') || lowerMessage.includes('fit')) {
      handleOptionClick('size_guide');
    } else if (lowerMessage.includes('login') || lowerMessage.includes('password')) {
      handleOptionClick('login_issue');
    } else if (lowerMessage.includes('payment') || lowerMessage.includes('billing')) {
      handleOptionClick('billing_help');
    } else if (lowerMessage.includes('technical') || lowerMessage.includes('bug') || lowerMessage.includes('error')) {
      handleOptionClick('technical_help');
    } else {
      addMessage(
        "I understand you're asking about: \"" + userMessage + "\"\n\nI'd recommend creating a support ticket so our team can give you a detailed, personalized response. Would you like me to help you with that?",
        'bot',
        'options',
        [
          { label: '🎫 Create Support Ticket', action: 'create_ticket' },
          { label: '🔙 Back to Main Menu', action: 'restart' },
        ]
      );
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          className={`bg-white rounded-2xl shadow-2xl w-full max-w-md ${isMobile ? 'h-[90vh]' : 'h-[600px]'} flex flex-col overflow-hidden`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-warm-500 to-amber-500 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                🤖
              </div>
              <div>
                <h3 className="font-semibold">Ottiq AI Assistant</h3>
                <p className="text-xs text-white/80">Always here to help</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white text-xl"
            >
              ✕
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[80%] rounded-lg p-3 ${
                  message.sender === 'user'
                    ? 'bg-warm-500 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}>
                  <div className="whitespace-pre-wrap text-sm">
                    {message.content}
                  </div>
                  
                  {message.options && (
                    <div className="mt-3 space-y-2">
                      {message.options.map((option, index) => (
                        <button
                          key={index}
                          onClick={() => handleOptionClick(option.action, option.value)}
                          className="block w-full text-left px-3 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm border"
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-900 rounded-lg p-3">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="border-t p-4">
            <form onSubmit={handleSendMessage} className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type your question..."
                className="flex-1"
                disabled={isTyping}
              />
              <Button
                type="submit"
                disabled={!inputValue.trim() || isTyping}
                size="sm"
              >
                Send
              </Button>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
