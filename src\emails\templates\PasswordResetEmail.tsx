/**
 * Password Reset Email Template
 * Secure password reset with clear instructions
 */

import { Section, Text, Hr } from '@react-email/components';
import { EmailLayout, EmailButton, EmailHeading, EmailText } from '../components/EmailLayout';
import { PasswordResetEmailData } from '../../lib/services/email';

interface PasswordResetEmailProps extends PasswordResetEmailData {}

export function PasswordResetEmail({
  customerName,
  resetUrl,
  expiresIn,
}: PasswordResetEmailProps) {
  const baseUrl = process.env.APP_URL || 'https://ottiq.com';

  return (
    <EmailLayout previewText="Reset your Ottiq password securely with this link.">
      {/* Header */}
      <Section className="text-center mb-8">
        <EmailHeading level={1}>
          🔐 Reset Your Password
        </EmailHeading>
        
        <EmailText className="text-lg text-center">
          Hi {customerName}, we received a request to reset your Ottiq account password. Click the button below to create a new password.
        </EmailText>
      </Section>

      {/* Reset Button */}
      <Section className="bg-primary-50 rounded-lg p-6 mb-8 text-center">
        <EmailHeading level={2}>Reset Your Password</EmailHeading>
        <EmailText>
          Click the button below to securely reset your password. This link will expire in {expiresIn}.
        </EmailText>
        
        <EmailButton href={resetUrl} variant="primary">
          Reset Password
        </EmailButton>
      </Section>

      {/* Security Information */}
      <Section className="bg-blue-50 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>🛡️ Security Information</EmailHeading>
        
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">⏰</Text>
            <EmailText className="mb-2">
              <strong>Time Limit:</strong> This reset link expires in {expiresIn} for your security.
            </EmailText>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">🔒</Text>
            <EmailText className="mb-2">
              <strong>One-Time Use:</strong> This link can only be used once. After resetting, it becomes invalid.
            </EmailText>
          </div>

          <div className="flex items-start space-x-3">
            <Text className="text-blue-600 font-bold">🔐</Text>
            <EmailText className="mb-2">
              <strong>Secure Connection:</strong> The reset process uses encrypted connections to protect your information.
            </EmailText>
          </div>
        </div>
      </Section>

      {/* Manual Link */}
      <Section className="bg-gray-50 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>Button Not Working?</EmailHeading>
        <EmailText>
          If the button above doesn't work, copy and paste this link into your browser:
        </EmailText>
        
        <div className="bg-white rounded-lg p-4 border">
          <Text className="font-mono text-sm text-gray-700 break-all">
            {resetUrl}
          </Text>
        </div>
      </Section>

      {/* Didn't Request This? */}
      <Section className="bg-yellow-50 border-l-4 border-yellow-400 rounded-lg p-6 mb-8">
        <EmailHeading level={3}>Didn't Request This?</EmailHeading>
        <EmailText>
          If you didn't request a password reset, you can safely ignore this email. Your password will remain unchanged.
        </EmailText>
        
        <EmailText>
          However, if you're concerned about your account security, please contact our support team immediately.
        </EmailText>
      </Section>

      {/* Password Tips */}
      <Section className="mb-8">
        <EmailHeading level={2}>💡 Creating a Strong Password</EmailHeading>
        <EmailText>
          When creating your new password, make it strong and secure:
        </EmailText>
        
        <div className="space-y-2">
          <div className="flex items-center space-x-3">
            <Text className="text-green-600 font-bold">✓</Text>
            <Text className="text-gray-700 text-sm">At least 8 characters long</Text>
          </div>
          
          <div className="flex items-center space-x-3">
            <Text className="text-green-600 font-bold">✓</Text>
            <Text className="text-gray-700 text-sm">Mix of uppercase and lowercase letters</Text>
          </div>
          
          <div className="flex items-center space-x-3">
            <Text className="text-green-600 font-bold">✓</Text>
            <Text className="text-gray-700 text-sm">Include numbers and special characters</Text>
          </div>
          
          <div className="flex items-center space-x-3">
            <Text className="text-green-600 font-bold">✓</Text>
            <Text className="text-gray-700 text-sm">Avoid common words or personal information</Text>
          </div>
        </div>
      </Section>

      {/* Support */}
      <Section className="text-center mb-8">
        <EmailHeading level={3}>Need Help?</EmailHeading>
        <EmailText>
          If you're having trouble resetting your password or have any security concerns, our support team is here to help.
        </EmailText>
        
        <EmailButton href={`${baseUrl}/support`} variant="outline">
          Contact Support
        </EmailButton>
      </Section>

      {/* Footer */}
      <Section className="text-center">
        <Hr className="my-6" />
        <EmailText className="text-sm">
          This password reset was requested from IP address: [IP_ADDRESS] at [TIMESTAMP]
        </EmailText>
        
        <Text className="text-xs text-gray-500">
          For security reasons, we log password reset requests.
        </Text>
      </Section>
    </EmailLayout>
  );
}
